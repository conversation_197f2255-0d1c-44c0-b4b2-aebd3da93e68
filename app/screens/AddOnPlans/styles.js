import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet } = require("react-native");

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  renderItemMainView: {
    flex: 1,
    borderWidth: 2,
    borderColor: BaseColors.borderColor,
    backgroundColor: BaseColors.grayBackgroundColor,
    marginVertical: 6,
    borderRadius: 8,
    marginHorizontal: 20,
  },

  itemTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.lightBlack10,
    maxWidth: "97%",
  },
  summaryView: {
    marginTop: 32,
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: BaseColors.backgroundColor,
    padding: 10,
    borderBottomEndRadius: 8,
    borderBottomStartRadius: 8,
  },
  amountTextStyle: {
    fontFamily: FontFamily.RobotSemiBold,
    fontSize: 19,
    color: BaseColors.activeTab,
  },
  btnContainerView: {
    marginHorizontal: 20,
    marginBottom: 10,
  },
  isCheckStyle: {
    position: "absolute",
    bottom: -12,
    alignSelf: "center",
  },
  isCheckViewStyle: {
    height: 23,
    width: 23,
    backgroundColor: BaseColors.white,
    borderRadius: 23,
  },
  descRenderItemStyle: {
    flexDirection: "row",
    alignItems: "center",
    margin: 4,
    gap: 8,
  },
  couponView: {
    borderWidth: 0.5,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    borderColor: BaseColors.borderColor,
    backgroundColor: BaseColors.white,
  },
  couponText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.black,
    marginBottom: 10,
    opacity: 0.8,
  },
  currentPlanText: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 14,
    color: BaseColors.black100,
  },
  bottomView: {
    flexDirection: "row",

    marginBottom: 10,
    alignSelf: "center",
  },
  privacyPolicyTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: "#616161",
    // position: "absolute",
    // bottom: 30,
    alignSelf: "center",
  },
  priceView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  discountMainView: {
    borderWidth: 1,
    marginHorizontal: 20,
    borderRadius: 8,
    backgroundColor: "#f2e2bd",
    borderColor: BaseColors.activeTab,
    overflow: "hidden",
  },
  textView: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 12,
  },
  textView1: {
    marginVertical: 12,
  },
  discountTextView: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.activeTab,
  },
});

export default styles;
