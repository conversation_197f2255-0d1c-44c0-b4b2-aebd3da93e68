import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Platform,
} from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import { FontFamily } from "@config/typography";
import { BaseColors } from "@config/theme";
import CButton from "@components/CButton";
import { CustomIcon } from "@config/LoadIcons";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import { getAddOnPlans, oneTimePaymentFun, paymentAddOnsFunc, purchasePlanAddOnPlan } from "./apiFunction";
import { useFocusEffect } from "@react-navigation/native";
import Toast from "react-native-simple-toast";
import Loader from "@components/Loader";
import VerifyModal from "@components/VerifyModal";
import { getProducts } from "@app/utils/commonFunction";
import { isArray, isEmpty, isString } from "lodash-es";
import CInput from "@components/TextInput";
import { isValidCoupon, paySubscription } from "@screens/Payments/apiFunction";
import NoRecord from "@components/NoRecord";
import LottieView from "lottie-react-native";
import { images } from "@config/images";
import dayjs from "dayjs";
import OfferComponent from "@components/OfferComponent";
import * as RNIap from "react-native-iap";

const { setAddOnPlan } = authAction;
const AddOnPlans = ({ navigation, route }) => {
  // Getting Route Params
  const { type, data } = route?.params;

  // Redux Variable
  const dispatch = useDispatch();
  const { addOnPlan, userData, isCurrentPlan, adminSettingData, activePlanData } = useSelector(
    (s) => s.auth
  );
  const offerText = adminSettingData?.find((obj) => obj?.slug === "OFFERTEXT");
  // State's
  const [onClickItem, setOnClickItem] = useState(null);
  const [selectedItem, setSelectedItem] = useState({});
  const [isLoader, setIsLoader] = useState(false);
  const [btnLoader, setIsBtnLoader] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [isModal, setIsModal] = useState(false);
  const [btnText, setBtnText] = useState("");
  const [couponData, setCouponData] = useState(data ? data : {});
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const [applyCouponModal, setApplyCouponModal] = useState(false);
  // Memo's
  const onClickItemMemo = useMemo(() => onClickItem, [onClickItem]);
  const isLoaderMemo = useMemo(() => isLoader, [isLoader]);
  const selectedItemMemo = useMemo(() => selectedItem, [selectedItem]);
  const btnLoaderMemo = useMemo(() => btnLoader, [btnLoader]);

  useEffect(() => {
    const subscription = RNIap?.purchaseUpdatedListener(r => {
      if (Platform.OS === 'ios') {
        RNIap.finishTransaction({
          purchase: r,
          isConsumable: true,
          developerPayloadAndroid: '',
        })
          .then(async l => {
            const add_on_plan_id = type === "couponCode" ? selectedItem?.plan_id : selectedItemMemo?.add_on_plan_id;
            const plan_id = isCurrentPlan[0]?.plan_id || isCurrentPlan?.plan_id || activePlanData?.plan_id;
            oneTimePaymentFun(
              r,
              add_on_plan_id,
              plan_id,
              r?.productId,
              setIsBtnLoader,
              handleToPurchaseAddOnPlan,
              type,
              couponCode,
            );
          })
          .catch((error) => {
            console.log("🚀 ~ useEffect ~ error:", error)
          });
      }
    });

    const subscriptionError = RNIap?.purchaseErrorListener(error => {
      console.log('error----', error);
    });

    return () => {
      subscription?.remove();
      subscriptionError?.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedItem, selectedItemMemo, isCurrentPlan, activePlanData]);

  const getAddOnList = useCallback(async () => {
    setIsLoader(true);
    const resp = await getAddOnPlans();

    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(setAddOnPlan(resp?.data?.data));
      const getProductIdArr = resp?.data?.data.map((s) => {
        if (s?.product_id) {
          return s?.product_id;
        }
      });
      const finalPIdArr = getProductIdArr.filter((s) => s !== undefined);
      if (finalPIdArr && !isEmpty(finalPIdArr)) {
        await getProducts({ skus: finalPIdArr });
      }
      if (finalPIdArr && !isEmpty(finalPIdArr)) {
        await getProducts({ skus: finalPIdArr });
      }
      // const currentPlan = resp?.data?.data?.findIndex(
      //   (item) => item?.active_plan === true
      // );
      // const currentAddOn = resp?.data?.data?.filter(
      //   (item) => item?.active_plan === true
      // );
      setOnClickItem(0);
      setSelectedItem(resp?.data?.data[0]);
      setIsLoader(false);
    } else {
      Toast.show(
        resp?.data?.message || `Currently we don't have any add on plans`
      );
      setIsLoader(false);
    }
    setIsLoader(false);
  }, [isLoaderMemo]);

  const handleToPurchaseAddOn = useCallback(
    async (add_on_plan_id, product_id) => {
      setIsBtnLoader(true);
      // const resp = await purchasePlanAddOnPlan(id, isCurrentPlan[0]?.plan_id);
      // if (resp !== undefined && resp?.data?.success) {
      // setIsBtnLoader(false);
      if (product_id && isString(product_id)) {
        paymentAddOnsFunc(
          add_on_plan_id,
          isCurrentPlan[0]?.plan_id || isCurrentPlan?.plan_id || activePlanData?.plan_id,
          product_id,
          setIsBtnLoader,
          handleToPurchaseAddOnPlan,
          type,
          couponCode
        );
      } else {
        Toast.show("Product id is missing");
        setIsBtnLoader(false);
      }
      // } else {
      //   Toast.show(resp?.data?.message || "Something went wrong please try again");
      // setIsBtnLoader(false);
      // }
      // setIsBtnLoader(false);
    },
    [btnLoaderMemo, couponCode, type, activePlanData]
  );

  const handleToPurchaseAddOnPlan = async () => {
    // setIsBtnLoader(true);
    // const resp = await verifyAddOnPayment(planData);
    // if (resp !== undefined && resp?.data?.success) {
    // navigation.navigate("Home", {
    //   screen: "HomeTab",
    // });
    setIsModal(true);
    setIsBtnLoader(false);
    Toast.show("Payment Successful");
    // } else {
    //   Toast.show(resp?.data?.message || "Something went wrong please try again");
    //   setIsBtnLoader(false);
    // }
    setIsBtnLoader(false);
  };

  const getSpecialOffer = useCallback(async (id) => {
    await getProducts({ skus: [id] });
  });
  useFocusEffect(
    useCallback(() => {
      if (type !== "couponCode") {
        getAddOnList();
      } else {
        setOnClickItem(0);
        setCouponData(data);
        setSelectedItem(data);
        setCouponCode(data?.couponCode);
        if (!isEmpty(data?.product_id)) {
          getSpecialOffer(data?.product_id);
        }
        if (!isEmpty(data?.couponCode)) {
          setBtnText("Applied");
        }
      }
    }, [])
  );

  const renderDescItem = useCallback(
    ({ item }) => {
      return (
        <View style={styles.descRenderItemStyle}>
          <CustomIcon name="correct-icon" size={14} color={BaseColors.gray11} />
          <Text style={styles.itemTextStyle}>{item.title}</Text>
        </View>
      );
    },
    [onClickItemMemo]
  );
  const renderItem = useCallback(
    ({ item, index }) => {
      return (
        <TouchableOpacity
          style={[
            styles.renderItemMainView,
            {
              borderColor:
                index === onClickItemMemo
                  ? BaseColors.activeTab
                  : BaseColors.borderColor,
              marginBottom: 12,
            },
          ]}
          activeOpacity={0.8}
          onPress={() => {
            setOnClickItem(index);
            setSelectedItem(item);
          }}
        >
          <View style={{ paddingHorizontal: 10, paddingTop: 10 }}>
            {item?.features_list_preview?.map((item, index) => {
              return renderDescItem({ item, index });
            })}
          </View>
          <View style={styles.summaryView}>
            <Text
              style={[
                styles.itemTextStyle,
                {
                  fontFamily: FontFamily.RobotoRegular,
                  color: BaseColors.black,
                },
              ]}
            >
              {item.name}
            </Text>
            <Text style={styles.amountTextStyle}>
              ₹{Number(item?.price).toFixed(2)}
            </Text>
          </View>

          <View style={styles.isCheckStyle}>
            {index === onClickItemMemo ? (
              <View style={styles.isCheckViewStyle}>
                <CustomIcon
                  name="BsCheckCircleFill"
                  size={22}
                  color={BaseColors.activeTab}
                />
              </View>
            ) : null}
          </View>
        </TouchableOpacity>
      );
    },
    [onClickItemMemo, isLoaderMemo]
  );
  const onApplyCoupon = async () => {
    setIsBtnLoading(true);
    const resp = await isValidCoupon(couponCode);
    if (resp?.data?.success) {
      setCouponData(resp?.data?.data);
      setSelectedItem(resp?.data?.data);
      if (!isEmpty(data?.couponCode)) {
        setBtnText("Applied");
      }
      setApplyCouponModal(true);
      setIsBtnLoading(false);
    } else {
      Toast.show(resp?.data?.message || "Something went wrong");
      setIsBtnLoading(false);
      setCouponData({});
      setSelectedItem({});
    }
  };
  const noRecordView = () => {
    return <NoRecord title="Coupon not valid" />;
  };
  function capitalize(str) {
    if (!str) return ""; // Handle empty strings
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  const renderCouponCodeView = () => {
    return (
      <View style={[styles.couponCodeView, { flex: 1 }]}>
        <FlatList
          data={[couponData]}
          renderItem={({ item, index }) => {
            const features_list_preview = item?.features_list_preview && isArray(item?.features_list_preview) ? 
                  item?.features_list_preview : 
                  item?.features_list_preview && isString(item?.features_list_preview) ? JSON.parse(
                    item?.features_list_preview
                  ) : [];


            const planTypeLabel = (data) => {
              if (!data) return "-";

              const [days, other] = data.split("_");
              if (data?.includes("other") || data === '7') {
                return `${days} days `;
              } else {
                return `${capitalize(days)} ${capitalize(other)}`;
              }

              // if (includeOther) {
              //   return `${days} days` ?? "-";
              // }

              // return durationOptionsMap[data.duration] ?? "-";
            };
            return (
              <TouchableOpacity
                style={[
                  styles.renderItemMainView,
                  {
                    borderColor: null,
                    backgroundColor: null,
                    borderWidth: 0,
                    marginBottom: 12,
                  },
                ]}
                activeOpacity={0.8}
                onPress={() => {
                  setOnClickItem(index);
                  setSelectedItem(item);
                }}
              >
                <View style={{ paddingHorizontal: 10, paddingTop: 10 }}>
                  {features_list_preview?.map((item, index) => {
                    return renderDescItem({ item, index });
                  })}
                </View>
                <View style={styles.summaryView}>
                  <View style={{ flex: 1 }}>
                    <Text
                      style={[
                        styles.itemTextStyle,
                        {
                          fontFamily: FontFamily.RobotoRegular,
                          color: BaseColors.black,
                          marginBottom: 5
                        },
                      ]}
                    >
                      {planTypeLabel(item?.duration)}
                    </Text>
                    <Text
                      style={[
                        styles.itemTextStyle,
                        {
                          fontFamily: FontFamily.RobotoRegular,
                          color: BaseColors.black,
                          fontSize: 16,
                        },
                      ]}
                    >
                      {item.name}
                    </Text>
                  </View>
                  <View>
                    <Text
                      style={[
                        styles.amountTextStyle,
                        {
                          fontSize: 16,
                          textAlign: "right",
                          color: BaseColors.green,
                          marginBottom: 5
                        },
                      ]}
                    >
                      {`Discount ${item?.discount_value}%`}
                    </Text>
                    <View style={styles.priceView}>
                      <Text
                        style={[
                          styles.amountTextStyle,
                          {
                            textDecorationLine: item?.discount
                              ? "line-through"
                              : null,
                            fontSize: 16,
                            color: BaseColors.gray11,
                          },
                        ]}
                      >
                        ₹{Number(item?.price).toFixed(2)}
                      </Text>
                      {item?.discount ? (
                        <Text
                          style={[
                            styles.amountTextStyle,
                            { color: BaseColors.black },
                          ]}
                        >
                          ₹{Number(item?.final_price).toFixed(2)}
                        </Text>
                      ) : null}
                    </View>
                  </View>
                </View>

                <View style={styles.isCheckStyle}>
                  {index === onClickItemMemo ? (
                    <View style={styles.isCheckViewStyle}>
                      <CustomIcon
                        name="BsCheckCircleFill"
                        size={22}
                        color={BaseColors.activeTab}
                      />
                    </View>
                  ) : null}
                </View>
              </TouchableOpacity>
            );
          }}
          ListEmptyComponent={noRecordView}
        />
      </View>
    );
  };
  return (
    <SafeAreaView style={styles.mainView}>
      {/* For Header */}
      <CHeader
        headingTitle={
          type === "couponCode"
            ? translate("offerTitle")
            : translate("addOnPlans")
        }
        handleBackButton={() => navigation.goBack()}
      />

      <ScrollView>
        {couponData?.isSpecialOffer && (
          <>
            <OfferComponent specialOfferDataObj={couponData} handleToSpecialOffer={() => {}} />
          </>
        )}
        {/* Coupon View */}
        {type === "couponCode" && !data?.isSpecialOffer && (
          <View style={{ marginHorizontal: 20, marginVertical: 10 }}>
            <Text style={styles.couponText}>Coupon Code</Text>
            <View style={styles.couponView}>
              <View style={{ flex: 2 }}>
                <CInput
                  placeholderText="Enter Code"
                  containerStyle={{ height: 40, borderWidth: 0 }}
                  fontFamilyStyle={{ minHeight: 40 }}
                  value={couponCode}
                  onChange={(text) => setCouponCode(text)}
                />
              </View>

              <View style={{ flex: 1 }}>
                <CButton
                  containerStyle={{ height: 40, paddingVertical: 8 }}
                  onBtnClick={() => onApplyCoupon()}
                  loading={isBtnLoading}
                  disabled={isEmpty(couponCode)}
                >
                  {btnText}
                </CButton>
              </View>
            </View>
          </View>
        )}
        {/* List Out Add On Plans  */}

        {isLoaderMemo ? (
          <>
          <Loader loading={isLoaderMemo} />
          </>
        ) : type !== "couponCode" ? (
          <>
            <FlatList
              data={addOnPlan}
              renderItem={renderItem}
              keyExtractor={({ item, index }) => index}
            />

            
          </>
        ) : (
          renderCouponCodeView()
        )}
      </ScrollView>

      {type === "couponCode" ? (<>
      <View style={styles.bottomView}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate("CMS", {
                type: "privacy",
              })
            }
          >
            <Text style={styles.privacyPolicyTextStyle}>
              {"Subscriber Agreement"}
              {" • "}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate("CMS", {
                type: "terms",
              })
            }
          >
            <Text style={styles.privacyPolicyTextStyle}>
              {"FAQ"}
              {" • "}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate("CMS", {
                type: "privacy",
              })
            }
          >
            <Text style={styles.privacyPolicyTextStyle}>
              {translate("PrivacyAndPolicyText")}
            </Text>
          </TouchableOpacity>
        </View>
        <View style={{ marginBottom: 40 }}>
          <Text style={styles.privacyPolicyTextStyle}>
            {"in case of any query contact us "}
          </Text>
      </View></>) :null}

        {/* Pay Now Button */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginHorizontal: 20,
            marginBottom: 20,
          }}
        >
          <View style={{ alignItems: "center", flex: 1 }}>
            <Text style={styles.currentPlanText}>Amount to Pay</Text>
            <Text
              style={[
                styles.currentPlanText,
                {
                  fontSize: 18,
                  color: BaseColors.black,
                  fontFamily: FontFamily.RobotSemiBold,
                },
              ]}
            >
              ₹{Number(selectedItemMemo?.final_price || selectedItemMemo?.price || 0).toFixed(2)}
            </Text>
          </View>
          <View style={{ flex: 1 }}>
            <CButton
              showRightIcon={true}
              onBtnClick={() =>
                handleToPurchaseAddOn(
                  type === "couponCode" ? selectedItem?.plan_id : selectedItemMemo?.add_on_plan_id,
                  selectedItem?.product_id
                )
              }
              loading={btnLoaderMemo}
              disabled={isEmpty(selectedItem)}
            >
              Pay
            </CButton>
          </View>
        </View>
      {isModal ? (
        <VerifyModal
          visible={isModal}
          setModalVisible={(e) => {
            setIsModal(e);
          }}
          setIsModal={(e) => setIsModal(e)}
          type={"boost"}
          text={type ? "Your Plan is Successfully Purchased" :  "Your Add Ons is Successfully Purchased"}
          navigation={navigation}
        />
      ) : null}
      {applyCouponModal ? (
        <VerifyModal
          visible={applyCouponModal}
          setModalVisible={(e) => setApplyCouponModal(false)}
          setIsModal={(e) => setApplyCouponModal(false)}
          type={"addOns"}
          subTitle1={`'${couponCode}' Applied`}
          text={`${couponData?.discount_value}% Discount`}
          subTitle2={"with this coupon code"}
          navigation={navigation}
          data={couponData[0]}
        />
      ) : null}
    </SafeAreaView>
  );
};

export default AddOnPlans;
