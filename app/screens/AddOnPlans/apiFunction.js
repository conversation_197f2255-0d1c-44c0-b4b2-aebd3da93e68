import { getApiData } from "@app/utils/apiHelper";
import { purchaseProduct } from "@app/utils/commonFunction";
import { images } from "@config/images";
import BaseSetting from "@config/setting";
import { BaseColors } from "@config/theme";
import { paySubscription } from "@screens/Payments/apiFunction";
import { isEmpty } from "lodash-es";
import { Platform } from "react-native";
import Toast from "react-native-simple-toast";

export const getAddOnPlans = async () => {
  try {
    const resp = await getApiData(BaseSetting.endpoints.addOnPlan, "GET");
    return resp;
  } catch (error) {
    console.error("🚀 ~ getAddOnPlans ~ error:", error);
  }
};

export const purchasePlanAddOnPlan = async (
  data,
  setIsBtnLoader,
  handleToPurchaseAddOnPlan
) => {
  setIsBtnLoader(true);
  let params = {
    DEVICE: Platform.OS,
    plan_id: data?.plan_id,
    add_on_id: data?.add_on_id,
    product_id: data?.product_id,
    package_name: "com.footbizz",
    order_id: data?.order_id,
  };
  if (Platform.OS === "android") {
    params.purchase_time = data?.purchase_time;
    params.purchase_token = data?.purchase_token;
  }
  if (Platform.OS === "ios") {
    params.transaction_receipt = data?.transaction_receipt;
    params.transaction_id = data?.transaction_id;
    params.transaction_date = data?.transaction_date;
  }
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.purchaseAddOnPlan,
      "POST",
      params
    );

    if (resp !== undefined && resp?.data?.success) {
      setIsBtnLoader(false);
      handleToPurchaseAddOnPlan();
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsBtnLoader(false);
    }

    // return resp;
  } catch (error) {
    console.error("🚀 ~ purchasePlan ~ error:", error);
  }
};

// Open Razorpay modal for payment
export const paymentAddOnsFunc = async (
  add_on_plan_id,
  plan_id,
  product_id,
  setIsBtnLoader,
  handleToPurchaseAddOnPlan,
  type = 'add-ons',
  couponCode = ''
) => {
  try {
    setIsBtnLoader(true);
    await purchaseProduct(product_id)
    .then(async(r) => {
        if (r?.transactionReceipt && Platform.OS === 'android') {
          oneTimePaymentFun(
            r,
            add_on_plan_id,
            plan_id,
            product_id,
            setIsBtnLoader,
            handleToPurchaseAddOnPlan,
            type,
            couponCode,
          );
        } else {
          setIsBtnLoader(false);
        }
      })
      .catch((g) => {
        setIsBtnLoader(false);
        console.log("🚀 ~ purchaseProduct ~ g:", g);
      });
    // const options = {
    //   description: "Credits towards FootBizz",
    //   image:
    //     "https://admin.footbizz.in/static/media/small-logo.85a3359cd35634e423dc.png",
    //   currency: itemData?.currency,
    //   key: BaseSetting?.razorPayKey, // Your api key
    //   amount: Number(itemData?.amount_due) * 100,
    //   name: "FootBizz",
    //   modal: {
    //     backdropclose: true,
    //     escape: true,
    //     handleback: true,
    //     confirm_close: true,
    //     animation: true,
    //   },
    //   timeout: 180,
    //   prefill: {
    //     email: userData?.company_email, // Sender email
    //     contact: userData?.company_phone_number, // Sender contact number
    //     name: userData?.company_name, // Sender name
    //   },
    //   readonly: {
    //     contact: true,
    //     email: true,
    //     name: false,
    //   },
    //   theme: {
    //     color: BaseColors.activeTab,
    //   },
    //   send_sms_hash: true,
    // };
    // RazorpayCheckout.open(options)
    //   .then((data) => {
    //     if (data?.razorpay_payment_id) {
    //       handleToPurchaseAddOn({
    //         add_on_id: selectedItemMemo,
    //         razorpay_payment_id: data.razorpay_payment_id,
    //         purchase_master_id: purchase_master_id,
    //       });
    //     }
    //   })
    //   .catch((error) => {
    //     console.log("🚀 ~ error:", error);
    //     // handle failure
    //     Toast.show(
    //       error?.error?.description || "You may have cancelled the payment"
    //     );
    //   });
  } catch (error) {
    console.error("🚀 ~ purchasePlan ~ error:", error);
  }
};

export const verifyAddOnPayment = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.verifyAddOnPayment,
      "POST",
      data
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ verifyAddOnPayment ~ error:", error);
  }
};

export const oneTimePaymentFun = async(
  r,
  add_on_plan_id,
  plan_id,
  product_id,
  setIsBtnLoader,
  handleToPurchaseAddOnPlan,
  type,
  couponCode
) => {
  if(type === 'couponCode'){
    const data = {
      plan_id: add_on_plan_id,
      DEVICE: Platform.OS,
      product_id: product_id,
      package_name: "com.footbizz",
    };
    if (Platform.OS === "android" && !isEmpty(r)) {
      data.order_id = r?.transactionId;
      data.purchase_token = r?.purchaseToken;
      data.purchase_time = r?.transactionDate;
    }
    if (Platform.OS === "ios" && !isEmpty(r)) {
      data.order_id = r?.originalTransactionIdentifierIOS || r?.transactionId;
      data.transaction_receipt = r?.transactionReceipt;
      data.transaction_date = r?.transactionDate;
      data.transaction_id =
        r?.originalTransactionIdentifierIOS || r?.transactionId;
    }
    if(couponCode){
      data.coupon_code = couponCode
    }
    // Purchase api call for coupon and special offer plan
    const resp = await paySubscription(data);
    if (resp !== undefined && resp?.data?.success) {
      setIsBtnLoader(false);
      handleToPurchaseAddOnPlan();
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsBtnLoader(false);
    }
  }else{
    // Call when Add-ons purchase
    purchasePlanAddOnPlan(
      {
        product_id,
        purchase_time: r?.transactionDate,
        purchase_token: Platform.OS === "android" ? r?.purchaseToken : "",
        order_id: r?.transactionId,
        transaction_receipt:
          Platform.OS === "ios" ? r?.transactionReceipt : "",
        transaction_id: r?.transactionId,
        transaction_date: r?.transactionDate,
        plan_id,
        add_on_id: add_on_plan_id,
      },
      setIsBtnLoader,
      handleToPurchaseAddOnPlan
    );
  }
}
