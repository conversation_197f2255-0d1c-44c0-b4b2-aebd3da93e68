import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const onSubmit = async (data, signUpType, googleId, isEdit) => {
  const finalData = {
    full_name: data?.fullName?.trim(),
    username: data?.userName,
    phone_code: data?.countryCode,
    phone_number: data?.mobileNo,
    email: data?.email,
    gender: data?.gender || "preferNotToSay",
    type: signUpType,
  };

  // if (data?.gender) {
  //   finalData.gender = data?.gender;
  // }
  if (data?.dateOfBirth) {
    finalData.dob = data?.dateOfBirth;
  }
  if (signUpType === "social" && isEdit === false) {
    finalData.social_connection_id = googleId;
  }

  if (data?.userId) {
    finalData["user_id"] = data?.userId;
  }
  if (!data?.userId && signUpType !== "social") {
    finalData["password"] = data?.password;
  }
  if (data?.referralCode) {
    finalData["code"] = data?.referralCode;
  }
  if (data?.profileImage !== undefined) {
    finalData["profile_picture"] = data?.profileImage;
  }

  try {
    const resp = await getApiData(
      `${!data?.userId ? BaseSetting.endpoints.userProfile : BaseSetting.endpoints.UpdateUserData}`,
      "POST",
      finalData,
      true,
      true
    );

    return resp;
  } catch (error) {
    console.error("🚀 ~ onSubmit ~ error:", error);
  }
};

export const getData = async (data) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getUserData}${data}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

export const referralApiCall = async (referralCode) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.addReferralCode}?code=${referralCode}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ referralApiCall ~ error:", error);
  }
};
