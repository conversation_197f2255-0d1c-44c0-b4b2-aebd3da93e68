import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BaseColors.white,
  },
  KeyboardAvoidingViewStyle: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainFootLogo: {
    height: 120,
    width: 120,
    marginTop: Platform.OS === "ios" ? 0 : 0,
    alignSelf: "center",
  },
  headingText: {
    fontSize: 30,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontColor,
    textAlign: "center",
    marginTop: 25,
  },
  profileImgStyle: {
    height: 120,
    width: 120,
    borderRadius: 60,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginTop: 20,
  },
  profileImgMainView: {
    height: 122,
    width: 122,
    borderRadius: 60,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginTop: 20,
  },
  editIconStyle: {
    height: 24,
    width: 24,
  },
  editMainViewStyle: {
    position: "absolute",
    bottom: -9,
    right: 15,
  },
  profileImgMainViewStyle: {
    height: 140,
    width: 140,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
  },
  profileTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginTop: 12,
    color: "#555454",
  },
  commonView: {
    marginTop: 20,
  },
  formMainView: {
    marginHorizontal: 20,
  },
  buttonView: {
    marginHorizontal: 20,
    marginBottom: Platform.OS === "ios" ? 0 : 20,
  },
  errorTxt: {
    color: "#D6002E",
    textAlign: "left",
  },
  errorMsgMainView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 8,
    gap: 8,
  },
});
export default styles;
