import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { Keyboard } from "react-native";
import Toast from "react-native-simple-toast";

export const handleToSignIn = async (data) => {
  Keyboard.dismiss();
  const finalData = {
    type: data?.loginType,
  };
  if (data?.singUpType === "social") {
    finalData["idToken"] = data?.idToken;
    finalData["social_connection"] = data?.social_connection;
    if (data?.social_connection === "apple") {
      if (data?.tname !== undefined) {
        finalData["tname"] = data?.tname;
      }
    }
  } else if (data?.loginType === "phone") {
    finalData["phone_number"] = data?.mobileNo;
    finalData["phone_code"] = data?.countryCode;
    finalData["password"] = data?.password;
  } else {
    finalData["email"] = data?.email;
    finalData["password"] = data?.password;
  }

  try {
    const resp = await getApiData(
      BaseSetting.endpoints.login,
      "POST",
      finalData
    );

    return resp;
  } catch (error) {
    console.error("🚀 ~ handleToSignIn ~ error:", error);
  }
};
