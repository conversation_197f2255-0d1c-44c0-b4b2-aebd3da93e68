import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import {
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { BaseColors } from "@config/theme";
import CHeader from "@components/CHeader";
import CButton from "@components/CButton";
import CInput from "@components/TextInput";
import Toast from "react-native-simple-toast";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { handleToSignIn } from "./apiFunctions";
import {
  getMobileNumberLength,
  mobileValidation,
} from "@app/utils/commonFunction";
import { useDispatch, useSelector } from "react-redux";
import authAction from "../../redux/reducers/auth/actions";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Checkbox from "@components/Checkbox";
import BlockWarningModal from "@components/BlockWarningModal";
import { getSettingData } from "@screens/Home/apiCallFunction";
import { useFocusEffect } from "@react-navigation/native";
import { isEmpty } from "lodash-es";

const Animated = require("react-native-reanimated").default;
const Easing = require("react-native-reanimated").Easing;
const FadeIn = require("react-native-reanimated").FadeIn;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const { images } = require("@config/images");

const {
  setUserData,
  setCompanyId,
  setAccessToken,
  setIsCurrentPlan,
  setUserId,
  setAdminSettingData,
  setNotificationCount
} = authAction;
const LoginBasicSchemaWithEmail = yup.object().shape({
  email: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  password: yup
    .string()
    .required("enterYourPasswordErrMSg")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "passwordsErrMSg"
    ),
  loginType: yup.string().default("email"),
});
const LoginBasicSchemaWithMobileNo = yup.object().shape({
  mobileNo: yup
    .string()
    .required("enterMobileNumber")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  password: yup
    .string()
    .required("enterYourPasswordErrMSg")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "passwordsErrMSg"
    ),
  countryCode: yup.string().default("91"),
  loginType: yup.string().default("phone"),
});
const EmailWithLogin = ({ navigation, route }) => {
  const { adminSettingData } = useSelector((s) => s.auth);

  // Route Variable :
  const loginType = route?.params?.loginType;
  const dispatch = useDispatch();
  // Ref's
  const passwordText = useRef(null);
  const emailText = useRef(null);
  const mobileText = useRef(null);

  // State's
  const [mobileNoLength, setMobileLength] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const [isBlockUserModal, setIsBlockUserModal] = useState(false);
  const [isCheck, setIsCheck] = useState(false);
  const [countryCode, setCountryCode] = useState("+91");

  // Memo's
  const mobileNoLengthMemo = useMemo(() => mobileNoLength, [mobileNoLength]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const isCheckMemo = useMemo(() => isCheck, [isCheck]);

  // For Fetch Settings Data
  const fetchSettingData = async () => {
    const resp = await getSettingData();
    if (resp !== undefined) {
      let updatedData = resp?.map((val) => {
        return {
          slug: val.slug,
          value: val.value,
        };
      });
      if (!isEmpty(updatedData)) {
        dispatch(setAdminSettingData(updatedData));
      }
    }
  };

  // Form Variable
  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(
      loginType === "email"
        ? LoginBasicSchemaWithEmail
        : loginType === "mobile"
          ? LoginBasicSchemaWithMobileNo
          : null
    ),
  });

  useFocusEffect(
    useCallback(() => {
      fetchSettingData();
    }, [])
  );
  const returnResponse = useCallback(
    async (value) => {
      Keyboard.dismiss();
      setIsLoading(true);
      let isValid = true;

      if (loginType === "mobile") {
        const mobileNumberValidation = await mobileValidation(
          value?.mobileNo,
          mobileNoLengthMemo
        );
        if (!mobileNumberValidation?.isValid) {
          isValid = false;
          setError("mobileNo", {
            message: mobileNumberValidation?.validMessage,
          });
        } else {
          isValid = true;
        }
      } else {
        isValid = true;
      }

      if (isValid) {
        const resp = await handleToSignIn(value);
        if (resp?.data?.success && resp?.data?.data) {
          if (resp?.data?.data?.is_completed === 0) {
            dispatch(setCompanyId(resp?.data?.data?.company_id));
            dispatch(setUserId(resp?.data?.data?.user_id));
          }
          // navigation.navigate("SingUpWithFastRegistrationSecond", {
          //   user_id: resp?.data?.data?.user_id,
          //   company_id: resp?.data?.data?.company_id,
          // });
          // Toast.show(
          //   "Ohh, we can see here you are not full fill your company detail"
          // );
          // } else {
          dispatch(setUserData(resp?.data?.data));
          dispatch(setAccessToken(resp?.data?.token));
          if(resp?.data?.data?.notification_count){
            dispatch(setNotificationCount(Number(resp?.data?.data?.notification_count)));
          }
          if (resp?.data?.data?.active_plan?.length !== 0) {
            dispatch(setIsCurrentPlan(resp?.data?.data?.active_plan));
          }
          AsyncStorage.setItem("token", resp?.data?.token);
          navigation.replace("HomeScreen");
          // }
          setIsLoading(false);
        } else {
          // Toast.show(resp?.data?.message, Toast.LONG);

          if (loginType === "mobile" && resp?.data?.isExisting === false) {
            setError("mobileNo", { message: resp?.data?.message });
          } else if (resp?.data?.isExisting === false) {
            setError("email", { message: resp?.data?.message });
          } else if (resp?.data?.is_banned === true) {
            setIsBlockUserModal(true);
          } else {
            Toast.show(resp?.data?.message);
          }
          setIsLoading(false);
        }
      }

      setIsLoading(false);
    },
    [isLoadingMemo]
  );

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader handleBackButton={() => navigation.goBack()} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : null}
        style={{
          flex: 1,
          backgroundColor: BaseColors.white,
        }}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Animated.Image
            entering={FadeIn.duration(800).easing(Easing.ease)}
            source={images.footMainLogo}
            style={styles.mainFootLogo}
            resizeMode={"contain"}
          />
          <View style={styles.contentView}>
            <Text style={styles.headingText}>
              {translate("LetJoinYouInText")}
            </Text>
            <View style={styles.helloImagAndText}>
              <Text style={styles.descText}>
                {translate("WelcomeBackText")}
              </Text>
              <Image source={images.helloImg} style={styles.helloImg} />
            </View>

            <Text style={styles.descText}>
              {translate("youHaveBeenMissedText")}
            </Text>
            <View style={styles.cInputStyle}>
              {loginType === "email" ? (
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="email"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.inputFields}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={emailText}
                        placeholderText="Email"
                        rightIconSize={18}
                        returnKeyType="next"
                        value={value}
                        onChange={(e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                        }}
                        onSubmit={() => {
                          // passwordText.current.focus();
                          passwordText?.current?.focus();
                        }}
                        isError={errors?.email}
                        isErrorMsg={errors?.email?.message}
                        isCerBtnText={"Register"}
                        onCerBtnPress={() => {
                          navigation.replace("SignUpWithMobileAndEmail", {
                            signUpType: "email",
                          });
                        }}
                      />
                    </Animated.View>
                  )}
                />
              ) : loginType === "mobile" ? (
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="mobileNo"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.inputFields}
                      entering={FadeInDown.duration(400)}
                    >
                      <CInput
                        reference={mobileText}
                        placeholderText={translate("MobileNumberPlaceHolder")}
                        returnKeyType="next"
                        inputType="mobile"
                        keyBoardType="number-pad"
                        maxLength={mobileNoLengthMemo}
                        value={value}
                        onChange={(e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                        }}
                        onSubmit={() => {
                          passwordText.current.focus();
                        }}
                        isError={errors?.mobileNo}
                        isErrorMsg={errors?.mobileNo?.message}
                        selectedCountryCode={(e) => {
                          const country_length = getMobileNumberLength(
                            e?.country_code
                          );
                          setMobileLength(country_length);
                          setValue("mobileNo", undefined);
                          const yourData = e?.dial_code?.split("+");
                          setCountryCode(yourData);
                          setValue("countryCode", yourData[1]);
                        }}
                        countryCodeValue={
                          getValues("countryCode")
                            ? `+${getValues("countryCode")}`
                            : countryCode
                        }
                        isCerBtnText={"Register"}
                        onCerBtnPress={() => {
                          navigation.replace("SignUpWithMobileAndEmail", {
                            signUpType: "mobile",
                          });
                        }}
                      />
                    </Animated.View>
                  )}
                />
              ) : null}
              <View>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="password"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.inputFields}
                      entering={FadeInDown.duration(400)}
                    >
                      <CInput
                        reference={passwordText}
                        onSubmit={handleSubmit(handleToSignIn)}
                        placeholderText="Password"
                        rightIconSize={18}
                        returnKeyType="done"
                        value={value}
                        onChange={(e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                        }}
                        passwordInputField={true}
                        isError={errors?.password}
                        isErrorMsg={errors?.password?.message}
                      />
                    </Animated.View>
                  )}
                />
              </View>
            </View>
            <View style={styles.forgetView}>
              <TouchableOpacity
                style={{ position: "absolute", alignSelf: "flex-end" }}
                activeOpacity={0.8}
                onPress={() =>
                  navigation.navigate("CForgotPasswordEmail", {
                    loginType: loginType,
                  })
                }
              >
                <Text style={styles.forgetViewText}>
                  {translate("forgotPasswordText")}?
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <Animated.View style={styles.checkBoxMainView} entering={FadeIn}>
          <View style={styles.cmsMainView}>
            <Checkbox
              isChecked={isCheckMemo}
              toggleCheckbox={(e) => {
                setIsCheck(!isCheckMemo);
              }}
            />
            <View style={styles.checkboxConditionView}>
              <Text style={styles.continue}>
                <View>
                  <Text style={styles.and}>By clicking on you agree</Text>
                </View>
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate("CMS", {
                      type: "terms",
                    })
                  }
                >
                  <Text style={styles.term}> Terms & Conditions</Text>
                </TouchableOpacity>
                <View>
                  <Text style={styles.and}> and </Text>
                </View>
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate("CMS", {
                      type: "privacy",
                    })
                  }
                >
                  <Text style={styles.privacy}>Privacy Policy.</Text>
                </TouchableOpacity>
              </Text>
            </View>
          </View>
        </Animated.View>
        <Animated.View entering={FadeIn.duration(400).easing(Easing.ease)}>
          <CButton
            style={styles.btnStyle}
            onBtnClick={handleSubmit((s) => returnResponse(s))}
            loading={isLoadingMemo}
            disabled={!isCheckMemo}
          >
            {translate("singInText")}
          </CButton>
        </Animated.View>
      </KeyboardAvoidingView>
      {isBlockUserModal ? (
        <BlockWarningModal
          visible={isBlockUserModal}
          setModalVisible={(e) => setIsBlockUserModal(e)}
          text={"contactAdminTxt"}
          textTitle={
            adminSettingData?.find((obj) => obj?.slug === "CONTACTEMAIL")?.value
          }
        />
      ) : null}
    </SafeAreaView>
  );
};

export default memo(EmailWithLogin);
