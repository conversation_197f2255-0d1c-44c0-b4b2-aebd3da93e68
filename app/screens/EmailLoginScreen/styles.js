import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainFootLogo: {
    height: 120,
    width: 120,
    marginTop: Platform.OS === "ios" ? 0 : 0,
    alignSelf: "center",
  },
  headingText: {
    fontSize: 34,
    fontFamily: FontFamily.RobotSemiBold,
    color: "#343434",
    textAlign: "center",
  },
  contentView: {
    marginHorizontal: 25,
    marginTop: 49,
  },
  descText: {
    fontSize: 24,
    fontFamily: FontFamily.RobotoRegular,
    color: "#343434",
    marginBottom: 5,
    textAlign: "center",
  },
  cInputStyle: {
    marginTop: 20,
  },
  btnStyle: {
    marginHorizontal: 25,
    marginBottom: 20,
    marginTop: 20,
  },

  forgetViewText: {
    color: "#000000",
    fontSize: 15,
  },
  helloImg: {
    height: 30,
    width: 30,
  },
  helloImagAndText: {
    flexDirection: "row",
    marginTop: 15,
    justifyContent: "center",
  },
  inputFields: {
    marginBottom: 15,
  },
  checkBoxMainView: {
    marginHorizontal: 20,
  },
  // buttonMainView: {
  //   marginHorizontal: 20,
  //   marginTop: 20,
  //   marginBottom: Platform.OS === "ios" ? 0 : 20,
  // },
  cmsMainView: {
    flexDirection: "row",
    // alignItems: "center",
    marginHorizontal: 10,

    justifyContent: "center",
  },
  forgetView: {
    marginBottom: 20,
  },
  and: {
    fontSize: 14,
    fontWeight: "500",
    letterSpacing: 0.8,
    color: BaseColors.gray2,
    fontFamily: FontFamily.Barlow,
  },
  term: {
    color: BaseColors.primary3,
    textDecorationLine: "underline",
    textTransform: "capitalize",
    fontSize: 14,
    fontWeight: "500",
  },
  privacy: {
    color: BaseColors.primary3,
    textDecorationLine: "underline",
    fontSize: 14,
    fontWeight: "500",
    textTransform: "capitalize",
  },
});

export default styles;
