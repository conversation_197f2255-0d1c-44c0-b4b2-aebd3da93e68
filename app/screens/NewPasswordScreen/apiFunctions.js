import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";

export const onsubmit = async (data, routeParams, navigation) => {
  const finalData = {
    type: routeParams?.type,
    password: data?.new_password,
  };
  if (routeParams?.type !== "email") {
    finalData["phone_code"] = routeParams?.phone_code;
    finalData["phone_number"] = routeParams?.phone_number;
  } else {
    finalData["email"] = routeParams?.email;
  }
  if (data?.new_password === data?.reEnter_password) {
    try {
      const resp = await getApiData(
        BaseSetting.endpoints.updatePass,
        "POST",
        finalData,
        false,
        false
      );

      return resp;
    } catch (error) {
      console.error("🚀 ~ onsubmit ~ error:", error);
    }
  } else {
    Toast.show("Password does not match");
  }
};
