import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet, useWindowDimensions } from "react-native";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  itemContainer: {
    alignItems: "center",
  },
  itemTitle: {
    fontSize: 24,
    lineHeight: 30,
    letterSpacing: 1,
    fontFamily: FontFamily.OutFitSemiBold,
    color: BaseColors.black,
    textAlign: "center",
    marginBottom: 10,
    marginTop: 20,
  },
  itemText: {
    textAlign: "center",
    marginHorizontal: 35,
    color: "black",
    lineHeight: 20,
  },
  bottomMainViewStyle: {
    position: "absolute",
    bottom: 30,
    alignSelf: "center",
    width: "100%",
    marginHorizontal: 30,
  },
  btnForGuest: {
    alignItems: "center",
  },
  btnText: {
    fontWeight: "500",
    fontSize: 16,
    lineHeight: 21,
    color: "#004872",
    letterSpacing: 1,
  },

  joiText: {
    fontFamily: FontFamily.semibold,
  },

  headingImg: {
    alignItems: "center",
  },
  logoImg: { height: 47, width: 190 },
  paginationView: { paddingVertical: 25 },
  buttonView: {
    width: "90%",
    marginHorizontal: 30,
    alignSelf: "center",
  },
  paginationContainer: {
    flexDirection: "row",
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  dots: {
    backgroundColor: BaseColors.primary,
    marginHorizontal: 4,
    borderRadius: 6,
  },
  descTextStyle: {
    fontSize: 16,
    color: "#263238",
    letterSpacing: 1,
    textAlign: "center",
    marginBottom: 10,
    fontFamily: FontFamily.RobotoRegular,
    paddingHorizontal: 20,
  },
});

export default styles;
