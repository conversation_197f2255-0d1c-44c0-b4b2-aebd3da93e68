import { View } from "react-native";
import React, { memo } from "react";
// import Animated, {
//   useAnimatedStyle,
//   interpolate,
//   Extrapolate,
//   interpolateColor,
// } from "react-native-reanimated";
import styles from "./styles";

const Animated = require("react-native-reanimated").default;
const useAnimatedStyle = require("react-native-reanimated").useAnimatedStyle;
const interpolate = require("react-native-reanimated").interpolate;
const Extrapolate = require("react-native-reanimated").Extrapolate;
const interpolateColor = require("react-native-reanimated").interpolateColor;

const Pagination = ({ data, x, screenWidth }) => {
  // eslint-disable-next-line react/no-unstable-nested-components
  const PaginationComp = ({ i }) => {
    const animatedDotStyle = useAnimatedStyle(() => {
      const widthAnimation = interpolate(
        x.value,
        [(i - 1) * screenWidth, i * screenWidth, (i + 1) * screenWidth],
        [8, 20, 8],
        Extrapolate.CLAMP
      );
      const heightAnimation = interpolate(
        x.value,
        [(i - 1) * screenWidth, i * screenWidth, (i + 1) * screenWidth],
        [8, 8, 8],
        Extrapolate.CLAMP
      );
      const colorAnimation = interpolateColor(
        x.value,
        [(i - 1) * screenWidth, i * screenWidth, (i + 1) * screenWidth],
        ["rgba(0, 0, 0, 0.25)", "#D6002E", "rgba(0, 0, 0, 0.25)"] // Change these color values to your desired colors
      );

      return {
        width: widthAnimation,
        height: heightAnimation,
        // opacity: opacityAnimation,
        backgroundColor: colorAnimation,
      };
    });
    return <Animated.View style={[styles.dots, animatedDotStyle]} />;
  };

  return (
    <View style={styles.paginationContainer}>
      {data.map((_, i) => {
        return <PaginationComp i={i} key={i} />;
      })}
    </View>
  );
};

export default memo(Pagination);
