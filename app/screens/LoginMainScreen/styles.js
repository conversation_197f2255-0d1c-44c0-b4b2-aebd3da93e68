import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainFootLogo: {
    height: 122,
    width: 123,
    marginTop: Platform.OS === "ios" ? 30 : 80,
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
  },
  footImageStyle: { height: 120, width: 120 },
  headingText: {
    fontSize: 34,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
    marginTop: 35,

    textAlign: "center",
  },
  renderItemMainView: {
    flexDirection: "row",
    borderWidth: 1,
    marginVertical: 6,
    marginHorizontal: 20,
    padding: 12,
    borderRadius: 5,
    borderColor: BaseColors.gray,
  },
  iconStyle: {
    height: 20,
    width: 20,
  },
  itemText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: "#434242",
    marginLeft: 9,
  },
  flatListStyle: {
    marginTop: 20,
  },
  separatorStyle: {
    borderBottomWidth: 2,
    width: "42%",
    marginHorizontal: 4,
    borderColor: BaseColors.lightGray,
    // borderColor: BaseColors.black,
  },
  separatorText: {
    fontSize: 14,

    fontFamily: FontFamily.OutFit,
    color: BaseColors.textLightGray,
  },
  separatorMainView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  alreadyAccountStyle: {
    marginTop: 26,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
  },
  alreadyAccountText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: "#434242",
  },
  signInButton: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.activeTab,
  },
  privacyPolicyTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: "#616161",
    // position: "absolute",
    // bottom: 30,
    alignSelf: "center",
  },
  bottomView: {
    flexDirection: "row",
    position: "absolute",
    bottom: 30,
    alignSelf: "center",
  },
});

export default styles;
