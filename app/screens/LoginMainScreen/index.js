import React, { memo, useCallback, useMemo, useState } from "react";
import {
  FlatList,
  Platform,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { RenderItem } from "./apiFunctions";
import authAction from "@redux/reducers/auth/actions";
import { useDispatch } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { hasAndroidPermission } from "@screens/AddStoryScreen/functions";
import Loader from "@components/Loader";

const Animated = require("react-native-reanimated").default;
const Easing = require("react-native-reanimated").Easing;
const FadeIn = require("react-native-reanimated").FadeIn;
const { images } = require("@config/images");
const { LoginListArr, signUpListArr } = require("@config/staticData");
const FastImage = require("react-native-fast-image");

const { setCompanyId, setUserId, logOut } = authAction;

const LoginMainScreen = ({ navigation }) => {
  // setUserData
  const dispatch = useDispatch();
  // Android Data for log in type list
  const androidLoginData = useMemo(
    () => LoginListArr.filter((item) => item.id !== 2),
    []
  );
  const androidSignUpData = useMemo(
    () => signUpListArr.filter((item) => item.id !== 2),
    []
  );

  // Sign up or signIn data
  const [isSignIn, setIsSignIn] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const isSignInMemo = useMemo(() => isSignIn, [isSignIn]);
  // RenderItem Data
  const renderItem = useCallback(
    ({ item, index }) => {
      return (
        <RenderItem
          item={item}
          navigation={navigation}
          index={index}
          isSignIn={isSignInMemo}
          dispatch={dispatch}
          setIsLoading={setIsLoading}
        />
      );
    },
    [isSignInMemo]
  );

  useFocusEffect(
    useCallback(() => {
      hasAndroidPermission();
      dispatch(setCompanyId(null));
      dispatch(setUserId(null));
      dispatch(logOut());

      return () => hasAndroidPermission();
    }, [])
  );
  // Flat List Data
  const data = useMemo(
    () =>
      isSignInMemo
        ? Platform.OS === "ios"
          ? LoginListArr
          : androidLoginData
        : Platform.OS === "ios"
          ? signUpListArr
          : androidSignUpData,
    [isSignInMemo, androidLoginData, androidSignUpData]
  );

  // Main return Function
  return (
    <>
      {isLoading && <Loader />}
      <SafeAreaView style={styles.mainView}>
        <Animated.View
          entering={FadeIn.duration(600).easing(Easing.ease)}
          style={styles.mainFootLogo}
          resizeMode={"contain"}
        >
          <FastImage
            source={images.footMainLogo}
            resizeMode="contain"
            style={styles.footImageStyle}
          />
        </Animated.View>
        <Animated.Text
          style={styles.headingText}
          entering={FadeIn.duration(600).easing(Easing.ease)}
        >
          {translate("letStartBtnText")}
        </Animated.Text>
        <View>
          <FlatList
            data={data}
            renderItem={renderItem}
            style={styles.flatListStyle}
            keyExtractor={(item, index) => index.toString()}
            initialNumToRender={10}
            getItemLayout={(data, index) => ({
              length: 50,
              offset: 50 * index,
              index,
            })}
            scrollEnabled={false}
          />
        </View>
        <Animated.View
          style={styles.separatorMainView}
          entering={FadeIn.duration(600).easing(Easing.ease)}
        >
          <View style={styles.separatorStyle} />
          <Text style={styles.separatorText}>{translate("orText")}</Text>
          <View style={styles.separatorStyle} />
        </Animated.View>
        <View style={styles.alreadyAccountStyle}>
          <Text style={styles.alreadyAccountText}>
            {isSignInMemo
              ? `${translate("DonAccountText")}?`
              : translate("alreadyAccountText")}
          </Text>
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => setIsSignIn(!isSignInMemo)}
          >
            <Text style={styles.signInButton}>
              {isSignInMemo ? translate("signUpText") : translate("singInText")}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomView}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate("CMS", {
                type: "privacy",
              })
            }
          >
            <Text style={styles.privacyPolicyTextStyle}>
              {translate("PrivacyAndPolicyText")}
              {" . "}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate("CMS", {
                type: "terms",
              })
            }
          >
            <Text style={styles.privacyPolicyTextStyle}>
              {translate("TermsOfServiceText")}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </>
  );
};
export default memo(LoginMainScreen);
