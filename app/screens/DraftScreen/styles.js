import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

const HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  postListMain: {
    flex: 1,
  },
  saveCounter: {
    marginHorizontal: 20,
    flex: 1,
  },
  addReelIcon: {
    paddingRight: 5,
    paddingTop: 5,
    position: "absolute",
    right: -15,
    top: -15,
  },
  mainButtonView: {
    flexDirection: "row",
    gap: 10,
    justifyContent: "center",
  },
  buttonView: {
    width: "50%",
  },
  buttonCStyle: {
    height: 40,
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  btnText: {
    fontSize: 16,
    fontFamily: FontFamily.InterMedium,
  },
  mainPostView: {
    marginTop: 20,
  },
  imageContainer: {
    height: HEIGHT / 5,
    width: "31.4%",
    borderRadius: 10,
    marginRight: 10,
    marginBottom: 10,
    overflow: "hidden",
    elevation: 6,
    position: "relative",
  },
  image: {
    width: "100%",
    height: HEIGHT / 5,
  },
  centerMain: {
    height: Dimensions.get("window").height / 1.3,
  },
  reelAnimView: {
    height: 60,
    width: 60,
  },
});
export default styles;
