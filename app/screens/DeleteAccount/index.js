import React, { memo } from "react";
import { SafeAreaView, View } from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import Loader from "@components/CLoaderLottie";

const DeleteAccount = ({ navigation }) => {
  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="deleteAccount"
      />
      <View
        style={{
          flex: 1,
          paddingHorizontal: 14,
        }}
      >
        <Loader />
      </View>
    </SafeAreaView>
  );
};
export default memo(DeleteAccount);
