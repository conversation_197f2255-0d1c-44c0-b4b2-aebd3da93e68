import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  centerMain: {
    flex: 1,
    paddingHorizontal: 14,
    height: Dimensions.get("window").height - 200,
  },
  listItemText: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 18,
    color: BaseColors.black,
    paddingHorizontal: 20,
    marginVertical: 10,
  },
});
export default styles;
