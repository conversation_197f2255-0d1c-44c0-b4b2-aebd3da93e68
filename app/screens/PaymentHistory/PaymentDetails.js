import React, { memo } from "react";
import { FlatList, SafeAreaView, Text, View } from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import NoRecord from "@components/NoRecord";
import PaymentsListRenderItem from "@components/PaymentCollapse/PaymentCollapse";
import { isEmpty } from "lodash-es";

const PaymentDetails = ({ navigation, route }) => {
  const paymentList = route?.params?.paymentList || [];
  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="paymentDetails"
      />

      <FlatList
        data={[paymentList] || []}
        renderItem={(item, index) => {
          console.log("🚀 ~ PaymentDetails ~ item:", item?.item?.add_on_data);
          return (
            <>
              <PaymentsListRenderItem item={item?.item} index={0} />
              {!isEmpty(item?.item?.add_on_data) ? (
                <View>
                  <Text style={styles.listItemText}>
                    Add on Payment History
                  </Text>
                  <FlatList
                    data={item?.item?.add_on_data || []}
                    renderItem={(item2, index) => {
                      return <PaymentsListRenderItem item={item2?.item} />;
                    }}
                    keyExtractor={(item, index) => index?.toString()}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              ) : null}
            </>
          );
        }}
        keyExtractor={(item, index) => index?.toString()}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.centerMain}>
            <NoRecord title={"noRecordFound"} />
          </View>
        }
      />
    </SafeAreaView>
  );
};
export default memo(PaymentDetails);
