import React, { memo, useCallback, useState } from "react";
import {
  FlatList,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  View,
} from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { getPaymentHistory } from "./apiFunction";
import Toast from "react-native-simple-toast";
import { isEmpty } from "lodash-es";
import { useFocusEffect } from "@react-navigation/native";
import PaymentsListRenderItem from "@components/PaymentCollapse/PaymentCollapse";
import NoRecord from "@components/NoRecord";
import { BaseColors } from "@config/theme";
import MiniLoader from "@components/MiniLoader";
import AuthActions from "@redux/reducers/auth/actions";
import { useDispatch, useSelector } from "react-redux";

const { setPaymentHistoryList } = AuthActions;

const PaymentHistory = ({ navigation }) => {
  const dispatch = useDispatch();

  const { paymentHistoryList } = useSelector((auth) => auth.auth);

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [bottomLoading, setBottomLoading] = useState(false);

  useFocusEffect(
    useCallback(() => {
      apiFuncPaymentHistory();
    }, [])
  );

  const apiFuncPaymentHistory = async (page = 1, bottomLoader = false) => {
    if (isEmpty(paymentHistoryList?.data)) {
      setLoading(true);
    }
    if (bottomLoader) {
      setBottomLoading(true);
    }

    const resp = await getPaymentHistory(page);
    if (!isEmpty(resp?.data?.data) && resp?.data?.data !== undefined) {
      dispatch(
        setPaymentHistoryList({
          page: page,
          next_enable: resp?.data?.pagination?.hasNextPage,
          data:
            page > 1
              ? [...paymentHistoryList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      setLoading(false);
      setBottomLoading(false);
    } else {
      dispatch(setPaymentHistoryList([]));
      setLoading(false);
      setBottomLoading(false);
      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    apiFuncPaymentHistory();
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom && paymentHistoryList?.next_enable) {
      apiFuncPaymentHistory(paymentHistoryList?.page + 1, true);
    }
  };

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="paymentHistory"
      />

      <ScrollView
        onScroll={handleScroll}
        scrollEventThrottle={0.5}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.activeTab]} // Customize refresh indicator color
            tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {loading ? (
          <MiniLoader size="medium" />
        ) : (
          <View style={{ flex: 1 }}>
            <FlatList
              data={paymentHistoryList?.data || []}
              renderItem={(item) => {
                return (
                  <PaymentsListRenderItem
                    navigation={navigation}
                    item={item?.item}
                    type="list"
                  />
                );
              }}
              keyExtractor={(item, index) => index?.toString()}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.centerMain}>
                  <NoRecord title={"noRecordFound"} />
                </View>
              }
            />
            {bottomLoading ? (
              <View>
                <MiniLoader size="small" />
              </View>
            ) : null}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};
export default memo(PaymentHistory);
