import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const getPaymentHistory = async (page) => {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.paymentHistory}?pageSize=10&timezone=${timezone}&page=${page}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.error("error:", error);
  }
};
