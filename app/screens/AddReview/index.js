import React, { useMemo, useState } from "react";
import { Dimensions, ScrollView, Text, View } from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { AirbnbRating } from "react-native-ratings";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import Toast from "react-native-simple-toast";
import { sendFeedBack } from "./apiFunction";
import VerifyModal from "@components/VerifyModal";
import { useSelector } from "react-redux";
import LottieView from "lottie-react-native";
import { images } from "@config/images";
import { FontFamily } from "@config/typography";

const AddReview = ({ navigation }) => {
  const [isShareFeedbackText, setIsShareFeedbackText] = useState("");
  const [rating, setRating] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isModal, setisModal] = useState(false);

  const { appReviewCount } = useSelector((s) => s.auth);
  const isShareFeedbackTextMemo = useMemo(
    () => isShareFeedbackText,
    [isShareFeedbackText]
  );
  const ratingMemo = useMemo(() => rating, [rating]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const isModalMemo = useMemo(() => isModal, [isModal]);

  const handleToPressBtn = async () => {
    setIsLoading(true);
    const finalData = {
      rates: ratingMemo,
      messages: isShareFeedbackTextMemo,
    };

    const resp = await sendFeedBack(finalData);
    if (resp?.data?.success) {
      setisModal(true);
      setRating(null);
      setIsShareFeedbackText("");
    } else {
      setIsLoading(false);
      setRating(null);
      setIsShareFeedbackText("");
      Toast.show(resp?.data?.message || "Something went wrong");
      navigation.reset({
        index: 0,
        routes: [{ name: "HomeScreen" }],
      });
    }
    setIsLoading(false);
  };

  const formContent = () => {
    return (
      <ScrollView>
        {/* For Render Main Content */}
        <View style={styles.secondMainView}>
          {/* For render Rating Star */}
          <View style={styles.ratingView}>
            {/* For Render Rating Component */}
            <AirbnbRating
              count={5}
              defaultRating={0}
              size={40}
              showRating={false}
              selectedColor={BaseColors.activeTab}
              onFinishRating={setRating}
              ratingContainerStyle={styles.ratingReviewView}
            />
          </View>
          {/* For render Rating Star */}
          <View style={styles.ratingView}>
            <Text style={styles.normalTextStyle}>
              {translate("ratingMoreAboutQueText")}
            </Text>
            {/* For Render Rating Component */}
            <View style={styles.moreAboutTextInputStyle}>
              <CInput
                returnKeyType="done"
                value={isShareFeedbackTextMemo}
                onChange={setIsShareFeedbackText}
                onSubmit={() => {
                  Keyboard.dismiss();
                }}
                multiline
                numberOfLines={5}
              />
            </View>
          </View>
          <CButton
            disabled={ratingMemo === null}
            onBtnClick={() => handleToPressBtn()}
            loading={isLoadingMemo}
          >
            SUBMIT
          </CButton>
        </View>
      </ScrollView>
    );
  };
  const alreadyDoneConten = () => {
    return (
      <View
        style={{
          alignItems: "center",
          justifyContent: "center",
          marginHorizontal: 20,

          height: Dimensions.get("screen").height / 1.3,
        }}
      >
        <LottieView
          autoSize={true}
          source={images.reviewComplete}
          autoPlay={true}
          loop={true}
          style={{
            width: Dimensions.get("screen").width - 100,
            height: Dimensions.get("screen").width - 100,
          }}
        />
        <Text
          style={{
            fontSize: 25,
            textAlign: "center",
            color: BaseColors.black100,
            fontFamily: FontFamily.OutFitMedium,
          }}
        >
          Thank you
        </Text>
        <Text
          style={{
            fontSize: 25,
            textAlign: "center",
            color: BaseColors.black100,
            fontFamily: FontFamily.OutFitMedium,
          }}
        >
          You have already give us your feedback
        </Text>
      </View>
    );
  };
  return (
    <View style={styles.mainView}>
      {/* For Render Header */}
      <CHeader
        headingTitle="Add Review"
        handleBackButton={() => navigation.goBack()}
      />
      {appReviewCount >= 3 ? alreadyDoneConten() : formContent()}

      {isModalMemo ? (
        <VerifyModal
          visible={isModalMemo || false}
          setModalVisible={(e) => {
            setisModal(e);
          }}
          setIsModal={(e) => setisModal(e)}
          text={"Thank you \n Rated Successfully"}
          navigation={navigation}
        />
      ) : null}
    </View>
  );
};

export default AddReview;
