import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.White,
  },
  headingText: {
    fontSize: 30,
    fontFamily: FontFamily.OutFitSemiBold,
    color: BaseColors.black,
    marginTop: 20,
    marginBottom: 10,
  },
  secondMainView: {
    marginHorizontal: 20,
  },
  normalTextStyle: {
    fontSize: 20,
    color: BaseColors.black,
    fontFamily: FontFamily.OutFitRegular,
  },
  ratingView: {
    marginVertical: 20,
  },
  ratingReviewView: {
    marginTop: 18,
  },
  moreAboutTextInputStyle: {
    marginTop: 20,
  },
});

export default styles;
