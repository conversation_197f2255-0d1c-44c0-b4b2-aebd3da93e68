import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import isEmpty from "lodash-es/isEmpty";
import Toast from "react-native-simple-toast";

export const handleToSetIsSeen = async (data) => {
  const resp = await getApiData(
    BaseSetting.endpoints.userViewStory,
    "POST",
    data,
    false,
    false
  );

  return resp;
};

// Fetch setting Data from Admin
export const getSettingData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=settings`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(resp?.data?.message);
      }
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

export const getPostList = async (setLoading, page) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.postList}?pageSize=14&page=${page}`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
        return resp;
      } else {
        Toast.show(resp?.data?.message || "Not Data Found");
        setLoading(false);
      }
    }
  } catch (error) {
    setLoading(false);
    console.error("error ~", error);
  }
};

// like button API in save Screen
export const ClickedLikeButton = async (PostId, PostType) => {
  try {
    const resp = await getApiData(
      PostType === "reel"
        ? `${BaseSetting.endpoints.reelsLikeDislike}/${PostId}`
        : `${BaseSetting.endpoints.likeDislike}${PostId}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};

// Save button API in save Screen
export const ClickedSaveButton = async (PostId, postType) => {
  try {
    const resp = await getApiData(
      postType === "reel"
        ? `${BaseSetting.endpoints.saveUnSavedReel}/${PostId}`
        : `${BaseSetting.endpoints.saveUnsavedPost}${PostId}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};

// Function for Navigate to Reels screen for Selected video.
export const onReelsPressFunc = (
  navigation,
  item,
  dispatch,
  setReelsList,
  reelsList
) => {
  if (item?.type === "reel") {
    dispatch(setReelsList({ ...reelsList, data: [item] }));
    navigation.navigate("Reel", {
      screen: "reels",
      params: {
        type: "selectedReel",
      },
    });
  }
};

export const getSpecialOffer = async () => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.getSpecialOffer,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getSpecialOffer ~ error:", error);
  }
};
