import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor:
      Platform.OS === "ios" ? BaseColors.white : BaseColors.white,
    marginBottom: 15,
  },
  centerMain: {
    flex: 1,
    paddingHorizontal: 14,
    height: Dimensions.get("window").height / 1.5 - 85,
  },
  postListMain: {
    marginBottom: 65,
  },
  trailMainView: {
    borderWidth: 1,
    marginHorizontal: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BaseColors.white,
    padding: 10,
    borderRadius: 8,
    borderColor: BaseColors.activeTab,
    marginVertical: 4,
    marginBottom: 20,
  },
  trailTextStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 14,
    color: BaseColors.activeTab,
  },
  discountMainView: {
    borderWidth: 1,
    marginHorizontal: 20,
    borderRadius: 8,
    marginBottom: 20,
    borderColor: BaseColors.activeTab,
    overflow: "hidden",
  },
  textView: {
    flexDirection: "row",
    position: "absolute",
    justifyContent: "space-between",
    width: "96%",
    left: 8,
  },

  textView1: {
    marginVertical: 12,
  },
  textViewNew: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 12,
  },
  discountMainViewNew: {
    borderWidth: 1,
    marginHorizontal: 20,
    borderRadius: 8,
    backgroundColor: "#f2e2bd",
    borderColor: BaseColors.activeTab,
    overflow: "hidden",
  },
  discountTextViewNew: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.activeTab,
  },
});

export default styles;
