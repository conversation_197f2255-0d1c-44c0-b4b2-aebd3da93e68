import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Dimensions,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import CInput from "@components/TextInput";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import CDropdown from "@components/CDropDown";
import styles from "./style";
import CButton from "@components/CButton";
import {
  getCompanyDetails,
  getCountryData,
  getCountryStateData,
  natureOfBusinessData,
  onSubmit,
  pickDocument,
} from "./apiFunctions";
import isEmpty from "lodash-es/isEmpty";
import {
  getMobileNumberLength,
  mobileValidation,
} from "@app/utils/commonFunction";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Toast from "react-native-simple-toast";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import CCamera from "@components/CameraButton/CCamera";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const { images } = require("@config/images");
const FastImage = require("react-native-fast-image");

const { setUserData, setAccessToken, setCompanyId } = authAction;
const PersonalDetailSchema = yup.object().shape({
  companyName: yup
    .string()
    .required("companyNameErrMsg")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters")
    .trim("whiteSpaceErrMsg"),
  companyEmail: yup
    .string()
    .email("validEmailAddress"),
  companyMobileNo: yup
    .string()
    .required("companyPhoneErrMsg")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  companyNatureOfBusiness: yup.string().required("natureOfBusinessErrMsg"),
  companyGSTNumber: yup.string(),
  panCardNumber: yup.string(),
  country: yup.string().required("selectCountry"),
  state: yup.string().required("selectState"),
  companyFullAddress: yup.string().required("companyFullAddressErrMsg"),
  companyProfileBio: yup
    .string()
    .required("companyProfileBioErrMsg")
    .min(10, "ProfileMustCharactersErrMsg")
    .max(200, "ProfileBioMustNotErrMsg"),
  countryCode: yup.string().default("91"),
  companyPinCode: yup
    .string()
    .required("companyPinCodeErrMsg")
    .max(10, "pinCodeCharactersErrMsg")
    .min(5, "pinCodeCharactersMaxErrMsg")
    .matches(/^[a-zA-Z0-9]+$/, "pinCodeValidErrMsg"),
});
const OtherCountrySchema = yup.object().shape({
  companyName: yup
    .string()
    .required("companyNameErrMsg")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters"),
  companyEmail: yup
    .string()
    .email("validEmailAddress"),
  companyMobileNo: yup
    .string()
    .required("companyPhoneErrMsg")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  companyNatureOfBusiness: yup.string().required("natureOfBusinessErrMsg"),
  companyGSTNumber: yup.string(),
  panCardNumber: yup.string(),
  country: yup.string().required("selectCountry"),
  companyFullAddress: yup.string().required("companyFullAddressErrMsg"),
  companyProfileBio: yup
    .string()
    .required("companyProfileBioErrMsg")
    .min(10, "ProfileMustCharactersErrMsg")
    .max(200, "ProfileBioMustNotErrMsg"),
  countryCode: yup.string().default("91"),
  companyPinCode: yup
    .string()
    .required("companyPinCodeErrMsg")
    .max(10, "pinCodeCharactersErrMsg")
    .min(5, "pinCodeCharactersMaxErrMsg")
    .matches(/^[a-zA-Z0-9]+$/, "pinCodeValidErrMsg"),
});

const SingUpWithCompanyDetail = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const { user_id, companyId, accessToken } = useSelector((e) => e.auth);

  // useRef :
  const companyNameRef = useRef(null);
  const companyEmailRef = useRef(null);
  const companyMobileNoRef = useRef(null);
  const companyNatureOfBusinessRef = useRef(null);
  const companyGSTNumberRef = useRef(null);
  const panCardNumberRef = useRef(null);
  const countryRef = useRef(null);
  const stateRef = useRef(null);
  const fullAddressRef = useRef(null);
  const profileBioRef = useRef(null);
  const companyPinCodeRef = useRef(null);
  const scrollRef = useRef(null);

  // states's
  const [profileImage, setProfileImage] = useState({});
  const [mobileNoLength, setMobileLength] = useState(10);
  const [natureOfBusinessData1, setNatureOfBusiness] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [countryData, setCountryData] = useState([]);
  const [selectedCountryID, setSelectedCountryID] = useState("");
  const [selectedCountryState, setSelectedCountryState] = useState([]);
  const [countryCode, setCountryCode] = useState("91");
  const [isSchema, setIsSchema] = useState(OtherCountrySchema);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [profilePictureModal, setProfilePictureModal] = useState(false);
  const [imagePath, setImagePath] = useState({});
  const [isEditMode, setIsEditMode] = useState(false);

  useEffect(() => {
    if (selectedCountryID === 101) {
      setIsSchema(PersonalDetailSchema);
    } else {
      setIsSchema(OtherCountrySchema);
    }
  }, [selectedCountryID]);

  // memo's
  const profileImageMemo = useMemo(() => profileImage, [profileImage]);
  const natureOfBusinessDataMemo = useMemo(
    () => natureOfBusinessData1,
    [natureOfBusinessData1]
  );
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const mobileNoLengthMemo = useMemo(() => mobileNoLength, [mobileNoLength]);
  const isSchemaMemo = useMemo(() => isSchema, [isSchema]);
  const {
    control,
    setValue,
    handleSubmit,
    setError,
    getValues,
    clearErrors,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(isSchemaMemo),
  });

  const fetchData = async () => {
    const data = await natureOfBusinessData();
    setNatureOfBusiness(data?.data?.data);
  };

  const fetchCountryStateData = async (id) => {
    const resp = await getCountryStateData(id);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setSelectedCountryState(updatedData);
  };

  const fetchCountryData = async () => {
    const resp = await getCountryData();
    if (resp !== undefined) {
      let updatedData = resp?.map((val) => {
        return {
          ...val,
          label: val.name,
          value: val.id,
        };
      });

      setCountryData(updatedData);
    }
  };
  const getDetail = async () => {
    setIsLoading(true);
    const resp = await getCompanyDetails();
    
    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      // setValue("companyEmail", resp?.data?.data?.company_email || undefined);
      if (
        !isEmpty(resp?.data?.data?.company_phone_number) ||
        resp?.data?.data?.company_phone_number !== undefined
      ) {
        setIsEditMode(true);
      }
      setValue("companyFullAddress", resp?.data?.data?.address || undefined);
      
      if (resp?.data?.data?.company_email !== "undefined" && resp?.data?.data?.company_email !== 'null') {
        setValue("companyEmail", resp?.data?.data?.company_email || undefined);
      }
      if (resp?.data?.data?.gst_number !== "undefined") {
        setValue("companyGSTNumber", resp?.data?.data?.gst_number || undefined);
      }
      if (resp?.data?.data?.pan_number !== "undefined") {
        setValue("panCardNumber", resp?.data?.data?.pan_number || undefined);
      }
      setValue("companyName", resp?.data?.data?.company_name || undefined);
      setValue(
        "companyNatureOfBusiness",
        resp?.data?.data?.nature_of_business_id || undefined
      );
      setSelectedCountryID(Number(resp?.data?.data?.country));
      setValue("country", Number(resp?.data?.data?.country) || undefined);
      setValue("countryCode", resp?.data?.data?.company_phone_code || "91");
      setCountryCode("91");
      setValue(
        "companyMobileNo",
        `${resp?.data?.data?.company_phone_number}` || undefined
      );
      setCountryCode(resp?.data?.data?.company_phone_code || undefined);
      setValue("companyPinCode", String(resp?.data?.data?.pincode) || undefined);
      setValue("state", Number(resp?.data?.data?.state) || undefined);
      dispatch(setCompanyId(resp?.data?.data?.company_id || null));
      setValue("companyProfileBio", resp?.data?.data?.company_bio || undefined);
      if (!isEmpty(resp?.data?.data?.profile_picture)) {
        setValue("companyProfile", {
          uri: resp?.data?.data?.profile_picture,
          isOld: true,
        });
        setProfileImage({
          uri: resp?.data?.data?.profile_picture,
          isOld: true,
        });
      }
      if (Number(resp?.data?.data?.country) === 101) {
        setIsSchema(PersonalDetailSchema);
      } else {
        setIsSchema(OtherCountrySchema);
      }
      setIsLoading(false);
    } else {
      console.log(
        resp.data?.message || "Something went wrong please try again"
      );
      setIsLoading(false);
    }
      
    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
      setValue("companyId", companyId), setValue("userId", user_id);
      fetchCountryData();
      if (!isEmpty(accessToken)) {
        getDetail();
      }
      fetchCountryStateData(101);
    }, [])
  );

  const returnResponse = useCallback(
    async (value) => {
      Keyboard.dismiss();
      let isValid = true;
      const mobileNumberValidation = await mobileValidation(
        value?.companyMobileNo,
        mobileNoLengthMemo
      );

      if (!mobileNumberValidation?.isValid) {
        isValid = false;
        setError("companyMobileNo", {
          message: mobileNumberValidation?.validMessage,
        });
      } else {
        isValid = true;
      }

      if (isValid) {
        if (Number(value?.country) !== 101) {
          delete value.state;
        }

        setIsLoading(true);
        const resp = await onSubmit(value, companyId, user_id);
        if (resp !== undefined) {
          if (resp?.data?.success && resp?.data?.data) {
            dispatch(setUserData(resp?.data?.data));
            dispatch(setAccessToken(resp?.data?.token));
            AsyncStorage.setItem("token", resp?.data?.token);
            if (route?.params?.isEdit) {
              Toast.show("Profile Update Successfully 😊");
              navigation.navigate("DrawerHome", { screen: "Profile" });
            } else {
              Toast.show("Registration Successfully 😊");
              navigation.replace("HomeScreen");
            }
            setIsLoading(false);
          } else {
            if(resp?.data?.message === 'Network error'){
               returnResponse(value)
            }else{
              setIsLoading(false);
              Toast.show(resp?.data?.message, Toast.LONG);
            }
          }
        }

        setIsLoading(false);
      }
      setIsLoading(false);
    },
    [isLoadingMemo]
  );

  const HandleSelectImage = async (type) => {
    const data = await pickDocument(type, imagePath);
    setProfilePictureModal(false);
    setIsLoading(false);
    setProfileImage(data);
    setValue("companyProfile", data);
  };

  useEffect(() => {
    if (isCameraOpen) {
      setProfilePictureModal(false);
    }
    if (!isCameraOpen && imagePath?.uri) {
      setProfilePictureModal(false);
      HandleSelectImage("captureImg");
    }
  }, [isCameraOpen]);

  return (
    <SafeAreaView style={styles.mainView}>
      {isCameraOpen ? (
        <View style={{ flex: 1 }}>
          <CCamera
            setImagePath={setImagePath}
            isCameraOpen={isCameraOpen}
            setIsCameraOpen={setIsCameraOpen}
            recordVideo={false}
          />
        </View>
      ) : (
        <>
          <CHeader handleBackButton={() => navigation.goBack()} />
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : null}
            style={styles.KeyboardAvoidingViewStyle}
          >
            <ScrollView
              showsVerticalScrollIndicator={false}
              ref={scrollRef}
              bounces={false}
            >
              <Image
                source={images.footMainLogo}
                style={styles.mainFootLogo}
                resizeMode={"contain"}
              />
              <Text style={styles.headingText}>
                {translate("letsCreateAccountText")}
              </Text>
              <TouchableOpacity
                style={styles.profileImgMainViewStyle}
                activeOpacity={0.8}
                onPress={() => setProfilePictureModal(true)}
              >
                {isEmpty(profileImage) ? (
                  <View
                    style={[
                      styles.profileImgMainView,
                      { backgroundColor: "#C4C4C4" },
                    ]}
                  >
                    <CustomIcon name="Camera" size={35} color={"#8E8383"} />
                  </View>
                ) : (
                  <View style={styles.profileImgMainView}>
                    <FastImage
                      source={{ uri: profileImage?.uri }}
                      style={styles.profileImgStyle}
                    />
                  </View>
                )}
                <TouchableOpacity
                  style={[styles.editMainViewStyle]}
                  activeOpacity={0.9}
                  onPress={() => setProfilePictureModal(true)}
                >
                  <CustomIcon
                    name={"Edit-Square"}
                    size={25}
                    color={BaseColors.activeTab}
                  />
                </TouchableOpacity>
              </TouchableOpacity>
              <Text style={styles.profileTextStyle}>
                {translate("CompanyLogoText")}
              </Text>
              {/* Form View */}
              <View style={styles.formMainView}>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyName"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyNameRef}
                        placeholderText={translate("companyNameText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          companyEmailRef.current.focus();
                        }}
                        isError={errors?.companyName}
                        isErrorMsg={errors?.companyName?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="companyEmail"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyEmailRef}
                        placeholderText={translate("companyEmailText")}
                        returnKeyType="next"
                        value={value}
                        onChange={(e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                        }}
                        onSubmit={() => {
                          companyMobileNoRef.current.focus();
                        }}
                        isError={errors?.companyEmail}
                        isErrorMsg={errors?.companyEmail?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyMobileNo"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyMobileNoRef}
                        placeholderText={translate("CompanyMobileText")}
                        returnKeyType="next"
                        value={value}
                        onChange={async (e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                          const mobileNumberValidation = await mobileValidation(
                            e,
                            mobileNoLengthMemo
                          );

                          if (!mobileNumberValidation?.isValid) {
                            isValid = false;
                            setError("companyMobileNo", {
                              message: mobileNumberValidation?.validMessage,
                            });
                          } else if (mobileNumberValidation?.isValid) {
                            clearErrors("companyMobileNo");
                          }
                        }}
                        maxLength={mobileNoLengthMemo || 10}
                        onSubmit={() => {
                          // companyNatureOfBusinessRef.current.focus();
                        }}
                        keyBoardType="phone-pad"
                        inputType="mobile"
                        selectedCountryCode={(e) => {
                          const country_length = getMobileNumberLength(
                            e?.country_code
                          );

                          setMobileLength(country_length);
                          setValue("companyMobileNo", undefined);
                          const yourData = e?.dial_code?.split("+");
                          setCountryCode(yourData[1]);
                          setValue("countryCode", yourData[1]);
                        }}
                        isError={errors?.companyMobileNo}
                        isErrorMsg={errors?.companyMobileNo?.message}
                        countryCodeValue={
                          getValues("countryCode")
                            ? `+${getValues("countryCode")}`
                            : `+${countryCode}`
                        }
                      />
                    </Animated.View>
                  )}
                />

                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyNatureOfBusiness"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      entering={FadeInDown}
                      style={styles.commonView}
                    >
                      <CDropdown
                        reference={companyNatureOfBusinessRef}
                        labelplaceholder={translate(
                          "CompanyNatureBusinessText"
                        )}
                        data={natureOfBusinessDataMemo}
                        setItem={(e) => onChange(e?.value)}
                        value={value}
                        isError={errors?.companyNatureOfBusiness}
                        isErrorMsg={errors?.companyNatureOfBusiness?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="companyGSTNumber"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyGSTNumberRef}
                        placeholderText={translate("gstNumberText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          panCardNumberRef.current.focus();
                        }}
                        isError={errors?.companyGSTNumber}
                        isErrorMsg={errors?.companyGSTNumber?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="panCardNumber"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={panCardNumberRef}
                        placeholderText={translate("panCardText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          companyPinCodeRef.current.focus();
                        }}
                        isError={errors?.panCardNumber}
                        isErrorMsg={errors?.panCardNumber?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyPinCode"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyPinCodeRef}
                        placeholderText={translate("pin_zip_code_text")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          fullAddressRef.current.focus();
                        }}
                        isError={errors?.companyPinCode}
                        isErrorMsg={errors?.companyPinCode?.message}
                      />
                    </Animated.View>
                  )}
                />
                <View style={styles.addressMainView}>
                  <Controller
                    control={control}
                    rules={{
                      required: true,
                    }}
                    name="country"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <Animated.View
                          entering={FadeInDown}
                          style={styles.commonView}
                        >
                          <CDropdown
                            reference={countryRef}
                            labelplaceholder={translate("countryText")}
                            data={countryData}
                            setItem={(e) => {
                              onChange(e?.value);
                              setSelectedCountryID(e?.value);
                              if (e?.value !== 101) {
                                clearErrors("state");
                              }
                            }}
                            value={value}
                            isError={errors?.country}
                            isErrorMsg={errors?.country?.message}
                            WIDTH_DROP_DOWN={
                              value === 101
                                ? Dimensions.get("window").width / 2 - 30
                                : Dimensions.get("window").width - 40
                            }
                          />
                        </Animated.View>
                      );
                    }}
                  />

                  {selectedCountryID === 101 ? (
                    <Controller
                      control={control}
                      rules={{
                        required: selectedCountryID === 101,
                      }}
                      name="state"
                      render={({ field: { onChange, value } }) => {
                        return (
                          <Animated.View
                            entering={FadeInDown}
                            style={[styles.commonView, { marginLeft: 20 }]}
                          >
                            <CDropdown
                              reference={stateRef}
                              labelplaceholder={translate("stateText")}
                              data={selectedCountryState}
                              setItem={(e) => onChange(e?.value)}
                              value={value}
                              isError={errors?.state}
                              isErrorMsg={errors?.state?.message}
                              WIDTH_DROP_DOWN={
                                Dimensions.get("window").width / 2 - 30
                              }
                            />
                          </Animated.View>
                        );
                      }}
                    />
                  ) : null}
                </View>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyFullAddress"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={fullAddressRef}
                        placeholderText={translate("fullAddressText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          profileBioRef.current.focus();
                        }}
                        isError={errors?.companyFullAddress}
                        isErrorMsg={errors?.companyFullAddress?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyProfileBio"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={[styles.commonView, { marginBottom: 40 }]}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={profileBioRef}
                        placeholderText={translate("profileBioText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={handleSubmit(onSubmit)}
                        isError={errors?.companyProfileBio}
                        isErrorMsg={errors?.companyProfileBio?.message}
                        multiline
                        numberOfLines={5}
                        onFocus={() => {
                          setTimeout(() => {
                            scrollRef?.current?.scrollToEnd({ animated: true });
                          }, 200);
                        }}
                      />
                    </Animated.View>
                  )}
                />
              </View>
            </ScrollView>
            <CButton
              style={styles.buttonView}
              onBtnClick={handleSubmit((s) => returnResponse(s))}
              loading={isLoadingMemo}
            >
              {translate("nextText")}
            </CButton>

            {profilePictureModal ? (
              <AlreadyHaveStoryModal
                visible={profilePictureModal}
                setModalVisible={(e) => setProfilePictureModal(e)}
                title1="captureFromCamera"
                title2="chooseFromGallery"
                onPressTitle1={() => setIsCameraOpen(true)}
                onPressTitle2={() => HandleSelectImage()}
              />
            ) : null}
          </KeyboardAvoidingView>
        </>
      )}
    </SafeAreaView>
  );
};
export default memo(SingUpWithCompanyDetail);
