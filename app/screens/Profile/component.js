import {
  FlatList,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./styles";
import FastImage from "react-native-fast-image";
import CButton from "@components/CButton";
import { FontFamily } from "@config/typography";
import { translate } from "../../lang/Translate";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { profileTabBar } from "@config/staticData";
import { useRef } from "react";
import { isEmpty } from "lodash-es";
import { navigationRef } from "@navigation/NavigationService";
import { images } from "@config/images";
import ShimmerPlaceholder from "react-native-shimmer-placeholder";
import LinearGradient from "react-native-linear-gradient";
import { useSelector } from "react-redux";

export const UserProfileHeader = ({
  userData = [],
  handleToUpgrade = () => {},
  onPressMoreInfo = () => {},
  handleToFollowToggle = () => {},
  onPressProfile = () => {},
  type,
  isCurrentPlan = {},
  isLoading = false,
  setIsPaymentModal = () => {},
}) => {
  const { activePlanData } = useSelector((s) => s.auth);
  return (
    <View>
      <View style={styles.userProfileHeaderStyle}>
        {/* Profile Picture View */}
        <TouchableOpacity
          style={styles.profilePictureMainStyle}
          activeOpacity={0.8}
        >
          <TouchableOpacity
            style={styles.profilePictureContainerStyle}
            onPress={() => onPressProfile()}
          >
            {!isEmpty(userData?.profile_picture) &&
            userData?.profile_picture !== undefined ? (
              <FastImage
                source={{ uri: userData?.profile_picture }}
                style={styles.profilePictureStyle}
              />
            ) : (
              <FastImage
                source={
                  userData?.gender === "male"
                    ? images.manAvatar
                    : images.womanAvatar
                }
                style={styles.profilePictureStyle}
              />
            )}
          </TouchableOpacity>
        </TouchableOpacity>
        <View
          style={{
            flexDirection: "row",
            width: "65%",
            justifyContent: "space-between",
          }}
        >
          <View>
            <Text style={styles.userNameTextStyle}>{userData?.full_name}</Text>
            <Text style={styles.userNatureTextStyle}>
              {userData?.nature_of_business_name}
            </Text>
            <View style={{ marginTop: 4 }}>
              {type !== "anotherProfile" &&
              !activePlanData?.is_prime_user &&
              !activePlanData?.is_free_user ? (
                <CButton
                  containerStyle={styles.btnContainerStyle}
                  txtSty={styles.btnTextContainerStyle}
                  titleFontSize={15}
                  titleFontFamily={FontFamily.RobotoMedium}
                  onBtnClick={() => handleToUpgrade()}
                >
                  {translate("upgradeText")}
                </CButton>
              ) : null}

              {type === "anotherProfile" ? (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 9,
                  }}
                >
                  <CButton
                    containerStyle={styles.btnContainerStyle}
                    txtSty={styles.btnTextContainerStyle}
                    titleFontSize={15}
                    titleFontFamily={FontFamily.RobotoMedium}
                    onBtnClick={() => handleToFollowToggle()}
                    loading={isLoading}
                  >
                    {userData?.is_followed ? "Unfollow" : "Follow"}
                  </CButton>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => {
                      if (
                        !isEmpty(isCurrentPlan) ||
                        activePlanData?.is_prime_user ||
                        activePlanData?.is_free_user
                      ) {
                        navigationRef.current.navigate("MessagesInfo", {
                          userInfo: {
                            is_blocked: userData?.is_blocked,
                            conversation_id: userData?.conversation_id || "",
                            userData: {
                              user_dp: userData?.profile_picture,
                              user_id: userData?.user_id,
                              full_name: userData?.username,
                            },
                          },
                        });
                      } else {
                        setIsPaymentModal(true);
                      }
                    }}
                    style={{
                      borderWidth: 1,
                      height: 30,
                      width: 30,
                      alignItems: "center",
                      justifyContent: "center",
                      borderRadius: 5,
                      borderColor: BaseColors.activeTab,
                    }}
                  >
                    <CustomIcon
                      name="BsChatFill-1"
                      size={19}
                      color={BaseColors.activeTab}
                    />
                  </TouchableOpacity>
                </View>
              ) : null}
            </View>
          </View>

          <TouchableOpacity
            style={{ marginTop: 5 }}
            activeOpacity={0.8}
            onPress={() => onPressMoreInfo()}
          >
            <CustomIcon name="dot" size={20} color={BaseColors.black} />
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.companyDetailContainerStyles}>
        {!isEmpty(userData?.company_name) && (
          <View style={styles.companyDetailMainView}>
            <CustomIcon
              name="BsBuilding"
              size={20}
              color={BaseColors.fontDarkGrayColor}
            />
            <Text style={styles.companyNameTextStyle}>
              {userData?.company_name}
            </Text>
          </View>
        )}
        <View>
          {!isEmpty(userData?.country) || !isEmpty(userData?.state) ? (
            <Text style={styles.countryStateText}>
              {userData?.code_flag} {userData?.country}{" "}
              {!isEmpty(userData?.state) ? "," : null} {userData?.state || ""}
            </Text>
          ) : null}
        </View>
        <View>
          <Text style={styles.profileBioStyle}>{userData?.company_bio}</Text>
        </View>
      </View>
    </View>
  );
};

export const UserProfileHeaderSalton = () => {
  return (
    <View>
      <View style={styles.userProfileHeaderStyle}>
        {/* Profile Picture View */}

        <ShimmerPlaceholder
          height={100}
          width={100}
          style={{ borderRadius: 55 }}
          LinearGradient={LinearGradient}
        />

        <View
          style={{
            flexDirection: "row",
            width: "65%",
            justifyContent: "space-between",
          }}
        >
          <View>
            <ShimmerPlaceholder
              height={12}
              LinearGradient={LinearGradient}
              width={120}
              style={{ borderRadius: 5 }}
            />
            <ShimmerPlaceholder
              height={12}
              LinearGradient={LinearGradient}
              width={50}
              style={{ borderRadius: 5, marginTop: 5 }}
            />
            <View style={{ marginTop: 12 }}>
              <ShimmerPlaceholder
                height={25}
                width={90}
                style={{ borderRadius: 5 }}
              />
            </View>
          </View>

          <ShimmerPlaceholder height={20} width={5} />
        </View>
      </View>
      <View style={styles.companyDetailContainerStyles}>
        <ShimmerPlaceholder
          height={16}
          width={240}
          LinearGradient={LinearGradient}
          style={{ borderRadius: 5 }}
        />

        <ShimmerPlaceholder
          height={40}
          LinearGradient={LinearGradient}
          style={{ marginTop: 12, borderRadius: 5 }}
        />
      </View>
    </View>
  );
};
export const TabBar = ({
  setCurrentIndex = () => {},
  currentIndex = 0,
  type,
}) => {
  const data =
    type === "anotherProfile"
      ? profileTabBar?.filter((i) => i.id !== 3)
      : profileTabBar;
  return (
    <View style={styles.tabBarMainView}>
      {data.map((item, index) => {
        return (
          <TouchableOpacity
            activeOpacity={0.8}
            key={index}
            style={[
              styles.tabBarView,
              {
                backgroundColor:
                  currentIndex === index
                    ? BaseColors.activeTab
                    : BaseColors.white,
                borderWidth: currentIndex === index ? 2 : 0,
                borderColor: BaseColors.activeTab,
              },
            ]}
            onPress={() => setCurrentIndex(index)}
          >
            <Text
              style={[
                styles.tabBarTextStyle,
                {
                  color:
                    currentIndex === index
                      ? BaseColors.white
                      : BaseColors.gray9,
                },
              ]}
            >
              {item?.title}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export const HighLightDataView = ({
  onPressNewHighlight = () => {},
  onPressIntro = () => {},
  onPressMyHighLights = () => {},
  onReachEnd = () => {},
  bottomLoader = true,
  myHighLights = [],
  myIntro = {},
  isCreateHighlight = false,
}) => {
  // const { myHighLights, myIntro } = useSelector((s) => s.auth);

  const scrollViewRef = useRef(null);

  const handleScroll = (event) => {
    const { layoutMeasurement, contentSize, contentOffset } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom) {
      if (myHighLights?.next_enable) {
        onReachEnd(myHighLights?.page + 1);
      }

      // Perform any action you want when the user scrolls to the end
    }
  };

  return (
    // My Intro Screen
    <View style={styles.HighLightMainContainer}>
      <ScrollView
        ref={scrollViewRef}
        onScroll={handleScroll}
        horizontal
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {isCreateHighlight || !isEmpty(myIntro) ? (
          <TouchableOpacity
            style={[styles.myIntroMainView, { marginLeft: 20 }]}
            onPress={onPressIntro}
            activeOpacity={0.8}
          >
            <View style={styles.myIntroView}>
              {!isEmpty(myIntro) ? (
                <FastImage
                  source={{ uri: myIntro?.thubnails }}
                  style={{ height: 75, width: 75, borderRadius: 49 }}
                  resizeMode="cover"
                />
              ) : isCreateHighlight ? (
                <CustomIcon
                  name="BsPlusCircle"
                  size={25}
                  color={BaseColors.activeTab}
                />
              ) : null}
            </View>
            <Text style={styles.highLightTextStyle}>My Intro</Text>
          </TouchableOpacity>
        ) : null}
        {isCreateHighlight ? (
          <TouchableOpacity
            style={[styles.myIntroMainView, { marginLeft: 10 }]}
            onPress={onPressNewHighlight}
            activeOpacity={0.8}
          >
            <View style={styles.myIntroView}>
              <CustomIcon
                name="BsPlusCircle"
                size={25}
                color={BaseColors.activeTab}
              />
            </View>
            <Text style={styles.highLightTextStyle}>New</Text>
          </TouchableOpacity>
        ) : null}

        {!isEmpty(myHighLights) ? (
          <>
            {myHighLights?.data?.map((item, index) => {
              return (
                <TouchableOpacity
                  style={[
                    styles.myIntroMainView,
                    {
                      marginLeft: 10,
                      overflow: "hidden",
                      marginRight:
                        index === myHighLights?.data?.length - 1 ? 20 : 0,
                    },
                  ]}
                  activeOpacity={0.8}
                  onPress={() => onPressMyHighLights(item)}
                >
                  <View
                    style={[
                      styles.myIntroView,
                      { overflow: "hidden", borderRadius: 49 },
                    ]}
                  >
                    <FastImage
                      source={{
                        uri:
                          item?.type === "video"
                            ? item?.thubnails
                            : item?.content_url,
                      }}
                      style={{ height: 75, width: 75, borderRadius: 49 }}
                      resizeMode="cover"
                    />
                  </View>
                  <Text style={styles.highLightTextStyle}>
                    {item?.description}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </>
        ) : null}
      </ScrollView>
    </View>
  );
};
