import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";

export const userStoryHistory = async (page = 1) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.storyHistory}?pageSize=20&page=${page}`,
      "GET",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ userStoryHistory ~ error:", error);
  }
};

export const addHighlight = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.addHighlight,
      "POST",
      data,
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ addHighlight ~ error:", error);
  }
};

export const addIntroVideo = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.addIntro,
      "POST",
      data,
      true,
      true,
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ addIntroVideo ~ error:", error);
  }
};

export const getHighlight = async (page, id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.myHighlights}/${id}?pageSize=20&page=${page}`,
      "GET",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getHighlight ~ error:", error);
  }
};

export const getsMyIntro = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getMyIntro}/${id}`,
      "GET",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ myIntro ~ error:", error);
  }
};

export const getReelDataList = async (id, page) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getReelDetail}/${id}?pageSize=10&page=${page}`,
      "GET",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getReelDataList ~ error:", error);
  }
};

export const getPostDataList = async (id, page) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getPostDetail}/${id}?pageSize=10&page=${page}`,
      "GET",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getPostDataList ~ error:", error);
  }
};

export const removeMyIntro = async () => {
  try {
    const resp = await getApiData(BaseSetting.endpoints.deleteIntro, "POST");
    return resp;
  } catch (error) {
    console.log("🚀 ~ removeMyIntro ~ error:", error);
  }
};

export const highlightUpdate = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.highlightUpdate,
      "POST",
      data,
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ highlightUpdate ~ error:", error);
  }
};

export const removeHighlight = async (storyId) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.removeHighlight}/${storyId}`,
      "POST",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ removeHighlight ~ error:", error);
  }
};

export const getFriendUserData = async (userId) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getFriendUserData}/${userId}`,
      "GET",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getUserData ~ error:", error);
  }
};

export const onShareProfile = async (receiver_id, post_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.shareProfile}?receiver_id=${receiver_id}&profile_id=${post_id}`,
      "POST",
    );
    if (resp?.data?.success) {
      Toast.show(resp?.data?.message || "Sent");
    } else {
      Toast.show(resp?.data?.message || "Not shared");
    }
  } catch (error) {
    console.log("🚀 ~ getCurrentPlantDetail ~ error:", error);
  }
};

export const boostedPostList = async (page = 1) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.boostedPostList}?slug=boosted&pageSize=20&page=${page}`,
      "GET",
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ boostedPostList ~ error:", error);
  }
};
