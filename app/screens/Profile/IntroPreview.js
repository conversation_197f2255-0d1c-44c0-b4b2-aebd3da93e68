import { View, SafeAreaView, Dimensions, Platform } from "react-native";
import React, { useMemo, useState } from "react";
import { BaseColors } from "@config/theme";
import CHeader from "@components/CHeader";
import Video from "react-native-video";
import styles from "./styles";
import CButton from "@components/CButton";
import { addIntroVideo, getsMyIntro, removeMyIntro } from "./apiFunction";
import Toast from "react-native-simple-toast";
import VideoPlayer from "react-native-video-controls";
import authAction from "@redux/reducers/auth/actions";
import { myIntroOptionData } from "@config/staticData";
import { useDispatch } from "react-redux";

const { setMyIntro, setUserSelfIntro } = authAction;
const IntroPreview = ({ navigation, route }) => {
  // Dispatch
  const dispatch = useDispatch();
  const screen = route?.params?.screen;
  const [isLoading, setIsLoading] = useState(false);
  const [showPopover, setShowPopover] = useState(false);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);

  const handleToSaveBtn = async () => {
    setIsLoading(true);
    const resp = await addIntroVideo({ file: route?.params?.video[0] });
    if (resp !== undefined && resp?.data?.success) {
      navigation.navigate("HomeScreen", { screen: "Profile" });
      setIsLoading(false);
    } else {
      Toast.show(resp?.data?.message);
      setIsLoading(false);
    }
    setIsLoading(false);
  };

  const getMyIntro = async () => {
    const resp = await getsMyIntro();
    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      dispatch(setUserSelfIntro(resp?.data?.data));
    } else {
      Toast.show(resp?.data?.message);
    }
  };
  const handleToUpdateIntro = async (e) => {
    navigation.navigate("AddReelScreen", { screenName: "profile" });
  };

  const handleToDeleteIntro = async (e) => {
    const resp = await removeMyIntro();
    if (resp !== undefined && resp?.data?.success) {
      await dispatch(setMyIntro({}));
      navigation.goBack();
    } else {
      Toast.show(resp?.data?.message || "Something went wrong please try again");
    }
  };
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: BaseColors.white }}>
      {/* {screen === "profile" ? null : ( */}
      <CHeader
        headingTitle={screen === "profile" ? "myIntro" : "gallery"}
        handleBackButton={() => navigation.goBack()}
        chat={
          screen === "profile" &&
          route?.params?.type !== "anotherProfile" &&
          route?.params?.type !== "addIntroPreview"
        }
        optionData={myIntroOptionData || []}
        showPopover={showPopover}
        setShowPopover={setShowPopover}
        handleOptionsBtn={(e) => {
          if (e === "upDateIntro") {
            handleToUpdateIntro(e);
          } else {
            handleToDeleteIntro(e);
          }
        }}
      />
      {/* )} */}

      <View style={styles.introVideoPreviewMainView}>
        {Platform.OS === "android" ? (
          <VideoPlayer
            source={{
              uri:
                screen === "profile"
                  ? route?.params?.video[0]?.content_url
                  : route?.params?.video[0]?.uri,
            }}
            onBack={() => navigation.goBack()}
            disableVolume
            disableBack
            repeat
          />
        ) : (
          <Video
            source={{
              uri:
                screen === "profile"
                  ? route?.params?.video[0]?.content_url
                  : route?.params?.video[0]?.uri,
            }}
            style={{
              height: Dimensions.get("window").height / 1.5,
              width: Dimensions.get("window").width - 20,
              alignSelf: "center",

              overflow: "hidden",
              alignItems: "center",
            }}
            resizeMode="contain"
            repeat
            controls={screen === "profile" || false}
          />
        )}
      </View>
      <View style={{ marginHorizontal: 20 }}>
        {screen === "profile" ? null : (
          <CButton onBtnClick={() => handleToSaveBtn()} loading={isLoadingMemo}>
            SAVE
          </CButton>
        )}
      </View>
    </SafeAreaView>
  );
};

export default IntroPreview;
