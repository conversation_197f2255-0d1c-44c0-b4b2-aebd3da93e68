import {
  View,
  Text,
  SafeAreaView,
  PanResponder,
  Image,
  Dimensions,
  TouchableOpacity,
  Platform,
} from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import Toast from "react-native-simple-toast";
import { BaseColors } from "@config/theme";
import StoryProgressBar from "@components/StoryComponent/StoryProgressBar";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import Video from "react-native-video";
import { CustomIcon } from "@config/LoadIcons";
import FastImage from "react-native-fast-image";
import CHeader from "@components/CHeader";
import { highLightOptionData } from "@config/staticData";
import { removeHighlight } from "./apiFunction";
import Loader from "@components/Loader";
import { images } from "@config/images";
import CInput from "@components/TextInput";
import ShareListModal from "@components/ShareListModal";
import { useSelector } from "react-redux";
import { likeDislikeToggle } from "@components/StoryComponent/apiCallFunction";
import LottieView from "lottie-react-native";
import { onSharePost } from "@app/utils/commonFunction";
import { useFocusEffect } from "@react-navigation/native";

const VideoPlaying = (props) => {
  const { thumbnailImage, url, setIsOnLoad, isPlay } = props;

  return (
    <Video
      source={{
        uri: url,
      }}
      poster={thumbnailImage}
      posterResizeMode="contain"
      minLoadRetryCount={6}
      resizeMode="contain"
      style={{
        height: Dimensions.get("window").height / 1.7,
        width: Dimensions.get("window").width - 20,
        borderRadius: 12,
      }}
      paused={isPlay}
      preload="auto"
      onBuffer={(w) => {
        Platform.OS !== "ios" ? setIsOnLoad(false) : null;
      }}
      onPlaybackResume={() => {
        setIsOnLoad(true);
      }}
      onLoadStart={(e) => {
        setIsOnLoad(false);
      }}
      onLoad={(q) => {
        setIsOnLoad(true);
      }}
      onLoadEnd={(r) => {
        setIsOnLoad(true);
      }}
    />
  );
};

// Dimension height
const { height, width } = Dimensions.get("window");

const HighlightPreviewScreen = ({ navigation, route }) => {
  const data = route?.params?.item;
  const userName = route?.params?.userProfileName;
  const userProfile = route?.params?.userProfileImage;

  const { userFollowList } = useSelector((auth) => auth.auth);
  const [isActive, setIsActive] = useState(true);
  const [isReset, setIsRest] = useState(false);
  const [openModal, setIsOpenModal] = useState(false);
  const [showPopover, setShowPopover] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLikeAnim, setIsLikeAnim] = useState(false);
  const [shareListModal, setIsShareListModal] = useState(false);
  const [textInputData, setTextInputValue] = useState(false);

  const removeHighlights = async (storyId) => {
    setIsLoading(true);
    const id = storyId || data?.story_id;
    const resp = await removeHighlight(id);

    if (resp !== undefined && resp?.data?.success) {
      setIsLoading(false);
      navigation.goBack();
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsLoading(false);
    }
    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      setIsRest(true);
      data?.type !== "video" ? setIsActive(true) : null;
    }, [isActive])
  );
  const handleToLikeAndDisLike = async (story_id) => {
    const resp = await likeDislikeToggle(story_id);
    if (resp?.data?.success) {
      if (data) {
        data.is_liked = !data.is_liked;
      }
      setIsLikeAnim(true);
    } else {
      console.error("Something went wrong please try again");
    }
  };

  const handleToReply = async (message) => {
    if (!isEmpty(message)) {
      const resp = await onReplyToMessage({
        story_id: data?.story_id,
        message: message,
      });
      console.log("Your API Response is : ", resp?.data);
      setTextInputValue(null);
      if (resp?.data?.success) {
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  };

  const LoadImage = useCallback(
    (data) => {
      return (
        <FastImage
          source={{
            uri: data?.content_url,
          }}
          resizeMode="contain"
          style={[
            {
              height: Dimensions.get("window").height / 1.7,
              width: Dimensions.get("window").width - 20,
              overflow: "hidden",
              borderRadius: 12,
              zIndex: 999,
            },
          ]}
          onLoad={(e) => setIsActive(true)}
          onLoadStart={(e) => setIsActive(true)}
          onLoadEnd={() => setIsActive(true)}
        />
      );
    },
    [isActive]
  );
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: BaseColors.white }}>
      {route?.params?.type !== "chatScreen" ? (
        <CHeader
          headingTitle="myHighlights"
          chat={route?.params?.type === "anotherProfile" ? false : true}
          optionData={highLightOptionData}
          showPopover={showPopover}
          setShowPopover={setShowPopover}
          handleOptionsBtn={(e) => {
            if (e === "rename") {
              navigation.navigate("Auth", {
                screen: "UserHighlightPreview",
                params: {
                  item: data,
                  type: "edit",
                },
              });
            } else {
              removeHighlights();
            }
          }}
          handleBackButton={() => navigation.goBack()}
        />
      ) : null}
      <Loader loading={isLoading} />
      <View style={styles.contentMainView}>
        <View style={styles.indiCatorMainView}>
          <StoryProgressBar
            duration={
              data?.type === "image" ? 5000 : data?.story_seconds || 15000
            } // Example duration (5 seconds)
            isActive={isActive} // Pass isActive state
            onComplete={() => navigation.goBack()} // Callback when progress completes
            progressWidth={`${100}%`} // Optional prop to set progress bar width
            completedColor="red" // Optional prop to set completed color
            reset={false}
          />
        </View>
        <View style={[styles.headerView, { justifyContent: "space-between" }]}>
          <View style={[styles.headerView]}>
            <Image
              source={{ uri: userProfile }}
              style={styles.profileImageView}
            />
            <View>
              <Text style={styles.userNameText}>{userName}</Text>
              <Text style={styles.hoursTimeText}>{data?.timestamp}</Text>
            </View>
          </View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
              navigation.goBack();
            }}
            style={{ zIndex: 1111111 }}
          >
            <Text style={styles.cancelTextStyle}>
              {translate("cancelText")}
            </Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={[
            {
              alignItems: "center",
              zIndex: 11111,

              overflow: "hidden",
            },
          ]}
          activeOpacity={1}
          onPressIn={() => {
            // Hold in
            console.log("Press in");
            // if (!isViewUserList) {
            setIsActive(false);
            // }
          }}
          onPressOut={() => {
            // Hold Out
            // if (!isViewUserList) {
            setIsActive(true);
            // }
          }}
        >
          {data?.type === "video" ? (
            <VideoPlaying
              url={data?.content_url}
              thumbnailImage={data?.thubnails}
              isPlay={!isActive}
              setIsOnLoad={(e) => {
                setIsActive(e);
              }}
            />
          ) : (
            <FastImage
              source={{
                uri: data?.content_url,
              }}
              resizeMode="contain"
              style={[
                {
                  height: Dimensions.get("window").height / 1.7,
                  width: Dimensions.get("window").width - 20,
                  overflow: "hidden",
                  borderRadius: 12,
                  zIndex: 999,
                },
              ]}
              onLoad={(e) => setIsActive(true)}
              onLoadStart={(e) => setIsActive(true)}
              onLoadEnd={() => setIsActive(true)}
            />
          )}
        </TouchableOpacity>
      </View>
      {route?.params?.type === "chatScreen" ? (
        <View>
          <View style={styles.bottomView}>
            <View
              style={{
                width:
                  Platform.OS === "android"
                    ? data?.is_shareable === 1
                      ? "75%"
                      : "89%"
                    : data?.is_shareable === 1
                      ? "70%"
                      : "79%",
              }}
            >
              <CInput
                placeholderText={"Send a message..."}
                textInputStyle={{ maxHeight: 35 }}
                onChange={setTextInputValue}
                value={textInputData}
                onSubmit={() => {
                  handleToReply(textInputData);
                }}
              />
            </View>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => handleToLikeAndDisLike(data?.story_id)}
            >
              {data?.is_liked === false ? (
                <CustomIcon
                  name="BsSuitHeart"
                  size={30}
                  color={BaseColors.activeTab}
                />
              ) : (
                <>
                  {isLikeAnim ? (
                    <LottieView
                      autoSize={true}
                      source={images.like}
                      autoPlay={true}
                      loop={false}
                      style={styles.likeView}
                      onAnimationFinish={() => setIsLikeAnim(false)}
                    />
                  ) : null}
                  <CustomIcon
                    name="BsSuitHeartFill"
                    size={30}
                    color={BaseColors.activeTab}
                  />
                </>
              )}
            </TouchableOpacity>
            {data?.is_shareable === 1 ? (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => setIsShareListModal(true)}
              >
                <CustomIcon
                  name="BsShareFill"
                  size={30}
                  color={BaseColors.gray2}
                />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
      ) : null}

      <ShareListModal
        visible={shareListModal}
        setModalVisible={() => {
          setIsShareListModal(!shareListModal);
        }}
        buttonText={"Share"}
        listData={userFollowList?.data || []}
        shareItem={data?.story_id}
        handleBtnPress={(s, i) => {
          onSharePost(s?.item, i);
        }}
      />
    </SafeAreaView>
  );
};

export default HighlightPreviewScreen;
