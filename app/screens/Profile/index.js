// User Profile screen
import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from "react";
import {
  Dimensions,
  FlatList,
  Platform,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./styles";
import {
  HighLightDataView,
  TabBar,
  UserProfileHeader,
  UserProfileHeaderSalton,
} from "./component";
import CButton from "@components/CButton";
import { BaseColors } from "@config/theme";
import {
  boostedPostList,
  getFriendUserData,
  getHighlight,
  getPostDataList,
  getReelDataList,
  getsMyIntro,
  onShareProfile,
} from "./apiFunction";
import authAction from "@redux/reducers/auth/actions";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import Toast from "react-native-simple-toast";
import FastImage from "react-native-fast-image";
import LottieView from "lottie-react-native";
import NoRecord from "@components/NoRecord";
import { images } from "@config/images";
import { isEmpty } from "lodash-es";
import MoreInfoModal from "@components/MoreInfoModal";
import {
  profileEditForAnotherUserModalItem,
  profileEditModalItem,
} from "@config/staticData";
import {
  getUserFollowList,
  getUserFollowingList,
  handleFollowToggle,
} from "@app/utils/commonFunction";
import FollowListModal from "@components/FollowListModal";
import UserProfileCard from "@components/UserProfileCard";
import ReportModal from "@components/ReportModal";
import { translate } from "./../../lang/Translate";
import { handleToReportUser } from "@screens/ViewReels/apiFunctions";
import ShareListModal from "@components/ShareListModal";
import dayjs from "dayjs";
import ProfilePreview from "@components/ProfilePreview";
import ShimmerPlaceholder from "react-native-shimmer-placeholder";
import LinearGradient from "react-native-linear-gradient";
import PurChasePlanModal from "@components/PurchasePlanModal";
import { CustomIcon } from "@config/LoadIcons";
import { navigationRef } from "@navigation/NavigationService";
import CHeader from "@components/CHeader";
import FeedBackModal from "@components/FeedbackModal";

const {
  setMyHighlights,
  setMyIntro,
  setMyReel,
  setMyPost,
  setUserId,
  setUserFollowList,
  setUserFollowingList,
  setFriendUserData,
  setUserSelfPost,
  setUserSelfReel,
  setUserSelfHighlight,
  setUserSelfIntro,
  setIsReferralCode,
  setUserData,
  setSavedPostList,
  setSavedReelList,
  setMyBoostedPostList,
} = authAction;
const Profile = ({ navigation, route }) => {
  const type = route?.params?.type;
  // Redux Variable
  const {
    myHighLights,
    myPost,
    myReel,
    userData,
    myIntro,
    userFollowList,
    userFollowingList,
    friendUserData,
    userSelfPost,
    userSelfReel,
    userSelfHighlight,
    userSelfIntro,
    isCurrentPlan,
    activePlanData,
    myBoostedPostList,
  } = useSelector((s) => s.auth);

  const dispatch = useDispatch();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isBottomLoader, setIsBottomLoader] = useState(false);
  const [isEditModal, setIsEditModal] = useState(false);
  const [followListModal, setFollowListModal] = useState(false);
  const [followingListModal, setIsFollowingListModal] = useState(false);
  const [isUserModal, setIsUserModal] = useState(false);
  const [isFollowListLoading, setIsFollowListLoading] = useState(false);
  const [shareListModal, setIsShareListModal] = useState(false);
  const [isProfileModal, setIsProfileModal] = useState(false);
  const [isFollowingListLoading, setIsFollowingListLoading] = useState(false);
  const [isUserModalData, setIsUserModalData] = useState({});
  const [isPostLoading, setIsPostLoading] = useState(false);
  const [isReelLoading, setIsReelLoading] = useState(false);
  const [isBoostLoading, setIsBoostLoading] = useState(false);
  const [isReportModal, setIsReportModal] = useState(false);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [isFollowLoading, setIsFollowLoading] = useState(false);
  const [reportReason, setReportReason] = useState({});
  const [reportTextInput, setReportTextinput] = useState("");
  const [reasonErrorMessage, setIsReasonErrMsg] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const isEditModalMemo = useMemo(() => isEditModal, [isEditModal]);

  const getUserShareList =
    userFollowList?.data && userFollowingList?.data
      ? [...new Set([...userFollowList?.data, ...userFollowingList?.data])]
      : [];

  const getHighLights = async (page = 1, isBottomLoader = false, id) => {
    try {
      if (isBottomLoader === true) {
        setIsBottomLoader(true);
      }
      const resp = await getHighlight(page, id);
      if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
        if (type === "anotherProfile") {
          dispatch(
            setMyHighlights({
              page: page,
              next_enable: resp?.data?.hasNextPage || false,
              data:
                page > 1
                  ? [...myHighLights?.data, ...resp?.data?.data]
                  : resp?.data?.data,
            })
          );
        } else {
          dispatch(
            setUserSelfHighlight({
              page: page,
              next_enable: resp?.data?.hasNextPage || false,
              data:
                page > 1
                  ? [...userSelfHighlight?.data, ...resp?.data?.data]
                  : resp?.data?.data,
            })
          );
        }
        setIsBottomLoader(false);
      } else {
        Toast.show(resp?.data?.message);
        setIsBottomLoader(false);
      }
      setIsBottomLoader(false);
    } catch (error) {
      console.error("Error fetching highlights:", error);
      Toast.show("Something went wrong");
    } finally {
      setIsBottomLoader(false);
    }
  };
  const onRefresh = useCallback(async () => {
    setRefreshing(true); // Set refreshing state to true
    // Fetch updated data here
    await apiFunction();

    setRefreshing(false);
  }, [refreshing]);

  const handleScroll = ({ nativeEvent }) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const isAtEnd =
      layoutMeasurement.height + contentOffset.y >= contentSize.height;

    if (isAtEnd) {
      if (type === "anotherProfile") {
        if (currentIndex === 0 && myPost?.next_enable) {
          getMyPost(myPost?.page + 1, route?.params?.data?.user_id);
        } else if (currentIndex === 1 && myReel?.next_enable) {
          getMyReelList(myReel?.page, route?.params?.data?.user_id);
        }
      } else {
        if (currentIndex === 0 && userSelfPost?.next_enable) {
          getMyPost(userSelfPost?.page + 1, userData?.user_id);
        } else if (currentIndex === 1 && userSelfReel?.next_enable) {
          getMyReelList(userSelfReel?.page + 1, userData?.user_id);
        } else if (currentIndex === 2 && myBoostedPostList?.next_enable) {
          getBoostedPostList(myBoostedPostList?.page + 1);
        }
      }
    }
  };
  const getUserFollowLists = async (id) => {
    setIsFollowListLoading(true);
    const resp = await getUserFollowingList(id);
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      if (type === "anotherProfile") {
        friendUserData.followers = resp?.data?.data?.length || 0;
        dispatch(setFriendUserData(friendUserData));
      } else {
        userData.followers = resp?.data?.data?.length || 0;
        dispatch(setUserData(userData));
      }
      dispatch(setUserFollowList(resp?.data));
      setFollowListModal(true);
      setIsFollowListLoading(false);
    } else {
      console.log("Some thing went wrong");
      setIsFollowListLoading(false);
    }
    setIsFollowListLoading(false);
  };
  const getUserFollowingLists = async (id) => {
    setIsFollowingListLoading(true);
    const resp = await getUserFollowList(id);

    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      if (type === "anotherProfile") {
        friendUserData.followings = resp?.data?.data?.length || 0;
        dispatch(setFriendUserData(friendUserData));
      } else {
        userData.followings = resp?.data?.data?.length || 0;
        dispatch(setUserData(userData));
      }
      dispatch(setUserFollowingList(resp?.data));
      setIsFollowingListModal(true);
      setIsFollowingListLoading(false);
    } else {
      console.log("Some thing went wrong");
      setIsFollowingListLoading(false);
    }
    setIsFollowingListLoading(false);
  };
  // const getUser Follow
  const getMyIntro = async (id) => {
    const resp = await getsMyIntro(id);
    if (resp !== undefined && resp?.data?.success) {
      if (
        type === "anotherProfile" &&
        JSON.stringify(resp?.data?.data) !== JSON.stringify(myIntro)
      ) {
        dispatch(setMyIntro(resp?.data?.data));
      } else {
        if (
          JSON.stringify(userSelfIntro) !== JSON.stringify(resp?.data?.data)
        ) {
          dispatch(setUserSelfIntro(resp?.data?.data));
        }
      }
    } else {
      Toast.show(resp?.data?.message);
    }
  };

  // console.log("UserSelfPost list", userSelfPost);
  const getMyPost = async (page = 1, id) => {
    setIsPostLoading(true);
    const resp = await getPostDataList(id, page);
    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      if (type !== "anotherProfile") {
        dispatch(
          setUserSelfPost({
            page: page,
            next_enable: resp?.data?.hasNextPage || false,
            data:
              page > 1
                ? [...userSelfPost?.data, ...resp?.data?.data]
                : resp?.data?.data,
          })
        );

        setIsPostLoading(false);
      } else {
        dispatch(
          setMyPost({
            page: page,
            next_enable: resp?.data?.hasNextPage || false,
            data:
              page > 1
                ? [...myPost?.data, ...resp?.data?.data]
                : resp?.data?.data,
          })
        );
        setIsPostLoading(false);
      }
      setIsPostLoading(false);
    } else {
      Toast.show(resp?.data?.message || "Error while fetching posts");

      setIsPostLoading(false);
    }
    setIsPostLoading(false);
  };
  const getBoostedPostList = async (page = 1) => {
    const resp = await boostedPostList(page);

    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      dispatch(
        setMyBoostedPostList({
          page: page,
          next_enable: resp?.data?.hasNextPage || false,
          data:
            page > 1
              ? [...myBoostedPostList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      setIsBoostLoading(false);
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsBoostLoading(false);
    }
    setIsBoostLoading(false);
  };
  const getMyReelList = async (page = 1, id) => {
    const resp = await getReelDataList(id, page);
    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      if (type === "anotherProfile") {
        dispatch(
          setMyReel({
            page: page,
            next_enable: resp?.data?.hasNextPage || false,
            data:
              page > 1
                ? [...myReel?.data, ...resp?.data?.data]
                : resp?.data?.data,
          })
        );

        setIsReelLoading(false);
      } else {
        dispatch(
          setUserSelfReel({
            page: page,
            next_enable: resp?.data?.hasNextPage || false,
            data:
              page > 1
                ? [...userSelfReel?.data, ...resp?.data?.data]
                : resp?.data?.data,
          })
        );
        setIsReelLoading(false);
      }
    } else {
      setIsReelLoading(false);
    }

    setIsReelLoading(false);
  };

  const renderItem = useCallback(
    ({ item, index }) => {
      const createdAt = dayjs(item?.updatedAt).format("DD-MMMM");
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.imageContainer}
          onPress={() => {
            if (currentIndex === 0) {
              if (route?.params?.type === "anotherProfile") {
                if (!isEmpty(myPost)) {
                  dispatch(setSavedPostList(myPost));
                }
              } else {
                if (!isEmpty(userSelfPost)) {
                  dispatch(setSavedPostList(userSelfPost));
                }
              }
              navigation.navigate("SavedPostList", {
                selectedPostIndex: index,
                type:
                  route?.params?.type === "anotherProfile"
                    ? "anotherProfile"
                    : "profile",
              });
            } else if (currentIndex === 1) {
              navigation.navigate("ViewReelForSave", {
                selectedReelIndex: index,
                type:
                  route?.params?.type === "anotherProfile"
                    ? "anotherProfile"
                    : "profile",
              });
              if (route?.params?.type === "anotherProfile") {
                if (!isEmpty(myReel)) {
                  dispatch(setSavedReelList(myReel));
                }
              } else {
                if (!isEmpty(userSelfReel)) {
                  dispatch(setSavedReelList(userSelfReel));
                }
              }
            } else if (currentIndex === 2) {
              // Toast.show("Coming soon");
              const updatedPostList = {
                page: 1,
                next_enable: false,
                data: [{ ...item, whereCome: "boost" }],
              };
              if (item?.type === "post") {
                dispatch(setSavedPostList(updatedPostList));
                navigation.navigate("SavedPostList", {
                  selectedPostIndex: index,
                  type: "profile",
                });
              } else if (item?.type === "reel") {
                dispatch(setSavedReelList(updatedPostList));
                navigation.navigate("ViewReelForSave", {
                  selectedReelIndex: index,
                  type: "profile",
                });
              }
              // navigation.navigate("BoostedPostInsights", { data: item });
            }
          }}
        >
          {item?.ImageData || item?.ReelData ? (
            <FastImage
              source={{
                uri:
                  currentIndex === 0
                    ? item?.ImageData[0]?.fileUrl
                    : currentIndex === 2
                      ? item?.type === "post"
                        ? item?.ImageData[0]?.fileUrl
                        : item?.ReelData?.thumbnailData?.thumbUrl
                      : item?.ReelData?.thumbnailData?.thumbUrl,
              }}
              style={styles.image}
              resizeMode={"cover"}
            />
          ) : null}

          {currentIndex === 0 || currentIndex === 2 ? null : (
            <View style={styles.addReelIcon}>
              <LottieView
                autoSize={true}
                source={images.reelAnim}
                autoPlay={true}
                loop
                style={styles.reelAnimView}
              />
            </View>
          )}
          {currentIndex === 0 || currentIndex === 2 ? null : (
            <View style={styles.viewCountView}>
              <CustomIcon name="BsPlay" size={20} color={BaseColors.white} />
              <Text style={styles.viewCountTextStyle}>{item?.view_count}</Text>
            </View>
          )}
          {currentIndex === 2 ? (
            item?.type === "reel" ? (
              <View style={styles.addReelIcon}>
                <LottieView
                  autoSize={true}
                  source={images.reelAnim}
                  autoPlay={true}
                  loop
                  style={styles.reelAnimView}
                />
              </View>
            ) : null
          ) : null}
          {currentIndex === 2 ? (
            <View style={styles.createdAtTimeView}>
              <Text style={styles.createdAtTimeText}>
                {createdAt ? createdAt?.split("-")[0] : 0}
                {"\n"}
                {createdAt ? createdAt?.split("-")[1] : ""}
              </Text>
            </View>
          ) : null}
        </TouchableOpacity>
      );
    },
    [currentIndex, myPost, userSelfPost, myReel, userSelfReel]
  );

  const getFriendsUserData = async (id) => {
    const resp = await getFriendUserData(id);
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      if (route?.params?.type === "anotherProfile") {
        // if (
        //   JSON.stringify(resp?.data?.data) !== JSON.stringify(friendUserData)
        // ) {
          dispatch(setFriendUserData(resp?.data?.data));
        // }
      } else {
        if (JSON.stringify(resp?.data?.data) !== JSON.stringify(userData)) {
          dispatch(setUserData(resp?.data?.data));
        }
      }
    } else {
      navigation.goBack();
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  };

  useFocusEffect(
    useCallback(() => {
      dispatch(setFriendUserData({}));
      dispatch(setMyHighlights({}));
      dispatch(setMyIntro({}));
      dispatch(setMyReel({}));
      dispatch(setMyPost({}));
      apiFunction();
    }, [])
  );

  const apiFunction = async () => {
    setIsBoostLoading(true);
    setIsPostLoading(true);
    setIsReelLoading(true);
    // cleanRedux();
    dispatch(setFriendUserData({}));
    dispatch(setMyHighlights({}));
    dispatch(setMyIntro({}));
    dispatch(setMyReel({}));
    dispatch(setMyPost({}));
    if (
      route?.params?.type !== undefined &&
      route?.params?.type === "anotherProfile"
    ) {
      getFriendsUserData(route?.params?.data?.user_id);
      getMyPost(1, route?.params?.data?.user_id);
      getHighLights(1, false, route?.params?.data?.user_id);
      getMyIntro(route?.params?.data?.user_id);
      // getMyReelList(1, route?.params?.data?.user_id);
    } else {
      getFriendsUserData(userData?.user_id);
      getHighLights(1, false, userData?.user_id);
      getMyIntro(userData?.user_id);
      getMyPost(1, userData?.user_id);
      // await getMyReelList(1, userData?.user_id);
      // await getBoostedPostList(1);
    }
  };

  const onPressItem = async (item) => {
    console.log("Press", item);

    if (navigationRef.current?.getCurrentRoute()?.name !== "profile") {
      navigation.push("ProfileNew", { data: item, type: "anotherProfile" });
    } else {
      navigation.navigate("ProfileNew", { data: item, type: "anotherProfile" });
    }
  };
  const handleToReport = useCallback(async () => {
    const data = {
      user_id:
        type === "anotherProfile"
          ? route?.params?.data?.user_id
          : userData?.user_id,
      reason: translate(reportReason?.options),
      type: "user",
      reporting_id:
        type === "anotherProfile"
          ? route?.params?.data?.user_id
          : userData?.user_id,
    };

    if (reportReason?.options === "somethingElseText") {
      data.reason = reportTextInput;
    }
    if (
      reportReason?.options === "somethingElseText" &&
      reportTextInput === ""
    ) {
      setIsReasonErrMsg("Please enter,\nWhy you are report this user!");
    } else {
      const resp = await handleToReportUser(data);
      if (resp !== undefined && resp?.data?.success) {
        Toast.show(resp?.data?.message);
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
      }
      setIsReportModal(false);
    }
  });
  const handleToNavigationForEdit = async (item) => {
    if (type !== "anotherProfile") {
      if (item?.id === 1) {
        dispatch(setUserId(userData?.user_id));
        dispatch(setIsReferralCode(true));
        await setIsEditModal(false);
        navigation.navigate("Auth", {
          screen: "SignUpWithFastRegistration",
          params: { id: userData?.user_id, isEdit: true },
        });
      } else if (item?.id === 2) {
        Toast.show("Coming soon");
      }
    } else {
      if (
        !isEmpty(isCurrentPlan) ||
        activePlanData?.is_prime_user ||
        activePlanData?.is_free_user
      ) {
        if (item?.id === 1) {
          setIsEditModal(false);
          setIsShareListModal(true);
        } else if (item?.id === 2) {
          setIsEditModal(false);
          setIsReportModal(true);
        }
      } else {
        setIsEditModal(false);
        if (Platform.OS === "ios") {
          setTimeout(() => {
            setIsPaymentModal(true);
          }, 200);
        } else {
          setIsPaymentModal(true);
        }
      }
    }
  };

  function removeDuplicates(arr, prop) {
    return arr.filter(
      (obj, index, self) =>
        index === self.findIndex((o) => o[prop] === obj[prop])
    );
  }
  const handleToToggleFollow = async () => {
    setIsFollowLoading(true);
    friendUserData.is_followed = friendUserData.is_followed ? false : true;
    if (friendUserData.is_followed) {
      friendUserData.followers = friendUserData.followers + 1;
    } else {
      friendUserData.followers = friendUserData.followers - 1;
    }

    dispatch(setFriendUserData(friendUserData));
    const resp = await handleFollowToggle(friendUserData?.user_id);
    if (resp !== undefined && resp?.data?.success) {
      setIsFollowLoading(false);
      setIsFollowListLoading(false);
    } else {
      friendUserData.is_followed = !friendUserData.is_followed;
      dispatch(setFriendUserData(friendUserData));
      setIsFollowLoading(false);
    }
    setIsFollowLoading(false);
    setIsFollowListLoading(false);
  };

  const followUnFollowAnimation = (index) => {
    userFollowList.data[index].is_followed =
      !userFollowList.data[index].is_followed;
    dispatch(setUserFollowList(userFollowList));
  };
  const followingFollowAnimation = (index) => {
    userFollowingList.data[index].is_followed =
      !userFollowingList.data[index].is_followed;
    dispatch(setUserFollowingList(userFollowingList));
  };
  const handleToBtnPressForFollow = async (data) => {
    if (data?.type === "follow") {
      followUnFollowAnimation(data?.index);
    } else {
      followingFollowAnimation(data?.index);
    }
    const resp = await handleFollowToggle(data?.item);
    if (resp !== undefined && resp?.data?.success) {
      console.log("Success");
    } else {
      if (data?.type === "follow") {
        followUnFollowAnimation(data?.index);
      } else {
        followingFollowAnimation(data?.index);
      }
    }
  };

  const getTabData = (e) => {
    if (e === 2) {
      getBoostedPostList(1);
    } else if (e === 1) {
      if (
        route?.params?.type !== undefined &&
        route?.params?.type === "anotherProfile"
      ) {
        getMyReelList(1, route?.params?.data?.user_id);
      } else {
        getMyReelList(1, userData?.user_id);
      }
    } else {
      if (
        route?.params?.type !== undefined &&
        route?.params?.type === "anotherProfile"
      ) {
        getMyPost(1, route?.params?.data?.user_id);
      } else {
        getMyPost(1, userData?.user_id);
      }
    }
  };

  return (
    <SafeAreaView style={styles.mainView}>
      {navigationRef.current?.getCurrentRoute()?.name !== "profile" ? (
        <CHeader handleBackButton={() => navigation.goBack()} />
      ) : null}
      <ScrollView
        style={{
          flex: 1,
          marginBottom:
            Platform.OS === "android"
              ? route?.params?.screenName === "chatScreen"
                ? 50
                : 0
              : route?.params?.screenName === "chatScreen"
                ? 50
                : 50,
        }}
        showsVerticalScrollIndicator={false}
        // bounces={false}
        onScroll={handleScroll}
        scrollEventThrottle={16} // Adjust the throttle value as needed
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.activeTab]} // Customize refresh indicator color
            tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
            style={{ zIndex: 1111111111, paddingTop: 10 }}
          />
        }
      >
        {/* For User Profile */}
        {(type === "anotherProfile" && isEmpty(friendUserData)) ||
        isEmpty(userData) ? (
          <UserProfileHeaderSalton />
        ) : (
          <UserProfileHeader
            isCurrentPlan={isCurrentPlan}
            userData={type === "anotherProfile" ? friendUserData : userData}
            handleToUpgrade={() =>
              navigation.navigate("Auth", { screen: "paymentPlan" })
            }
            onPressMoreInfo={() => setIsEditModal(true)}
            handleToFollowToggle={() => {
              if (
                !isEmpty(isCurrentPlan) ||
                activePlanData?.is_prime_user ||
                activePlanData?.is_free_user
              ) {
                handleToToggleFollow();
              } else {
                setIsPaymentModal(true);
              }
            }}
            type={type}
            onPressProfile={() => setIsProfileModal(true)}
            isLoading={isFollowLoading}
            setIsPaymentModal={setIsPaymentModal}
          />
        )}

        {/* Follow or Following Button */}
        {(type === "anotherProfile" && isEmpty(friendUserData)) ||
        isEmpty(userData) ? (
          <View style={styles.followBtnContainer}>
            <>
              <ShimmerPlaceholder
                height={45}
                width={Dimensions.get("window").width / 2 - 30}
                style={{ borderRadius: 5 }}
                LinearGradient={LinearGradient}
              />
            </>
            <ShimmerPlaceholder
              height={45}
              width={Dimensions.get("window").width / 2 - 30}
              style={{ borderRadius: 5 }}
              LinearGradient={LinearGradient}
            />
          </View>
        ) : (
          <View style={styles.followBtnContainer}>
            <View style={{ width: "49%" }}>
              <CButton
                type="outlined"
                blackBtn={true}
                titleFontSize={16}
                fontColor={BaseColors.black}
                outLineBorderWidth={1}
                containerStyle={styles.followBtnContainerMain}
                loading={isFollowListLoading}
                onBtnClick={() =>
                  getUserFollowLists(
                    type === "anotherProfile"
                      ? friendUserData?.user_id
                      : userData?.user_id
                  )
                }
              >
                {type === "anotherProfile"
                  ? friendUserData?.followers
                  : userData?.followers}{" "}
                Followers
              </CButton>
            </View>
            <View style={{ width: "49%" }}>
              <CButton
                type="outlined"
                blackBtn={true}
                titleFontSize={16}
                fontColor={BaseColors.black}
                outLineBorderWidth={1}
                loading={isFollowingListLoading}
                containerStyle={styles.followBtnContainerMain}
                onBtnClick={() =>
                  getUserFollowingLists(
                    type === "anotherProfile"
                      ? friendUserData?.user_id
                      : userData?.user_id
                  )
                }
              >
                {type === "anotherProfile"
                  ? friendUserData?.followings
                  : userData?.followings}{" "}
                Following
              </CButton>
            </View>
          </View>
        )}
        {/* Highlight View */}
        <View style={{ marginVertical: 10 }}>
          {(type === "anotherProfile" && isEmpty(myHighLights)) ||
          (type !== "anotherProfile" && isEmpty(userSelfHighlight)) ? (
            <View style={{ marginHorizontal: 20 }}>
              <ShimmerPlaceholder
                height={80}
                width={80}
                style={{ borderRadius: 55 }}
                LinearGradient={LinearGradient}
              />
            </View>
          ) : (
            <HighLightDataView
              myHighLights={
                type === "anotherProfile" ? myHighLights : userSelfHighlight
              }
              myIntro={type === "anotherProfile" ? myIntro : userSelfIntro}
              onPressNewHighlight={() =>
                navigation.navigate("Auth", { screen: "MyPreviousStory" })
              }
              isCreateHighlight={type === "anotherProfile" ? false : true}
              onPressIntro={() => {
                if (!isEmpty(myIntro) || !isEmpty(userSelfIntro)) {
                  navigation.navigate("Auth", {
                    screen: "IntroPreview",
                    params: {
                      video: [
                        type === "anotherProfile" ? myIntro : userSelfIntro,
                      ],
                      screen: "profile",
                      type: type,
                    },
                  });
                } else {
                  if (type !== "anotherProfile") {
                    navigation.navigate("AddReelScreen", {
                      screenName: "profile",
                    });
                  }
                }
              }}
              onPressMyHighLights={(e) =>
                navigation.navigate("HighLightPreView", {
                  item: e,
                  userProfileName:
                    type === "anotherProfile"
                      ? route?.params?.data?.username
                      : userData?.full_name,
                  userProfileImage: userData?.profile_picture,
                  type: type,
                })
              }
              onReachEnd={(e) => {
                getHighLights(e, true);
              }}
              bottomLoader={isBottomLoader}
            />
          )}
        </View>
        <View style={{ flex: 1, marginVertical: 12, paddingHorizontal: 20 }}>
          <TabBar
            type={type}
            setCurrentIndex={async (e) => {
              getTabData(e);
              setCurrentIndex(e);
            }}
            currentIndex={currentIndex}
          />
          <View style={{ paddingVertical: 12 }}>
            {(currentIndex === 0 &&
              type === "anotherProfile" &&
              myPost?.data?.length === 0) ||
              (currentIndex === 0 && userSelfPost?.data?.length === 0) ||
              (currentIndex === 1 &&
                type === "anotherProfile" &&
                myReel?.data?.length === 0) ||
              (currentIndex === 1 && userSelfReel?.data?.length === 0)}
            <FlatList
              data={
                currentIndex === 0
                  ? type === "anotherProfile"
                    ? myPost?.data || []
                    : userSelfPost?.data || []
                  : currentIndex === 1
                    ? type === "anotherProfile"
                      ? myReel?.data || []
                      : userSelfReel?.data || []
                    : currentIndex === 2
                      ? myBoostedPostList?.data
                      : []
              }
              renderItem={renderItem}
              bounces={false}
              keyExtractor={(item, index) => index.toString()}
              keyboardShouldPersistTaps="handled"
              numColumns={3} // Set the number of columns for grid layout
              ListEmptyComponent={
                (currentIndex === 0 && isPostLoading) ||
                (currentIndex === 1 && isReelLoading) ||
                (currentIndex === 2 && isBoostLoading) ? (
                  <FlatList
                    data={[1, 2, 3, 4, 5, 5]}
                    numColumns={3}
                    renderItem={() => {
                      return (
                        <View
                          style={{ flexDirection: "row", flexWrap: "wrap" }}
                        >
                          <ShimmerPlaceholder
                            height={120}
                            width={Dimensions.get("window").width / 3 - 20}
                            style={[
                              styles.imageContainer,
                              {
                                width: Dimensions.get("window").width / 3 - 20,
                              },
                            ]}
                            LinearGradient={LinearGradient}
                          />
                        </View>
                      );
                    }}
                  />
                ) : (
                  <View style={styles.centerMain}>
                    <NoRecord
                      title={
                        currentIndex === 0
                          ? "noPostFound"
                          : currentIndex === 1
                            ? "noReelFound"
                            : "noBoostedPostFound"
                      }
                    />
                  </View>
                )
              }
              contentContainerStyle={{ marginBottom: 40 }}
              showsVerticalScrollIndicator={false}
              // onEndReached={handleLoadMore} // Call handleLoadMore when end of list is reached
              onEndReachedThreshold={0.1}
            />
          </View>
        </View>
      </ScrollView>

      <MoreInfoModal
        listData={
          type !== "anotherProfile" ||
          (type === "anotherProfile" &&
            route?.params?.data?.user_id === userData?.user_id)
            ? profileEditModalItem
            : profileEditForAnotherUserModalItem
        }
        visible={isEditModalMemo}
        setModalVisible={(e) => setIsEditModal(e)}
        onBtnPress={(e) => handleToNavigationForEdit(e)}
      />
      {/*For Follow List*/}

      <FollowListModal
        visible={followListModal}
        setModalVisible={() => setFollowListModal(!followListModal)}
        buttonText={"Remove"}
        title={`${type === "anotherProfile" ? friendUserData?.followers : userData?.followers || 0} Followers`}
        listData={userFollowList?.data || []}
        type="follow"
        user_id={
          type === "anotherProfile"
            ? friendUserData?.user_id
            : userData?.user_id
        }
        onPressItemClick={(e) => {
          setFollowListModal(false);
          onPressItem(e);
          // navigation.navigate("ProfileNew", {
          //   data: e,
          //   type: "anotherProfile",
          // });
        }}
        handleBtnPress={async (e) => {
          console.log("Your data");
          if (e?.type == "Follow") {
            if (
              !isEmpty(isCurrentPlan) ||
              activePlanData?.is_prime_user ||
              activePlanData?.is_free_user
            ) {
              handleToBtnPressForFollow({
                index: e?.index,
                item: e?.item?.user_id,
                type: "follow",
              });
            } else {
              setFollowListModal(false);

              if (Platform.OS === "ios") {
                setTimeout(() => {
                  setIsPaymentModal(true);
                }, 200);
              } else {
                setIsPaymentModal(true);
              }
            }
          } else {
            if (
              !isEmpty(isCurrentPlan) ||
              activePlanData?.is_prime_user ||
              activePlanData?.is_free_user
            ) {
              setFollowListModal(false);
              setIsUserModalData(e);
              if (Platform.OS === "ios") {
                // setTimeout(() => {
                setIsUserModal(true);
                // }, [500]);
              } else {
                setIsUserModal(true);
              }
            } else {
              if (Platform.OS === "ios") {
                setIsPaymentModal(true);
              } else {
                setIsPaymentModal(true);
              }
            }
          }
        }}
      />
      {/* For Following List */}
      <FollowListModal
        visible={followingListModal}
        setModalVisible={() => setIsFollowingListModal(!followingListModal)}
        buttonText={"Following"}
        title={`${
          type === "anotherProfile"
            ? friendUserData?.followings
            : userData?.followings || 0
        } Following`}
        listData={userFollowingList?.data || []}
        user_id={
          type === "anotherProfile"
            ? friendUserData?.user_id
            : userData?.user_id
        }
        onPressItemClick={(e) => {
          console.log("🚀 ~ Profile ~ e:", e);
          setIsFollowingListModal(false);
          onPressItem(e);
          // navigation.navigate("ProfileNew", {
          //   data: e,
          //   type: "anotherProfile",
          // });
        }}
        handleBtnPress={(e) => {
          console.log("Your data", e);
          if (e?.type == "Follow") {
            if (
              !isEmpty(isCurrentPlan) ||
              activePlanData?.is_prime_user ||
              activePlanData?.is_free_user
            ) {
              handleToBtnPressForFollow({
                index: e?.index,
                item: e?.item?.user_id,
                type: "following",
              });
            } else {
              setIsFollowingListModal(false);
              if (Platform.OS === "ios") {
                setTimeout(() => {
                  setIsPaymentModal(true);
                }, 200);
              } else {
                setIsPaymentModal(true);
              }
            }
          } else {
            if (
              !isEmpty(isCurrentPlan) ||
              activePlanData?.is_prime_user ||
              activePlanData?.is_free_user
            ) {
              setIsFollowingListModal(false);
              setIsUserModalData(e);
              if (Platform.OS === "ios") {
                // setTimeout(() => {
                setIsUserModal(true);
                // }, [500]);
              } else {
                setIsUserModal(true);
              }
            } else {
              if (Platform.OS === "ios") {
                setIsPaymentModal(true);
              } else {
                setIsPaymentModal(true);
              }
            }
          }
        }}
      />

      {/* For User Card */}
      <UserProfileCard
        visible={isUserModal}
        setModalVisible={() => setIsUserModal(!isUserModal)}
        listData={isUserModalData}
      />

      {/* Report Modal */}
      <ReportModal
        visible={isReportModal}
        setModalVisible={() => setIsReportModal(false)}
        selectedOptionForReason={(e) => {
          setReportReason(e);
        }}
        textInputData={reportTextInput}
        setTextInputValue={(e) => {
          setReportTextinput(e);
          setIsReasonErrMsg("");
        }}
        reasonValue={reportReason}
        isErrMessage={reasonErrorMessage}
        onBtnPress={() => {
          handleToReport();
        }}
      />

      {/* Share List Modal */}
      <ShareListModal
        visible={shareListModal}
        setModalVisible={() => {
          setIsShareListModal(!shareListModal);
        }}
        buttonText={"Share"}
        listData={removeDuplicates(getUserShareList, "username") || []}
        shareItem={friendUserData?.user_id}
        handleBtnPress={(s, i) => {
          onShareProfile(s?.item, i);
        }}
      />
      {isProfileModal ? (
        <ProfilePreview
          visible={isProfileModal}
          setModalVisible={() => setIsProfileModal(false)}
          image={
            type === "anotherProfile"
              ? friendUserData?.profile_picture
              : userData?.profile_picture
          }
        />
      ) : null}
      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
      {navigationRef.current.getCurrentRoute().name !== "HomeTab" && (
        <FeedBackModal title="Feedback" />
      )}
    </SafeAreaView>
  );
};

export default Profile;
