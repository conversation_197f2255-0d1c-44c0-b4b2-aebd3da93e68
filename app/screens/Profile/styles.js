import { FontFamily } from "@config/typography";

const { BaseColors } = require("@config/theme");
const { StyleSheet, Dimensions } = require("react-native");

const HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
    // paddingHorizontal: 20,
  },
  userProfileHeaderStyle: {
    marginTop: 20,
    flexDirection: "row",
    alignItems: "center",
    gap: 22,
    paddingHorizontal: 20,
  },
  profilePictureStyle: {
    height: 100,
    width: 100,
    borderRadius: 55,
  },
  profilePictureMainStyle: {
    borderRadius: 55,
    overflow: "hidden",
    borderWidth: 2,
    borderColor: BaseColors.activeTab,
    height: 105,
    width: 105,
    alignItems: "center",
    justifyContent: "center",
  },
  userNameTextStyle: {
    fontSize: 21,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.activeTab,
    textTransform: "capitalize",
  },
  btnContainerStyle: {
    height: 30,
    paddingVertical: 0,
  },
  btnTextContainerStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
  },
  headingStyle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  textMainViewContainer: {
    flex: 1,
  },
  companyDetailContainerStyles: {
    marginHorizontal: 10,
    marginTop: 8,
    paddingHorizontal: 20,
  },
  companyNameTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontDarkGrayColor,
  },
  companyDetailMainView: {
    flexDirection: "row",
    gap: 4,
  },
  profileBioStyle: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontDarkGrayColor,
    marginTop: 10,
  },
  followBtnContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 17,
    paddingHorizontal: 20,
  },
  countryStateText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontDarkGrayColor,
  },
  tabBarMainView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  tabBarView: {
    borderWidth: 1,
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    borderRadius: 5,
  },
  tabBarTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.gray9,
  },
  myIntroMainView: {
    marginVertical: 10,
    alignItems: "center",
  },
  myIntroView: {
    height: 75,
    width: 75,
    borderWidth: 1,
    borderColor: BaseColors.activeTab,
    borderRadius: 49,
    alignItems: "center",
    justifyContent: "center",
  },
  HighLightMainContainer: {
    flexDirection: "row",
    flexGrow: 1,
    // paddingHorizontal: 20,
  },
  userPreStoryView: {
    height: Dimensions.get("window").width / 3 - 40,
    width: Dimensions.get("window").width / 3 - 40,
  },
  usePreviousStoryView: {
    height: Dimensions.get("window").width / 2 - 40,
    width: Dimensions.get("window").width / 3 - 20,
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
    margin: 6,
    borderRadius: 10,
  },
  createdAtTimeView: {
    position: "absolute",
    bottom: 3,
    right: 2,
    alignItems: "center",
    borderRadius: 8,
    backgroundColor: BaseColors.white,
    padding: 6,
  },
  createdAtTimeText: {
    fontSize: 10,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
    textAlign: "center",
  },
  highLightScreenMainView: {
    flex: 1,
    // alignItems: "center",
    marginHorizontal: 30,
    justifyContent: "center",
  },
  highlightImageView: {
    height: 140,
    width: 140,
    borderRadius: 85,
    borderWidth: 2,
    borderColor: BaseColors.gray4,
  },
  introVideoPreviewMainView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  highLightTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.gray9,
    textAlign: "center",
    marginTop: 6,
  },
  imageContainer: {
    height: HEIGHT / 5,
    width: "31.4%",
    borderRadius: 10,
    marginRight: 10,
    marginBottom: 10,
    overflow: "hidden",
    elevation: 6,
    position: "relative",
  },
  image: {
    width: "100%",
    height: HEIGHT / 5,
  },
  centerMain: {
    height: Dimensions.get("window").height / 3.3,
  },
  reelAnimView: {
    height: 60,
    width: 60,
    zIndex: 11111,
  },
  highLightPreviewMainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  contentMainView: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: BaseColors.white,
  },
  btnMainView: {
    height: Dimensions.get("window").height,
    width: Dimensions.get("window").width,
    position: "absolute",
    top: 0,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  indiCatorMainView: {
    width: Dimensions.get("window").width - 30,
    marginTop: 20,
    justifyContent: "space-evenly",
    alignItems: "center",
    flexDirection: "row",
  },
  indiCatorViewStyles: {
    flex: 1,
    height: 3,
    backgroundColor: "rgba(248,247,247,0.5)",
    marginLeft: 10,
  },
  profileImageView: {
    height: 75,
    width: 75,
    borderRadius: 40,
    resizeMode: "contain",
  },
  headerView: {
    paddingVertical: 18,
    paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  userNameText: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  bottomView: {
    position: "absolute",
    marginVertical: Platform.OS === "ios" ? 0 : 30,
    flexDirection: "row",
    alignSelf: "center",
    bottom: 0,
    gap: 10,
    alignItems: "center",
  },
  cancelTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#484848",
    textTransform: "capitalize",
  },
  userStoryImageStyle: {
    borderRadius: 5,
    padding: 6,
  },
  renderItemMainView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  userNameStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  hoursTimeText: {
    fontSize: 14,
    color: BaseColors.gray2,
    fontFamily: FontFamily.RobotoMedium,
  },
  countView: {
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#DCDFE3",
    width: "100%",
  },
  countTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#484848",
  },
  likeView: {
    height: 100,
    width: 100,
    position: "absolute",
    bottom: 0,
    left: -35,
  },
  highLightText: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 14,
    color: BaseColors.black,
    textAlign: "center",
  },
  watchFullReelStyle: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 12,
    color: BaseColors.gray3,
  },
  watchFullReelView: {
    flexDirection: "row",
    alignItems: "center",
  },
  followBtnContainerMain: {
    paddingHorizontal: 24,
    paddingVertical: 0,
    height: 40,
  },
  popoverStyle: {
    borderWidth: 1,
    width: 150,
    borderRadius: 8,
    marginRight: 12,
    padding: 15,
    borderColor: BaseColors.gray,
  },
  userNatureTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.black103,
    marginVertical: 5,
  },
  addReelIcon: {
    paddingRight: 5,
    paddingTop: 5,
    position: "absolute",
    right: -15,
    top: -15,
  },
  viewCountTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.white,
  },
  viewCountView: {
    flexDirection: "row",
    position: "absolute",
    bottom: 3,
    alignItems: "center",
    gap: 2,
    left: 2,
  },
});

export default styles;
