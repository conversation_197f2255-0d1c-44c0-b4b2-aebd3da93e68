import {
  View,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from "react-native";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { BaseColors } from "@config/theme";
import CHeader from "@components/CHeader";
import styles from "./styles";
import FastImage from "react-native-fast-image";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import { isEmpty } from "lodash-es";
import Toast from "react-native-simple-toast";
import { addHighlight, highlightUpdate } from "./apiFunction";
import { useFocusEffect } from "@react-navigation/native";

const UserHighlightPreview = ({ navigation, route }) => {
  const data = route?.params?.item;

  // State's
  const [highLightName, setHighlightName] = useState("");
  const [isError, setIsError] = useState("");
  const [keyboardOpen, setKeyboardOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Memo's
  const highLightNameMemo = useMemo(() => highLightName, [highLightName]);
  const isErrorMemo = useMemo(() => isError, [isError]);

  // Get KeyBoard Open or Not
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardOpen(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardOpen(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (route?.params?.type === "edit") {
        setHighlightName(data?.description);
      }
    }, [])
  );

  // HighLight Method
  const handleToSubmitHighlight = async (type) => {
    let valid = true;
    setIsLoading(true);
    if (isEmpty(highLightNameMemo.trim())) {
      valid = false;
      setIsError("Highlight name is required");
      setIsLoading(false);
    }
    if (highLightNameMemo.length > 15) {
      valid = false;
      setIsError("highlight name max length is 15");
      setIsLoading(false);
    }
    if (valid) {
      const data = await {
        story_id: route?.params?.item?.story_id,
        is_highlighted: true,
        description: highLightNameMemo,
      };

      const resp =
        type === "edit"
          ? await highlightUpdate(data)
          : await addHighlight(data);

      if (resp !== undefined && resp?.data?.success) {
        Keyboard.dismiss();
        navigation.navigate("HomeScreen", { screen: "Profile" });
        setIsLoading(false);
      } else {
        Toast.show(resp?.data?.message);
        setIsLoading(false);
      }
      setIsLoading(false);
    }
    setIsLoading(false);
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: BaseColors.white,
      }}
    >
      <CHeader
        headingTitle="highlightTitle"
        handleBackButton={() => navigation.goBack()}
      />

      <ScrollView
        style={{ flex: 1 }}
        scrollEnabled={keyboardOpen}
        contentContainerStyle={{ flex: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.highLightScreenMainView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={{ alignItems: "center" }}>
            <FastImage
              source={{
                uri:
                  data?.type === "video" ? data?.thubnails : data?.content_url,
              }}
              style={styles.highlightImageView}
            />
          </View>

          <CInput
            placeholderText={"Highlights"}
            value={highLightNameMemo}
            onChange={(e) => {
              setHighlightName(e);
              setIsError("");
            }}
            onSubmit={() => {
              Keyboard.dismiss();
            }}
            maxLength={15}
            isError={isErrorMemo}
            isErrorMsg={isErrorMemo}
            containerStyle={{ marginHorizontal: 20, marginTop: 30 }}
          />
        </KeyboardAvoidingView>
      </ScrollView>

      <View style={{ marginHorizontal: 20, marginBottom: 30 }}>
        <CButton
          onBtnClick={() => handleToSubmitHighlight(route?.params?.type || "")}
          loading={isLoading}
        >
          DONE
        </CButton>
      </View>
    </SafeAreaView>
  );
};

export default UserHighlightPreview;
