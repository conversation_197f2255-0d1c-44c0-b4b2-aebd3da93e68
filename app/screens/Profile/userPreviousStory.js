import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
} from "react-native";
import React, { useCallback, useState } from "react";
import { BaseColors } from "@config/theme";
import CHeader from "@components/CHeader";
import { userStoryHistory } from "./apiFunction";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import FastImage from "react-native-fast-image";
import styles from "./styles";
import dayjs from "dayjs";
import NoRecord from "@components/NoRecord";
import CButton from "@components/CButton";
import { translate } from "../../lang/Translate";
import { isEmpty } from "lodash-es";

const { setUserStoryHistory } = authAction;
const UserPreviousStory = ({ navigation }) => {
  const dispatch = useDispatch();
  const { userPreStoryHistory } = useSelector((s) => s.auth);

  const [currentIndex, setCurrentIndex] = useState(null);

  const [isLoading, setIsLoading] = useState(false);
  const [selectedItem, setIsCurrentItem] = useState({});
  const [isBottomLoading, setIsBottomLoading] = useState(false);
  const getStoryHistory = async (page = 1, bottomLoading = false) => {
    if (bottomLoading) {
      setIsBottomLoading(true);
    } else {
      setIsLoading(true);
    }
    const resp = await userStoryHistory(page);

    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(
        setUserStoryHistory({
          currentPage: page,
          hasNextPage: resp?.data?.hasNextPage,
          data:
            page > 1
              ? [...userPreStoryHistory?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      setIsLoading(false);
      setIsBottomLoading(false);
    } else {
      console.log("Something went wrong please try again");
      setIsLoading(false);
      setIsBottomLoading(false);
    }
    setIsLoading(false);
    setIsBottomLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      getStoryHistory(1);
    }, [])
  );

  const renderItem = ({ item, index }) => {
    const createdAt = dayjs(item?.createdAt).format("DD-MMMM");

    return (
      <TouchableOpacity
        style={styles.usePreviousStoryView}
        key={index}
        onPress={() => {
          setCurrentIndex(index), setIsCurrentItem(item);
        }}
      >
        <View
          style={{
            height: 27,
            width: 27,
            borderWidth: 1,
            position: "absolute",
            top: 5,
            zIndex: 11111,
            right: 4,
            borderRadius: 23,
            backgroundColor: "rgba(255, 255, 255, 0.3)",
            alignItems: "center",
            justifyContent: "center",
            borderColor:
              currentIndex === index ? BaseColors.activeTab : BaseColors.black,
          }}
        >
          {currentIndex === index ? (
            <View
              style={{
                height: 18,
                width: 18,
                backgroundColor: BaseColors.activeTab,
                borderRadius: 23,
              }}
            />
          ) : null}
        </View>
        <FastImage
          source={{
            uri: item?.type === "video" ? item?.thubnails : item?.content_url,
          }}
          style={styles.userPreStoryView}
          resizeMode="contain"
        />
        <View style={styles.createdAtTimeView}>
          <Text style={styles.createdAtTimeText}>
            {createdAt ? createdAt?.split("-")[0] : 0}
            {"\n"}
            {createdAt ? createdAt?.split("-")[1] : ""}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: BaseColors.white }}>
      <CHeader
        headingTitle="newHighlight"
        handleBackButton={() => navigation.goBack()}
      />

      <FlatList
        data={userPreStoryHistory?.data}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
        numColumns={3}
        contentContainerStyle={{
          margin: 10,
          justifyContent: "center",
          // alignItems: "center",
        }}
        onEndReachedThreshold={3}
        onEndReached={() => {
          if (userPreStoryHistory?.hasNextPage && !isBottomLoading) {
            getStoryHistory(userPreStoryHistory?.currentPage + 1, true);
          }
        }}
        ListEmptyComponent={
          <View style={styles.centerMain}>
            <NoRecord title={"noRecordFound"} />
          </View>
        }
      />

      {currentIndex !== null ? (
        <View style={{ paddingHorizontal: 16 }}>
          <CButton
            onBtnClick={() =>
              navigation.navigate("UserHighlightPreview", {
                item: selectedItem,
              })
            }
          >
            {translate("nextText")}
          </CButton>
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default UserPreviousStory;
