import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import {
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from "react-native";
import styles from "./styles";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { onsubmit } from "./apiFunctions";
import { BaseColors } from "@config/theme";
import { useFocusEffect } from "@react-navigation/native";
import Toast from "react-native-simple-toast";
// import Animated, { FadeIn, FadeInDown } from "react-native-reanimated";
import { CustomIcon } from "@config/LoadIcons";
import { useSelector } from "react-redux";

const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;
const FadeInDown = require("react-native-reanimated").FadeInDown;

const NewPasswordSchema = yup.object().shape({
  fullName: yup.string().required("enterNameErrorMsg"),
  Email: yup.string().required("enterEmailErrorMsg"),
  Message: yup.string().required("enterMessageErrorMsg"),
});

const ContactUs = ({ navigation, route }) => {
  // ref's
  const fullNameRef = useRef(null);
  const EmailRef = useRef(null);
  const MessageRef = useRef(null);
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(NewPasswordSchema),
  });

  // redux state
  const { userData, adminSettingData } = useSelector((state) => state.auth);
  const contactEmail = adminSettingData?.find(
    (obj) => obj?.slug === "CONTACTEMAIL"
  );

  // state
  const [isLoading, setIsLoading] = useState(false);

  // memo
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);

  // handle Submitted Data
  const handleToSubmit = async (e) => {
    setIsLoading(true);
    const resp = await onsubmit(e, setIsLoading);

    if (resp?.data?.success) {
      setIsLoading(false);
      navigation.goBack();
      Toast.show("Messages Send Successfully");
    } else {
      setIsLoading(false);
      Toast.show(resp?.data?.message);
    }
  };

  useFocusEffect(
    useCallback(() => {
      reset();
      setValue("fullName", userData?.full_name || "");
      setValue("Email", userData?.email || "");
    }, [])
  );

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="contactUs"
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : null}
        style={{
          flex: 1,
          backgroundColor: BaseColors.white,
        }}
      >
        <ScrollView>
          <View style={styles.emailTextBox}>
            <View style={styles.emailBox}>
              <CustomIcon
                name={"Message"}
                size={35}
                color={BaseColors.activeTab}
              />
            </View>
            <View>
              <Text style={styles.emailText}>Email to</Text>
              <Text style={styles.secondEmailText}>{contactEmail?.value}</Text>
            </View>
          </View>
          <View style={styles.contentView}>
            <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="fullName"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={fullNameRef}
                    placeholderText={translate("fullNameText")}
                    rightIconSize={18}
                    returnKeyType="next"
                    value={value}
                    onChange={onChange}
                    onSubmit={() => {
                      EmailRef.current.focus();
                    }}
                    isError={errors?.fullName}
                    isErrorMsg={errors?.fullName?.message}
                    label={translate("fullNameText")}
                    isRequire
                    disabled={userData?.full_name ? true : false}
                  />
                )}
              />
            </Animated.View>

            <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="Email"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={EmailRef}
                    placeholderText={translate("EmailText")}
                    rightIconSize={18}
                    returnKeyType="next"
                    value={value}
                    onChange={onChange}
                    onSubmit={() => {
                      MessageRef.current.focus();
                    }}
                    isError={errors?.Email}
                    isErrorMsg={errors?.Email?.message}
                    label={translate("EmailText")}
                    isRequire
                    disabled={userData?.email ? true : false}
                  />
                )}
              />
            </Animated.View>

            <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="Message"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={MessageRef}
                    placeholderText={translate("Message")}
                    rightIconSize={18}
                    returnKeyType="done"
                    value={value}
                    onChange={onChange}
                    onSubmit={() => {
                      Keyboard.dismiss();
                    }}
                    isError={errors?.Message}
                    isErrorMsg={errors?.Message?.message}
                    label={translate("MessageText")}
                    multiline
                    numberOfLines={5}
                    isRequire
                  />
                )}
              />
            </Animated.View>
          </View>
        </ScrollView>
        <Animated.View style={styles.buttonMainView} entering={FadeIn}>
          <CButton
            onBtnClick={handleSubmit((e) => handleToSubmit(e))}
            loading={isLoadingMemo}
          >
            {translate("Submit")}
          </CButton>
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
export default memo(ContactUs);
