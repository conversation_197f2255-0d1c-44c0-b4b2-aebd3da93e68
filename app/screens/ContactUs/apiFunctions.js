import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const onsubmit = async (data, setIsLoading) => {
  const finalData = {
    message: data?.Message,
  };

  try {
    const resp = await getApiData(
      BaseSetting.endpoints.contactUs,
      "POST",
      finalData,
      false,
      false
    );
    return resp;
  } catch (error) {
    setIsLoading(false);
  }
};
