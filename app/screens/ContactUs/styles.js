import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  emailText: {
    color: BaseColors.white,
    fontSize: 15,
    fontFamily: FontFamily.RobotoRegular,
  },
  secondEmailText: {
    color: BaseColors.white,
    fontSize: 18,
    fontFamily: FontFamily.RobotSemiBold,
    fontWeight: "600",
  },
  emailBox: {
    backgroundColor: BaseColors.white,
    width: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
  },
  emailTextBox: {
    backgroundColor: BaseColors.activeTab,
    marginHorizontal: 20,
    borderRadius: 10,
    flexDirection: "row",
    padding: 10,
    gap: 10,
  },
  mainFootLogo: {
    height: 120,
    width: 120,
    marginTop: Platform.OS === "ios" ? 0 : 0,
    alignSelf: "center",
  },
  headingText: {
    fontSize: 34,
    fontFamily: FontFamily.RobotSemiBold,
    color: "#343434",
    textAlign: "center",
  },
  contentView: {
    marginHorizontal: 25,
  },
  descText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#000000",
    marginTop: 15,
    textAlign: "center",
  },
  cInputStyle: {
    marginTop: 15,
  },
  cInputStyle1: {
    marginTop: 10,
  },
  btnStyle: {
    marginTop: 45,
  },
  PsdText: {
    marginTop: 10,
  },
  buttonMainView: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: Platform.OS === "ios" ? 0 : 20,
  },
});
export default styles;
