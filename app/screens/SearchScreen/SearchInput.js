import { FlatList, Platform, SafeAreaView, View } from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import CHeader from "@components/CHeader";
import CSearch from "@components/CSearch";
import styles from "./styles";
import NoRecord from "@components/NoRecord";
import RenderSearchData from "./RenderSearchData";
import { isEmpty, isNull } from "lodash-es";
import MiniLoader from "@components/MiniLoader";
import { getChatSearchData } from "./apiCallFunction";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import AuthActions from "@redux/reducers/auth/actions";

const { setSearchHistory } = AuthActions;

const SearchInput = ({ navigation, route }) => {
  const inputRef = useRef(null);

  const dispatch = useDispatch();

  const [searchInputHistory, setSearchInputHistory] = useState(
    route?.params?.searchBtnData || ""
  );
  const [searchBtnData, setSearchBtnData] = useState("");
  const [typingTimeout, setTypingTimeout] = useState(null);
  const [loading, setLoading] = useState(false);
  const [accountData, setAccountData] = useState([]);

  const { userData, searchHistory } = useSelector((auth) => auth.auth);
  console.log("🚀 ~ SearchInput ~ searchHistory:", searchHistory);

  useEffect(() => {
    if (!isEmpty(searchInputHistory)) {
      setSearchBtnData(searchInputHistory);
      setSearchInputHistory("");
    }
  }, [searchInputHistory]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 500); // Delay to ensure the screen transition is complete
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isNull(searchBtnData) || !isEmpty(searchInputHistory)) {
      setLoading(true);
      if (!isEmpty(searchInputHistory)) {
        handleInputChange(searchInputHistory);
      } else {
        handleInputChange(searchBtnData);
      }
    }
  }, [searchBtnData]);

  const handleSearchList = async (searchBtnData = "", page = 1) => {
    const resp = await getChatSearchData(
      searchBtnData?.trim(),
      page,
      "account"
    );
    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      setAccountData({
        page: page,
        next_enable: resp?.data?.hasNextPage || false,
        data:
          page > 1
            ? [...accountData?.data, ...resp?.data?.data]
            : resp?.data?.data
      });

      setLoading(false);
    } else {
      setAccountData([]);
      setLoading(false);
      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  // serach input handler function
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      setTypingTimeout(
        setTimeout(async () => {
          handleSearchList(searchBtnData, 1);
        }, 1000)
      );
    },
    [typingTimeout, searchBtnData]
  );

  // navigate to user Profile
  const handleToPressUser = (data) => {
    const isUserIdAvailable = searchHistory?.some(
      (item) => item?.user_id === data?.user_id
    );
    if (!isUserIdAvailable) {
      dispatch(setSearchHistory([...searchHistory, data]));
    }
    if (data?.user_id !== userData?.user_id) {
      navigation.navigate("ProfileNew", { data: data, type: "anotherProfile" });
    } else {
      navigation.navigate("ProfileNew", { data: data });
    }
  };

  const removeHistory = (historyUser_id) => {
    data = searchHistory?.filter((item) => item?.user_id !== historyUser_id);
    dispatch(setSearchHistory(data));
  };

  const renderSearchView = ({ item }) => (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <RenderSearchData
        item={item}
        onPress={(pressData) => handleToPressUser(pressData)}
        pressRemove={(data) => removeHistory(data?.user_id)}
        type={!isEmpty(searchBtnData) ? "" : "history"}
      />
    </View>
  );

  const handleSubmitEditing = () => {
    if(!isEmpty(searchBtnData)){
      navigation.navigate("SearchScreen", {
        searchBtnData: searchBtnData.trim()
      });
    }
  };

  return (
    <SafeAreaView style={styles.main}>
      {/* Header */}
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="Search"
      />

      {/* Search  */}
      <View style={styles.searchView}>
        <CSearch
          searchBtnData={searchBtnData}
          setSearchBtnData={setSearchBtnData}
          searchLeft
          handleSubmitEditing={handleSubmitEditing}
          inputRef={inputRef}
        />
      </View>

      {isEmpty(searchHistory) && isEmpty(searchBtnData) ? (
        <View style={styles.centerMainEmpty}>
          <NoRecord
            title={"Empty"}
            type="chat"
            description="noSearchHistory"
            iconName="Search-1"
          />
        </View>
      ) : loading ? (
        <MiniLoader size="medium" />
      ) : (
        <>
          <View style={styles.mainPostView}>
            {/* Search List */}
            <FlatList
              data={
                !isEmpty(searchBtnData)
                  ? accountData?.data || []
                  : searchHistory || []
              }
              renderItem={renderSearchView}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              // onEndReachedThreshold={3}
              // onEndReached={() => {
              //   if (chatListNextEnablePage?.next_enable) {
              //     handleSearchChatList(
              //       searchBtnData,
              //       chatListNextEnablePage?.page + 1
              //     );
              //   }
              // }}
              ListEmptyComponent={
                <View style={styles.centerMain}>
                  <NoRecord title={"noRecordFound"} />
                </View>
              }
              // refreshing={refreshing}
              // onRefresh={onRefresh}
              style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
            />
          </View>
        </>
      )}
    </SafeAreaView>
  );
};

export default SearchInput;
