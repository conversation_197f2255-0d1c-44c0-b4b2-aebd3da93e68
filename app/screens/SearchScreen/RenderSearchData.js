import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import FastImage from "react-native-fast-image";
import { FontFamily } from "@config/typography";
import { isNull, truncate } from "lodash-es";
import { CustomIcon } from "@config/LoadIcons";

const RenderSearchData = ({
  item,
  onPress,
  pressRemove = () => {},
  type = "",
}) => {
  return (
    <View style={{ width: "100%", flexDirection: "row" }}>
      <TouchableOpacity
        style={styles.container}
        activeOpacity={0.8}
        onPress={() => onPress(item)}
      >
        {/* Image */}
        <View style={styles.imageContainer}>
          <FastImage
            source={{
              uri: !isNull(item?.profile_picture)
                ? item?.profile_picture
                : item?.user_dp,
            }}
            style={styles.image}
          />
        </View>

        {/* Title and description */}
        <View style={styles.textContainer}>
          <View>
            <Text style={styles.searchTitle}>
              {item?.company_name || item?.full_name || ""}
            </Text>
          </View>
          <View>
            <Text style={styles.searchDescription}>
              {truncate(item?.company_bio || "", {
                length: 40,
                omission: "...",
              })}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      {/* close Icon */}
      {type === "history" ? (
        <TouchableOpacity
          style={{ justifyContent: "center", paddingRight: 20, marginLeft: 5 }}
          onPress={() => pressRemove(item)}
          activeOpacity={0.8}
        >
          <CustomIcon name={"BsXCircle"} size={24} color={"#B5B5B5"} />
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

export default RenderSearchData;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    marginVertical: 10,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
  imageContainer: {
    borderRadius: 30,
    overflow: "hidden",
  },
  image: {
    height: 55,
    width: 55,
  },
  textContainer: {
    marginLeft: 10,
    flex: 1,
  },
  searchTitle: {
    fontSize: 17,
    fontFamily: FontFamily.RobotoRegular,
    color: "#454545",
    textTransform: "capitalize",
  },
  searchDescription: {
    fontSize: 13.5,
    fontFamily: FontFamily.RobotoRegular,
    color: "#B5B5B5",
  },
});
