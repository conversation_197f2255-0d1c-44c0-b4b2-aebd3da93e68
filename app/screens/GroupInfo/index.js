import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  View,
  Text,
  KeyboardAvoidingView,
  FlatList,
  ScrollView,
  Modal,
  ActivityIndicator,
} from "react-native";
import styles from "./styles";
import { BaseColors } from "@config/theme";
import CHeader from "@components/CHeader";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { SafeAreaView } from "react-native";
import { FontFamily } from "@config/typography";
import { CustomIcon } from "@config/LoadIcons";
import { TouchableOpacity } from "react-native-gesture-handler";
import { Image } from "react-native";
import Iicon from "react-native-vector-icons/Ionicons";
import CButton from "@components/CButton";
import {
  addGroupMember,
  groupInfo,
  groupmemberList,
  markAsAdmin,
  removeMember,
} from "./apiCallFunction";
import { useFocusEffect } from "@react-navigation/native";
import { useSelector } from "react-redux";
import CSearch from "@components/CSearch";
import { isEmpty, isNull } from "lodash-es";
import { getUserFollowingList } from "@app/utils/commonFunction";
import { getChatSearchData } from "@screens/Chat/apiCallFunction";
import { images } from "@config/images";
import FastImage from "react-native-fast-image";
import GroupConformationModal from "@components/GroupConformationModal";
// import { translate } from "@language/Translate";

/**
 * ChatTab Component
 *
 * Main chat interface component that provides a tabbed view for different chat types.
 * Features include:
 * - Tab navigation between Personal chats and Group chats
 * - Search functionality for finding chats
 * - Block list management
 * - User subscription validation for premium features
 * - Real-time chat updates via socket connection
 *
 * @param {Object} navigation - React Navigation object for screen navigation
 */
const GroupInfo = ({ navigation, route }) => {
  const GroupText = [
    { id: 1, text: "Members" },
    { id: 2, text: "Request" },
    { id: 3, text: "Rejected" },
  ];

  // ==================== STATE MANAGEMENT ====================

  // Modal and UI states
  const [selectId, setSelectId] = useState(1);
  const groupId = route?.params?.groupId || "";
  const [groupinfo, setGroupInfo] = useState({}); // Shows/hides block list screen
  const [actionTab, setActionTab] = useState(GroupText);
  const [visible, setModalVisible] = useState(false); // Modal visibility state
  const [searchBtnData, setSearchBtnData] = useState([]); // Search button data
  const [searchChatList, setSearchChatList] = useState([]);
  const [userList, setUserList] = useState([]);
  const [addedMembers, setAddedMembers] = useState([]);
  console.log("🚀 ~ GroupInfo ~ addedMembers:", addedMembers);
  const [memberList, setMemberList] = useState([]);
  // Authentication and subscription states
  const { isCurrentPlan, activePlanData, userData } = useSelector(
    (a) => a.auth
  );
  const [typingTimeout, setTypingTimeout] = useState(null); // Debounce timer for search input
  const [loading, setLoading] = useState(false);
  const [addLoader, setAddLoader] = useState(false);
  const [removeModalVisible, setRemoveModalVisible] = useState(false);
  const [removeItemData, setRemoveitemData] = useState({});
  const [selectUserId, setSelectUserId] = useState("");
  const [markAsAdminModal, setMarkAsAdminModal] = useState({
    visible: false,
    data: {},
  });

  // ==================== DAYJS CONFIGURATION ====================
  dayjs.extend(utc); // Extend dayjs with UTC plugin for timezone handling

  const fetchGroupInfo = async (group_id) => {
    const resp = await groupInfo(group_id);
    const listResp = await groupmemberList(group_id);
    setGroupInfo(resp?.data);
    setMemberList(listResp?.data);
  };
  const getUserShareList = useCallback(async () => {
    const resp = await getUserFollowingList(userData?.user_id);
    console.log("🚀 ~ GroupInfo ~ resp:", resp?.data);
    const status =
      (await addedMembers) &&
      addedMembers.some((member) => member.user_id === item.user_id)
        ? "Remove"
        : "Add";
    const updatedData = resp?.data?.data.map((item) => ({
      ...item,
      status: status,
    }));
    console.log("🚀 ~ GroupInfo ~ updatedData:", updatedData);
    setUserList(updatedData);
  }, [userData, addedMembers]);

  useFocusEffect(
    useCallback(() => {
      fetchGroupInfo(groupId);
      getUserShareList();
    }, [groupId])
  );

  useEffect(() => {
    let fiterTab;
    if (groupinfo?.isReqRejTab) {
      fiterTab = GroupText;
    } else {
      fiterTab = GroupText.filter((item) => item.id === 1);
    }
    setActionTab(fiterTab);
    setAddedMembers(memberList);
  }, [groupinfo, memberList]);

  const renderMemberList = ({ item }) => {
    const is_Admin = memberList?.find(
      (li) => li.user_id === userData?.user_id
    )?.is_admin;
    return (
      <TouchableOpacity
        style={styles.userRow}
        onPress={() => {
          setMarkAsAdminModal({ visible: true, data: item });
        }}
      >
        {item.profile_picture !== null ? (
          <FastImage
            source={{ uri: item.profile_picture }}
            style={styles.avatar}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.avatar}
          />
        )}
        <View style={styles.info}>
          <Text style={styles.name}>{item.full_name}</Text>
          {item.is_admin === 1 ? (
            <Text style={styles.role}>{"Admin"}</Text>
          ) : null}
        </View>
        <View style={styles.actions}>
          {is_Admin === 1 && (
            <>
              {groupinfo?.groupType === "public" && (
                <TouchableOpacity style={styles.removeButton}>
                  <Iicon name="checkbox-outline" size={20} color={"#1C274C"} />
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => {
                  showRemoveConfirmation(item);
                }}
              >
                <Iicon
                  name="close-circle-outline"
                  size={22}
                  color={"#F75555"}
                />
              </TouchableOpacity>
            </>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  /**
   * Toggles member status between "Add" and "Remove"
   * Updates both userList and addedMembers states
   *
   * @param {number} userId - ID of the user to toggle
   */
  const handleToggleMember = async (userId) => {
    setAddLoader(true);
    setSelectUserId(userId);
    const resp = await addGroupMember(groupId, userId);
    if (resp?.success) {
      setAddLoader(false);
      fetchGroupInfo(groupId);
    } else {
      setAddLoader(false);
    }
  };

  const showRemoveConfirmationFromModal = (item) => {
    // Small delay to ensure modal closes before opening confirmation
    setModalVisible(false);
    setTimeout(() => {
      setRemoveModalVisible(true);
      setRemoveitemData(item);
    }, 300);
  };

  /**
   * Shows confirmation modal before removing a member
   *
   * @param {Object} item - User object to remove
   */
  const showRemoveConfirmation = (item) => {
    setRemoveModalVisible(true);
    setRemoveitemData(item);
  };

  /**
   * Removes a member from the group after confirmation
   *
   * @param {Object} item - User object to remove
   */
  const handleRemoveMember = async (item) => {
    try {
      setLoading(true);
      const resp = await removeMember(groupId, item.user_id);

      if (resp?.success) {
        // Close modal first
        setRemoveModalVisible(false);
        setRemoveitemData({});

        // Refresh group info to get updated member list
        await fetchGroupInfo(groupId);
        setLoading(false);
      }
    } catch (error) {
      console.error("🚀 ~ handleRemoveMember ~ error:", error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Removes a member from the group after confirmation
   *
   * @param {Object} item - User object to remove
   */
  const handleMakeAdmin = async (item) => {
    try {
      setLoading(true);
      const resp = await markAsAdmin(groupId, item.user_id);

      if (resp?.success) {
        // Close modal first
        setMarkAsAdminModal({ visible: false, data: {} });

        // Refresh group info to get updated member list
        await fetchGroupInfo(groupId);
        setLoading(false);
      }
    } catch (error) {
      console.error("🚀 ~ handleRemoveMember ~ error:", error);
    } finally {
      setLoading(false);
    }
  };

  const renderUserList = ({ item, index }) => {
    console.log("🚀 ~ renderUserList ~ item:", item);
    return (
      <View
        key={item.id}
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: 10,
        }}
      >
        {/* User Profile Image */}
        {item.user_dp !== null ? (
          <FastImage
            source={{ uri: item.user_dp }}
            style={styles.memberImageSize}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.memberImageSize}
          />
        )}

        {/* User Info */}
        <View style={{ marginLeft: 10 }}>
          <Text
            style={{
              fontSize: 18,
              fontFamily: FontFamily.RobotoMedium,
              marginBottom: 5,
            }}
          >
            {item.full_name}
          </Text>
        </View>

        {/* Add/Remove Button */}
        <View
          style={{
            flex: 1,
            alignItems: "flex-end",
          }}
        >
          <TouchableOpacity
            style={{
              paddingHorizontal: 10,
              paddingVertical: 6,
              borderWidth: 1,
              borderRadius: 5,
              borderColor: BaseColors.activeTab,
              backgroundColor:
                item.status === "Remove"
                  ? BaseColors.white
                  : BaseColors.activeTab,
            }}
            onPress={() => handleToggleMember(item.user_id)}
          >
            {addLoader && selectUserId === item.user_id ? (
              <ActivityIndicator
                animating
                color={BaseColors.White}
                size="small"
              />
            ) : (
              <Text
                style={{
                  color:
                    item.status === "Remove"
                      ? BaseColors.activeTab
                      : BaseColors.white,
                }}
              >
                {item.status}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  /**
   * Renders individual member card in horizontal FlatList
   * Shows user image, name, and remove button
   *
   * @param {Object} item - User object
   * @param {number} index - Array index
   */
  const renderItem = ({ item, index }) => {
    return (
      <View style={{ marginHorizontal: 5 }}>
        {item.profile_picture !== null ? (
          <FastImage
            source={{ uri: item.profile_picture }}
            style={styles.memberImageSize}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.memberImageSize}
          />
        )}

        {/* User Name */}
        <Text
          style={{
            fontSize: 14,
            fontFamily: FontFamily.RobotoRegular,
            width: 50,
          }}
          numberOfLines={1}
        >
          {item.full_name}
        </Text>
        {/* Remove Button */}
        <TouchableOpacity
          style={styles.removeBtn}
          onPress={() => {
            console.log("testing item-------", item);
            // showRemoveConfirmationFromModal(item);
          }}
        >
          <CustomIcon name="BsX" size={15} color={"#9DB2CE"} />
        </TouchableOpacity>
      </View>
    );
  };

  /**
   * Handles user search API call
   *
   * @param {string} searchBtnData - Search query (trimmed)
   * @param {number} page - Page number for pagination (default: 1)
   */
  const handleSearchChatList = async (searchBtnData, page = 1) => {
    const resp = await getChatSearchData(searchBtnData?.trim(), page);
    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      const updatedData = resp?.data?.data.map((item) => ({
        ...item,
        status:
          addedMembers &&
          addedMembers.some((member) => member.user_id === item.user_id)
            ? "Remove"
            : "Add",
      }));
      setSearchChatList({
        page: page,
        next_enable: resp?.data?.hasNextPage,
        data:
          page > 1
            ? [...searchChatList?.data, ...updatedData] // Append for pagination
            : updatedData, // Replace for new search
      });
    } else {
      setSearchChatList([]);
    }
  };

  // ==================== SEARCH FUNCTIONALITY ====================

  /**
   * Handles search input with debouncing
   * Delays API call by 1 second after user stops typing
   * Only executes if user has valid subscription plan
   *
   * @param {string} searchBtnData - The search query entered by user
   */
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      setTypingTimeout(
        setTimeout(async () => {
          // Check if user has valid subscription before allowing search
          console.log("🚀 ~ CreateGroupChat ~ isCurrentPlan:", isCurrentPlan);
          console.log("🚀 ~ CreateGroupChat ~ activePlanData:", activePlanData);
          if (
            !isEmpty(isCurrentPlan) ||
            activePlanData?.is_prime_user ||
            activePlanData?.is_free_user
          ) {
            handleSearchChatList(searchBtnData, 1);
            console.log("🚀 ~ CreateGroupChat ~ searchBtnData:", searchBtnData);
          }
        }, 1000)
      );
    },
    [typingTimeout, searchBtnData]
  );

  // ==================== SIDE EFFECTS ====================

  /**
   * Trigger search when searchBtnData changes
   * Only if user has valid subscription
   */
  useEffect(() => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (!isNull(searchBtnData)) {
        handleInputChange(searchBtnData);
      }
    }
  }, [searchBtnData]);

  // ==================== MAIN RENDER ====================
  return (
    <SafeAreaView style={styles.main}>
      {/* ==================== HEADER SECTION ==================== */}
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="Group Info"
      />
      <ScrollView style={{ flex: 1 }} behavior="padding">
        {/* Group Profile Picture Section */}
        <View style={styles.profileImgMainViewStyle}>
          {/* <View
            style={[styles.profileImgMainView, { backgroundColor: "#C4C4C4" }]}
          /> */}
          <Image
            source={{ uri: groupinfo?.image_url }}
            style={styles.profileImgMainView}
          />
        </View>

        {/* Group Name and Member Count */}
        <View style={styles.groupHeaderContainer}>
          <Text style={styles.grouoTitleText}>#{groupinfo?.group_name}</Text>
          <Text style={styles.groupmemberText}>
            Group - {groupinfo?.member_count} Members
          </Text>
        </View>

        {/* Group Information */}
        <View style={styles.groupContentContainer}>
          <View style={styles.nobContainer}>
            <Text style={styles.nobText}>Nature of business</Text>
            <Text style={styles.nobType}>
              {groupinfo?.nature_business?.map((item) => item.name).join(", ")}
            </Text>
          </View>
          <View style={styles.gropSubContainer}>
            <Text style={styles.groupText}>Group</Text>
            <Text style={styles.groupTypeText}>
              {groupinfo?.group_type === "private"
                ? "Private"
                : groupinfo?.group_type === "public"
                  ? "Public"
                  : "Auto Join"}
            </Text>
          </View>
        </View>

        {/* Group Action */}
        <View style={styles.gropAction}>
          <View style={styles.gropActionSubContainer}>
            {actionTab.map((item) => (
              <TouchableOpacity
                activeOpacity={0.8}
                key={item.id}
                style={[
                  styles.groupActionContainer,
                  {
                    backgroundColor:
                      selectId === item.id
                        ? BaseColors.activeTab
                        : BaseColors.white,
                    borderWidth: selectId === item.id ? 1 : 0,
                  },
                ]}
                onPress={() => setSelectId(item.id)}
              >
                <Text
                  style={[
                    styles.groupActionText,
                    {
                      color:
                        selectId === item.id
                          ? BaseColors.white
                          : BaseColors.lightBlack10,
                    },
                  ]}
                >
                  {item.text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <View>
            <CustomIcon name="Search-1" size={20} />
          </View>
        </View>

        {/* Group Member List */}
        <View
          style={[
            styles.groupContentContainer,
            { paddingHorizontal: 10, alignItem: "center" },
          ]}
        >
          <FlatList
            data={memberList || []}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderMemberList}
          />
        </View>
      </ScrollView>
      <View style={styles.buttonConiner}>
        <CButton
          style={styles.buttonView}
          type="outlined"
          onBtnClick={() => setModalVisible(true)}
        >
          See All
        </CButton>
        <CButton style={styles.buttonView}>Join Now</CButton>
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        animationInTiming={5000}
        animationOutTiming={5000}
        visible={visible}
        onRequestClose={() => {
          setModalVisible(!visible);
        }}
      >
        <View style={styles.ovarlayStyle}>
          <View style={styles.modalView}>
            {/* Modal Header */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={styles.modalTitleText}>{"Add Members"}</Text>

              {/* Close Button */}
              <TouchableOpacity
                style={{
                  borderWidth: 1,
                  height: 24,
                  width: 24,
                  borderRadius: 5,
                  alignItems: "center",
                  justifyContent: "center",
                }}
                activeOpacity={0.8}
                onPress={() => setModalVisible(false)}
              >
                <CustomIcon name="BsX" size={20} color={BaseColors.black} />
              </TouchableOpacity>
            </View>

            {/* Search Component */}
            <View
              style={[
                styles.searchView,
                { marginHorizontal: 0, marginBottom: 20 },
              ]}
            >
              <CSearch
                optionData={[
                  {
                    key: "block",
                    icon: "material-symbols_block",
                    text: "Block List",
                  },
                ]}
                searchBtnData={searchBtnData}
                setSearchBtnData={setSearchBtnData}
              />
            </View>

            {/* Selected Members Horizontal List */}
            <View
              style={{
                borderWidth: 0.6,
                borderRadius: 5,
                borderColor: "#8E8383",
                paddingHorizontal: 10,
                paddingVertical: 15,
                marginBottom: 20,
              }}
            >
              <FlatList
                data={addedMembers}
                renderItem={renderItem}
                horizontal={true}
                keyExtractor={(item, index) => item.user_id}
                showsHorizontalScrollIndicator={false}
              />
            </View>

            {/* Available Users Vertical List */}
            <ScrollView showsVerticalScrollIndicator={false}>
              <FlatList
                data={
                  !isEmpty(searchBtnData)
                    ? !isEmpty(searchChatList?.data) && !isNull(searchBtnData)
                      ? searchChatList?.data
                      : userList
                    : userList
                }
                renderItem={renderUserList}
              />
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Remove Member Confirmation Modal */}
      <GroupConformationModal
        visible={removeModalVisible}
        header={"Remove Member"}
        content={`Are you sure you want to remove ${removeItemData?.full_name} from the group?`}
        optionOneLabel={"Yes"}
        optionTwoLabel={"Cancel"}
        onOptionOne={() => {
          handleRemoveMember(removeItemData);
        }}
        loading={loading}
        onOptionTwo={() => setRemoveModalVisible(false)}
        onRequestClose={() => setRemoveModalVisible(false)}
      />

      <Modal
        animationType="slide"
        transparent={true}
        animationInTiming={5000}
        animationOutTiming={5000}
        visible={markAsAdminModal?.visible}
        onRequestClose={() => {
          setMarkAsAdminModal({
            ...markAsAdminModal,
            visible: false,
            data: {},
          });
        }}
      >
        <View style={styles.ovarlayStyle}>
          <View style={[styles.modalView, { height: "20%" }]}>
            {/* Modal Header */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={styles.modalTitleText}>{"Permission"}</Text>

              {/* Close Button */}
              <TouchableOpacity
                style={{
                  borderWidth: 1,
                  height: 24,
                  width: 24,
                  borderRadius: 5,
                  alignItems: "center",
                  justifyContent: "center",
                }}
                activeOpacity={0.8}
                onPress={() =>
                  setMarkAsAdminModal({
                    visible: false,
                    data: {},
                  })
                }
              >
                <CustomIcon name="BsX" size={20} color={BaseColors.black} />
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                marginVertical: 20,
              }}
            >
              {markAsAdminModal?.data?.profile_picture !== null ? (
                <FastImage
                  source={{ uri: markAsAdminModal?.data?.profile_picture }}
                  style={styles.avatar}
                />
              ) : (
                <FastImage
                  source={
                    userData?.gender === "male"
                      ? images.manAvatar
                      : images.manAvatar
                  }
                  style={styles.avatar}
                />
              )}
              <Text
                style={[
                  styles.name,
                  { fontFamily: FontFamily.RobotSemiBold, fontSize: 16 },
                ]}
              >
                {markAsAdminModal?.data?.full_name}
              </Text>
              <CButton
                onBtnClick={() => handleMakeAdmin(markAsAdminModal?.data)}
                loading={loading}
              >
                Make Group admin
              </CButton>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default GroupInfo;
