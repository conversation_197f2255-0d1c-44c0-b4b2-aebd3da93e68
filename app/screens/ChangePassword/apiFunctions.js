import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const onsubmit = async (data, setIsLoading) => {
  const finalData = {
    oldpassword: data?.old_Password,
    newpassword: data?.new_password,
  };

  try {
    const resp = await getApiData(
      BaseSetting.endpoints.changePass,
      "POST",
      finalData,
      false,
      false
    );
    return resp;
  } catch (error) {
    setIsLoading(false);
  }
};
