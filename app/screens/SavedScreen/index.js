import React, { memo, useCallback, useState } from "react";
import {
  FlatList,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import CButton from "@components/CButton";
import NoRecord from "@components/NoRecord";
// import FastImage from "react-native-fast-image";
import { getPostData, getSavedReelData } from "./apiCallFunction";
import { useFocusEffect } from "@react-navigation/native";
import { isEmpty } from "lodash-es";
import { useDispatch, useSelector } from "react-redux";
import AuthActions from "@redux/reducers/auth/actions";
import MiniLoader from "@components/MiniLoader";
import { BaseColors } from "@config/theme";
// import { CustomIcon } from "@config/LoadIcons";

const FastImage = require("react-native-fast-image");
const LottieView = require("lottie-react-native").default;
const { images } = require("@config/images");

const { setSavedPostList, setSavedReelList } = AuthActions;

const Saved = ({ navigation }) => {
  const dispatch = useDispatch();

  const [saveAndReelButton, setSaveAndReelButton] = useState("post");
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [bottomLoading, setBottomLoading] = useState(false);

  const { savedPostList, savedReelList, userData } = useSelector(
    (auth) => auth.auth
  );

  useFocusEffect(
    useCallback(() => {
      fetchPostData();
      fetchSavedReelData();
    }, [])
  );

  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={styles.imageContainer}
        onPress={() =>
          saveAndReelButton === "post"
            ? navigation.navigate("SavedPostList", {
                selectedPostIndex: index,
                screenName: "fromSaveScreen",
              })
            : navigation.navigate("ViewReelForSave", {
                selectedReelIndex: index,
              })
        }
      >
        {item?.ImageData || item?.ReelData ? (
          <FastImage
            source={{
              uri:
                saveAndReelButton === "post"
                  ? item?.ImageData[0]?.fileUrl
                  : item?.ReelData?.thumbnailData?.thumbUrl,
            }}
            style={styles.image}
            resizeMode={"cover"}
          />
        ) : null}

        {saveAndReelButton === "post" ? null : (
          <View style={styles.addReelIcon}>
            {/* <CustomIcon
              name={"reel-outline"}
              size={20}
              color={BaseColors.activeTab}
            /> */}
            <LottieView
              autoSize={true}
              source={images.reelAnim}
              autoPlay={true}
              loop
              style={styles.reelAnimView}
            />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Fetch Saved Posts Data
  const fetchPostData = async (page = 1, bottomLoader = false) => {
    if (bottomLoader) {
      setBottomLoading(true);
    } else if (bottomLoader) {
      setLoading(true);
    }
    const resp = await getPostData(page);

    if (resp?.data?.success && resp?.data?.data && !isEmpty(resp?.data?.data)) {
      dispatch(
        setSavedPostList({
          screenName: "fromSaveScreen",
          page: page,
          next_enable: resp?.data?.hasNextPage || false,
          data:
            page > 1
              ? [...savedPostList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      setBottomLoading(false);
      setLoading(false);
    } else {
      setBottomLoading(false);
      setLoading(false);
      dispatch(setSavedPostList([]));
    }
  };

  // Fetch Saved Reels Data
  const fetchSavedReelData = async (page = 1, bottomLoader = false) => {
    if (isEmpty(savedReelList)) {
      setLoading(true);
    } else if (bottomLoader) {
      setBottomLoading(true);
    }
    const resp = await getSavedReelData(page, userData?.user_id);
    if (resp?.data?.success && resp?.data?.data && !isEmpty(resp?.data?.data)) {
      dispatch(
        setSavedReelList({
          page: page,
          next_enable: resp?.data?.hasNextPage || false,
          data:
            page > 1
              ? [...savedReelList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      setLoading(false);
    } else {
      dispatch(setSavedReelList([]));
      setLoading(false);
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    fetchPostData();
    fetchSavedReelData();
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    let bottomLoader = true;

    if (
      isCloseToBottom &&
      savedPostList?.next_enable &&
      saveAndReelButton === "post"
    ) {
      fetchPostData(savedPostList?.page + 1, bottomLoader);
    } else if (
      isCloseToBottom &&
      savedReelList?.next_enable &&
      saveAndReelButton !== "post"
    ) {
      fetchSavedReelData(savedReelList?.page + 1, bottomLoader);
    }
  };
  const handleLoadMore = () => {
    // Check if more data can be loaded and if loading is not already in progress
    if (
      savedPostList?.next_enable &&
      !bottomLoading &&
      savedPostList?.screenName === "fromSaveScreen"
    ) {
      fetchPostData(savedPostList.page + 1, true);
    }
  };

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() =>
          navigation.navigate("Home", {
            screen: "HomeTab",
            params: { fromNavBar: savedPostList?.data?.length || [] },
          })
        }
        headingTitle="saved"
      />

      {/* Heder Button */}
      <View style={styles.saveCounter}>
        <View style={styles.mainButtonView}>
          <View style={styles.buttonView}>
            <CButton
              type={saveAndReelButton === "post" ? null : "outlined"}
              containerStyle={styles.buttonCStyle}
              txtSty={styles.btnText}
              onBtnClick={() => setSaveAndReelButton("post")}
            >
              Saved Post
            </CButton>
          </View>
          <View style={styles.buttonView}>
            <CButton
              type={saveAndReelButton === "post" ? "outlined" : null}
              containerStyle={styles.buttonCStyle}
              txtSty={styles.btnText}
              onBtnClick={() => setSaveAndReelButton("reel")}
            >
              Saved Reels
            </CButton>
          </View>
        </View>
        <ScrollView
          onScroll={handleScroll}
          scrollEventThrottle={0.5}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.activeTab]} // Customize refresh indicator color
              tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {loading ? (
            <View
              style={{
                flex: 1,
                paddingHorizontal: 14,
              }}
            >
              <MiniLoader size="medium" />
            </View>
          ) : (
            <View style={styles.mainPostView}>
              <FlatList
                data={
                  saveAndReelButton === "post"
                    ? savedPostList?.data || []
                    : savedReelList?.data || []
                }
                renderItem={renderItem}
                keyExtractor={(item, index) => index.toString()}
                numColumns={3} // Set the number of columns for grid layout
                ListEmptyComponent={
                  <View style={styles.centerMain}>
                    <NoRecord title={"noRecordFound"} />
                  </View>
                }
                showsVerticalScrollIndicator={false}
                onEndReached={handleLoadMore} // Call handleLoadMore when end of list is reached
                onEndReachedThreshold={0.1}
              />
              {bottomLoading ? (
                <View>
                  <MiniLoader size="small" />
                </View>
              ) : null}
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};
export default memo(Saved);
