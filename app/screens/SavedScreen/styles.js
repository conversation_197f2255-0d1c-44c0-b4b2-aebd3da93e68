import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor:
      Platform.OS === "ios" ? BaseColors.white : BaseColors.white,
    // marginBottom: 15,
  },
  centerMain: {
    flex: 1,
    paddingHorizontal: 14,
    height: Dimensions.get("window").height / 1.5 - 85,
  },
  postListMain: {
    // marginBottom: 65,
  },
  trailMainView: {
    borderWidth: 1,
    marginHorizontal: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BaseColors.white,
    padding: 10,
    borderRadius: 8,
    borderColor: BaseColors.activeTab,
    marginVertical: 4,
    marginBottom: 20,
  },
  trailTextStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 14,
    color: BaseColors.activeTab,
  },
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  saveCounter: {
    marginHorizontal: 20,
    flex: 1,
  },
  addReelIcon: {
    paddingRight: 5,
    paddingTop: 5,
    position: "absolute",
    right: -15,
    top: -15,
  },
  mainButtonView: {
    flexDirection: "row",
    gap: 10,
    justifyContent: "center",
  },
  buttonView: {
    width: "50%",
  },
  buttonCStyle: {
    height: 40,
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  btnText: {
    fontSize: 16,
    fontFamily: FontFamily.InterMedium,
  },
  mainPostView: {
    marginTop: 20,
    marginBottom: 120,
  },
  imageContainer: {
    height: Dimensions.get("window").height / 5,
    width: "31.4%",
    borderRadius: 10,
    marginRight: 10,
    marginBottom: 10,
    overflow: "hidden",
    elevation: 6,
    position: "relative",
  },
  image: {
    width: "100%",
    height: Dimensions.get("window").height / 5,
  },
  reelAnimView: {
    height: 60,
    width: 60,
  },
});

export default styles;
