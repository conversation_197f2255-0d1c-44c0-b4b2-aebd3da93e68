import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import {
  FlatList,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  View,
} from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { useDispatch, useSelector } from "react-redux";
import PostComponent from "@components/PostComponent/PostComponent";
import NoRecord from "@components/NoRecord";
import AuthActions from "@redux/reducers/auth/actions";
import {
  ClickedLikeButton,
  ClickedSaveButton,
  getChatSearchData,
  getPostData,
} from "./apiCallFunction";
import {
  getUserFollowList,
  handleFollowToggle,
} from "@app/utils/commonFunction";
import { useFocusEffect } from "@react-navigation/native";
import Toast from "react-native-simple-toast";
import { isEmpty } from "lodash-es";
import MiniLoader from "@components/MiniLoader";
import PurChasePlanModal from "@components/PurchasePlanModal";
import {
  getCountryData,
  getCountryStateData,
  natureOfBusinessData,
} from "@screens/SingUpWithCompanyDetail/apiFunctions";

const { setSavedPostList, setUserFollowList, setCreatedPostList } = AuthActions;

const renderItem = (
  item,
  index,
  handleLike,
  setIsLikeAnim,
  isLikeAnim,
  handleToFollow,
  clickSaveIcon,
  selectedLikeIndex,
  setSelectedLikeIndex,
  setFollowLodging,
  followLodging,
  navigation,
  savedPostList,
  bottomLoading,
  notificationType,
  commentPostId,
  countryList,
  stateList,
  natureOfBusinessList
) => {
  return (
    <View
      style={{
        marginBottom: index === savedPostList?.data?.length - 1 ? 65 : 0,
      }}
    >
      <PostComponent
        item={item}
        index={index}
        getUserShareList={[]}
        handleLike={handleLike}
        setIsLikeAnim={setIsLikeAnim}
        isLikeAnim={isLikeAnim}
        handleToFollow={handleToFollow}
        handleSaveButton={clickSaveIcon}
        selectedLikeIndex={selectedLikeIndex}
        setSelectedLikeIndex={setSelectedLikeIndex}
        setFollowLodging={setFollowLodging}
        followLodging={followLodging}
        navigation={navigation}
        commentModelVisible={notificationType === "comment" ? true : false}
        commentPostId={commentPostId}
        handleToPressBoost={(e, nav) =>
          handleToPressBoost(
            e,
            nav,
            countryList,
            stateList,
            natureOfBusinessList
          )
        }
      />
      {bottomLoading && index === savedPostList?.data?.length - 1 ? (
        <MiniLoader />
      ) : null}
    </View>
  );
};

const handleToPressBoost = (
  e,
  nav,
  countryList,
  stateList,
  natureOfBusinessList
) => {
  const data = {
    files: e?.ImageData,
    SelectedCountry: e?.country?.split(",").map(Number),
    SelectedState: e?.state?.split(",").map(Number),
    Audience: e?.audience?.split(",").map(Number),
  };

  nav.navigate("CreateBoostPost", {
    data,
    countryData: countryList,
    stateData: stateList,
    natureOfBusinessDada: natureOfBusinessList,
    type: "post",
    apiRes: { post_id: e.post_id },
  });
};
const SavedPostList = ({ navigation, route }) => {
  const dispatch = useDispatch();
  // state
  const [isLikeAnim, setIsLikeAnim] = useState(false);
  const [selectedLikeIndex, setSelectedLikeIndex] = useState(0);
  const [bottomLoading, setBottomLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [followLodging, setFollowLodging] = useState({
    loader: false,
    selectedIndex: null,
  });

  const [countryList, setCountryData] = useState([]);
  const [stateList, setSelectedCountryState] = useState([]);
  const [natureOfBusinessList, setNatureOfBusinessData] = useState([]);

  const flatListRef = useRef(null);
  const selectedPostIndex = route?.params?.selectedPostIndex || 0;

  const screenName = route?.params?.screenName || "";
  const notificationType = route?.params?.notificationType || "";
  const commentPostId = route?.params?.commentPostId || "";

  const { savedPostList, createdPostList, isCurrentPlan, userData, activePlanData } =
    useSelector((auth) => auth.auth);

  useFocusEffect(
    useCallback(() => {
      getUserShareList();
      fetchCountryData();
      fetchCountryStateData(101);
      fetchNatureOfBusinessData();
    }, [])
  );
  const fetchCountryData = async (e) => {
    const resp = await getCountryData(e);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setCountryData(updatedData);
  };
  const fetchCountryStateData = async (id) => {
    const resp = await getCountryStateData(id);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setSelectedCountryState(updatedData);
  };

  // fetch Nature Of Business Data and manipulate response
  const fetchNatureOfBusinessData = async () => {
    const resp = await natureOfBusinessData();

    let updatedData = resp?.data?.data?.map((val) => {
      return {
        ...val,
        value: val.id,
      };
    });

    setNatureOfBusinessData(updatedData);
  };
  const getUserShareList = useCallback(async () => {
    const resp = await getUserFollowList(userData?.user_id);
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(setUserFollowList(resp?.data));
    } else {
      console.log("Some thing went wrong");
    }
  }, [userData]);

  // Function when we like like button
  const handleLike = async (post_id) => {
    if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
      

      const resp = await ClickedLikeButton(post_id);
      if (resp !== undefined) {
        if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
          const updatedPosts = savedPostList?.data?.map((post) => {
            if (post.post_id === post_id) {
              return {
                ...post,
                like_counts: post.is_liked
                  ? post.like_counts - 1
                  : post.like_counts + 1,
                is_liked: post.is_liked ? false : true,
              };
            }
            return post;
          });
    
          dispatch(
            setSavedPostList({
              ...savedPostList,
              data: updatedPosts,
            })
          );
    
          setIsLikeAnim(true);
        } else {
          // const updatedPosts = savedPostList?.data?.map((post) => {
          //   if (post.post_id === post_id) {
          //     return {
          //       ...post,
          //       like_counts: post.is_liked
          //         ? post.like_counts - 1
          //         : post.like_counts + 1,
          //       is_liked: post.is_liked ? false : true,
          //     };
          //   }
          //   return post;
          // });
          // dispatch(
          //   setSavedPostList({
          //     ...savedPostList,
          //     data: updatedPosts,
          //   })
          // );

          Toast.show(resp?.data?.message || "No Data found");
        }
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  // Handle Follow function
  const handleToFollow = async (userId, selected_index, type) => {
    if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
      setFollowLodging({
        loader: true,
        selectedIndex: selected_index,
      });
      if (type === "remove") {
        userData.followings = userData.followings - 1;
      } else {
        userData.followings = userData.followings + 1;
      }
      const resp = await handleFollowToggle(userId);
      const updatedPosts = savedPostList?.data?.map((post) => {
        if (post.user_id === userId) {
          return {
            ...post,
            is_followed: post.is_followed ? false : true,
          };
        }
        return post;
      });

      dispatch(
        setSavedPostList({
          ...savedPostList,
          data: updatedPosts,
        })
      );
      setFollowLodging({
        loader: false,
        selectedIndex: selected_index,
      });
      if (resp?.data?.success) {
        getUserShareList();
        setFollowLodging({
          loader: false,
          selectedIndex: selected_index,
        });
      } else {
        const updatedPosts = savedPostList?.data?.map((post) => {
          if (post.user_id === userId) {
            return {
              ...post,
              is_followed: post.is_followed ? false : true,
            };
          }
          return post;
        });
        if (type === "remove") {
          userData.followings = userData.followings + 1;
        } else {
          userData.followings = userData.followings - 1;
        }

        dispatch(
          setSavedPostList({
            ...savedPostList,
            data: updatedPosts,
          })
        );
        setFollowLodging({
          loader: false,
          selectedIndex: selected_index,
        });
        Toast.show(resp?.data?.message || "No Data found");
        console.log("Something went wrong please try again");
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  const UpdateSavedRedux = (post_id) => {
    // Remove Data from saved List
    const updatedPosts = savedPostList?.data?.filter(
      (post) => post.post_id !== post_id
    );

    dispatch(
      setSavedPostList({
        ...savedPostList,
        data: updatedPosts,
      })
    );

    // Update Post & Reel List
    const updatedPostsData = createdPostList?.data?.map((post) => {
      if (post?.post_id === post_id || post?.reel_id === post_id) {
        return {
          ...post,
          is_saved: post.is_saved ? false : true,
        };
      }
      return post;
    });

    dispatch(
      setCreatedPostList({
        ...createdPostList,
        data: updatedPostsData,
      })
    );
  };

  const UpdateCommentRedux = (post_id) => {
    const updatedPosts = savedPostList?.data?.map((post) => {
      if (post.post_id === post_id || post.reel_id === post_id) {
        return {
          ...post,
          is_saved: post.is_saved ? false : true,
        };
      }
      return post;
    });

    dispatch(
      setSavedPostList({
        ...savedPostList,
        data: updatedPosts,
      })
    );
  };

  // save unSave toggle button when come from chat screen or profile
  const handleSaveButton = async (post_Id, postType) => {
    if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
      UpdateCommentRedux(post_Id);
      const resp = await ClickedSaveButton(post_Id, postType);
      if (resp?.data?.success && resp !== undefined) {
        console.log(resp?.data?.message || "Saved");
      } else {
        Toast.show(resp?.data?.message || "No Data found");
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  // save unSave toggle button when come from save screen
  const handleSavedRemove = async (post_Id) => {
    UpdateSavedRedux(post_Id);
    const resp = await ClickedSaveButton(post_Id);
    if (resp?.data?.success && resp !== undefined) {
      console.log(resp?.data?.message || "Saved");
    } else {
      Toast.show(resp?.data?.message || "No Data found");
    }
  };

  const clickSaveIcon = (post_Id, postType) => {
    if (screenName === "fromSaveScreen") {
      handleSavedRemove(post_Id, postType);
    } else {
      handleSaveButton(post_Id, postType);
    }
  };

  useEffect(() => {
    if (!isEmpty(savedPostList?.data)) {
      if (flatListRef.current) {
        // flatListRef?.current?.scrollToIndex({ index: Number(selectedPostIndex) });
      }
    }
  }, [selectedPostIndex]);

  const getItemLayout = (data, index) => ({
    length: 100,
    offset: screenName === "fromSaveScreen" ? 320 * index : 500 * index,
  });

  const fetchSearchScreenData = async (searchBtnData = "", page = 1) => {
    setBottomLoading(true);
    const resp = await getChatSearchData(searchBtnData?.trim(), page, "for_me");
    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      let allPostList = resp?.data?.data?.filter(
        (item) => item?.type === "post"
      );

      const mergeArrays = (arr1, arr2) => {
        const combinedArray = [...arr1, ...arr2];
        if (page === 1 || page === 2) {
          const uniqueIds = new Set();
          const uniqueArray = combinedArray.filter((item) => {
            if (uniqueIds.has(item.id)) {
              return false;
            } else {
              uniqueIds.add(item.id);
              return true;
            }
          });
          return uniqueArray;
        } else {
          return combinedArray;
        }
      };

      const newPostList = mergeArrays(savedPostList?.data, allPostList);

      if (!isEmpty(newPostList)) {
        dispatch(
          setSavedPostList({
            ...savedPostList,
            page: page,
            next_enable: resp?.data?.pagination?.hasNextPage || false,
            data: newPostList,
          })
        );
        setBottomLoading(false);
      }
      setBottomLoading(false);
    } else {
      setBottomLoading(false);
      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  useEffect(() => {
    if (screenName === "fromSearchScreen") {
      fetchSearchScreenData();
    }
  }, []);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // fetchPostData();
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  return (
    <SafeAreaView style={styles.main}>
      {/* Header */}
      <CHeader
        handleBackButton={
          screenName === "fromSaveScreen"
            ? () => navigation.navigate("Saved")
            : () => navigation.goBack()
        }
        headingTitle="postTxt"
      />

      {/* Post Start */}
      <View style={styles.postListMain}>
        <FlatList
          ref={flatListRef}
          data={savedPostList?.data || []}
          keyboardShouldPersistTaps="handled"
          renderItem={({ item, index }) =>
            renderItem(
              item,
              index,
              handleLike,
              setIsLikeAnim,
              isLikeAnim,
              handleToFollow,
              clickSaveIcon,
              selectedLikeIndex,
              setSelectedLikeIndex,
              setFollowLodging,
              followLodging,
              navigation,
              savedPostList,
              bottomLoading,
              notificationType,
              commentPostId,
              countryList,
              stateList,
              natureOfBusinessList
            )
          }
          getItemLayout={getItemLayout}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item, index) => index.toString()}
          refreshing={refreshing}
          onRefresh={onRefresh}
          viewabilityConfig={{
            viewAreaCoveragePercentThreshold: 50,
          }}
          onEndReachedThreshold={3}
          onEndReached={() => {
            if (
              savedPostList?.next_enable &&
              !bottomLoading &&
              screenName === "fromSearchScreen"
            ) {
              fetchSearchScreenData("", savedPostList?.page + 1, true);
            }
          }}
          ListEmptyComponent={
            <View style={styles.centerMain}>
              <NoRecord title={"noRecordFound"} />
            </View>
          }
          style={{ paddingBottom: 300 }}
          legacyImplementation
        />
      </View>
      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};

export default memo(SavedPostList);
