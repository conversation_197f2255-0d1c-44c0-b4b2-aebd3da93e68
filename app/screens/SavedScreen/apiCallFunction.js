import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";

// Fetch Saved Post  Data
export const getPostData = async (page = 1) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.savedPostList}?pageSize=16&page=${page}`,
      "GET"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Fetch Saved Reel Data
export const getSavedReelData = async (page, userID) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getReelsList}/${userID}?slug=saved-reel&pageSize=10&page=${page}`,
      "GET"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// like button API
export const ClickedLikeButton = async (PostId) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.likeDislike}${PostId}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};

export const ClickedSaveButton = async (PostId) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.saveUnsavedPost}${PostId}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};

// Search  List
export const getChatSearchData = async (searchBtnData, page, searchType) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.searchList}?slug=${searchType}&page=${page}&pageSize=5&search=${searchBtnData}`,
      "GET"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};
