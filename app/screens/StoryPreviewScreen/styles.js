import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";
const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  headerMainView: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 18,
  },
  cancelTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  headingTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: "#322F2F",
  },
  storyPreviewImage: {
    height: Dimensions.get("window").height / 1.7,
    width: Dimensions.get("window").width - 40,
    resizeMode: "contain",
  },
  buttonComponent: {
    position: "absolute",
    bottom: 12,
    alignSelf: "center",
    width: "90%",
  },
});

export default styles;
