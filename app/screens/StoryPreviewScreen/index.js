// Importing necessary modules and components
import React, { memo, useCallback, useMemo, useState } from "react";
import {
  View,
  SafeAreaView,
  ImageBackground,
  Platform,
  Dimensions,
} from "react-native";
import StoryShareModal from "@components/StoryShareModal";
import styles from "./styles";
// import Video from "react-native-video";
import Loader from "@components/Loader";
import CButton from "@components/CButton";
import { translate } from "../../lang/Translate";
import CHeader from "@components/CHeader";
import { useFocusEffect } from "@react-navigation/native";
import AutoHeightImage from "react-native-auto-height-image";
import FastImage from "react-native-fast-image";

const Video = require("react-native-video").default;

// Functional component for StoryPreview screen
const StoryPreview = ({ navigation, route }) => {
  // Extracting image from route parameters
  const { image, video_duration, sendedFile, type } = route?.params ?? {};
  const WIDTH = Dimensions.get("window").width;
  // const HEIGHT = Dimensions.get("window").height;

  // State for controlling modal visibility
  const [visibleModal, setIsModalVisible] = useState(false);
  const [isLoader, setIsLoader] = useState(false);
  const [videoDuration, setVideoDuration] = useState(0);

  // Memoizing the modal visibility state
  const visibleModalMemo = useMemo(() => visibleModal, [visibleModal]);
  const isLoaderMemo = useMemo(() => isLoader, [isLoader]);
  const videoDurationMemo = useMemo(() => videoDuration, [videoDuration]);

  // Function to handle closing the modal and navigating back
  const closeModalAndNavigateBack = useCallback(() => {
    setIsModalVisible(false);
    if (image?.thubnails !== null) {
      navigation.goBack();
    }
  }, [navigation, visibleModalMemo]);

  useFocusEffect(
    useCallback(() => {
      setVideoDuration(String(video_duration?.durationInSec));
    }, [image])
  );

  // Rendering the StoryPreview screen
  return (
    <SafeAreaView style={styles.mainView}>
      {/* Rendering the header */}
      <CHeader
        headingTitle="add_story_text"
        handleBackButton={() => navigation.goBack()}
      />

      <ImageBackground
        style={{ flex: 1 }}
        source={{
          uri: image?.thubnails !== null ? image?.thubnails : image?.uri,
        }}
        blurRadius={12}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            marginBottom: 50,
          }}
        >
          {/* Rendering the Selected Video and Image */}
          {/* <Image source={image} style={[styles.storyPreviewImage]} /> */}
          {image?.thubnails !== null ? (
            <Video
              source={{
                uri: sendedFile.uri,
              }}
              minLoadRetryCount={6}
              resizeMode="contain"
              style={styles.storyPreviewImage}
              preload="auto"
              onBuffer={(w) => {
                if (Platform.OS === "android") {
                  setIsLoader(true);
                }
              }}
              onLoad={(q) => {
                setIsLoader(false);
              }}
              onLoadEnd={(r) => {
                setIsLoader(false);
                onStart("finish");
              }}
              onError={() => Toast.show("Failed to load video")}
            />
          ) : (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <AutoHeightImage
                width={WIDTH}
                source={{
                  uri: image?.uri,
                  priority: FastImage.priority.normal,
                }}
              />
            </View>
          )}
        </View>
      </ImageBackground>

      {/* Rendering the final shared modal */}
      <StoryShareModal
        visible={visibleModalMemo}
        image={image}
        setModalVisible={closeModalAndNavigateBack}
        video_duration={videoDurationMemo}
        navigation={navigation}
        storyType={
          image?.thubnails !== null || type === "reels" ? "video" : "image"
        }
        type={type}
      />

      {/* setting up the Loader */}
      <Loader loading={isLoader} />

      {/* Button Component */}
      <View style={styles.buttonComponent}>
        <CButton onBtnClick={() => setIsModalVisible(true)}>
          {translate("nextText")}
        </CButton>
      </View>
    </SafeAreaView>
  );
};

// Memoizing the StoryPreview component for performance optimization
export default memo(StoryPreview);
