import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const onSubmit = async (data) => {
  const finalData = {
    type: data?.singUpType,
  };
  if (data?.singUpType === "social") {
    finalData["idToken"] = data?.idToken;
    finalData["social_connection"] = data?.social_connection;
    if (data?.social_connection === "apple") {
      if (data?.tname !== undefined) {
        finalData["tname"] = data?.tname;
      }
    }
  } else if (data?.singUpType === "phone") {
    finalData["phone_number"] = data?.text;
    finalData["phone_code"] = data?.countryCode;
  } else {
    finalData["email"] = data?.text;
  }
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.signUp,
      "POST",
      finalData,
      false,
      false
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ onSubmit ~ error:", error);
  }
};
