import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import {
  Image,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./styles";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import CInput from "@components/TextInput";
import Checkbox from "@components/Checkbox";
import CButton from "@components/CButton";
import { onSubmit } from "./apiFunctions";
import {
  getMobileNumberLength,
  mobileValidation,
} from "@app/utils/commonFunction";
import Toast from "react-native-simple-toast";
import authAction from "@redux/reducers/auth/actions";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch } from "react-redux";

const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;
const { images } = require("@config/images");
const FastImage = require("react-native-fast-image");

const { logOut } = authAction;

// Form Schema
const signUpWithEmailBasicSchema = yup.object().shape({
  text: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  checkBox: yup.bool().default(false),
  singUpType: yup.string().default("email"),
});
const signUpMobileBasicSchema = yup.object().shape({
  text: yup
    .string()
    .required("enterMobileNumber")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  checkBox: yup.bool().default(false),
  singUpType: yup.string().default("phone"),
  countryCode: yup.string().default("91"),
});

const SignUpWithMobileAndEmail = ({ navigation, route }) => {
  // Route
  const signUpType = route?.params?.signUpType;

  // Redux variable
  const dispatch = useDispatch();
  //   Ref
  const mobileText = useRef(null);

  //   State's
  const [isCheck, setIsCheck] = useState(false);
  const [mobileNoLength, setMobileLength] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const [countryCode, setCountryCode] = useState("+91");

  // Memo's
  const mobileNoLengthMemo = useMemo(() => mobileNoLength, [mobileNoLength]);
  const isCheckMemo = useMemo(() => isCheck, [isCheck]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  // Form Variable
  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(
      signUpType === "email"
        ? signUpWithEmailBasicSchema
        : signUpType === "mobile"
          ? signUpMobileBasicSchema
          : null
    ),
  });

  // handle to SingUp

  const handleToSingUp = useCallback(
    async (data) => {
      let isValid = true;
      const mobileNumberValidation = await mobileValidation(
        data?.text,
        mobileNoLengthMemo
      );

      if (signUpType === "mobile") {
        if (!mobileNumberValidation?.isValid) {
          isValid = false;
          setError("text", {
            message: mobileNumberValidation?.validMessage,
          });
        } else {
          isValid = true;
        }
      }
      if (isValid) {
        setIsLoading(true);
        const respData = await onSubmit(data);
        if (respData !== undefined) {
          if (respData?.data?.success) {
            navigation.navigate("ForgotOtpScreen", {
              data: { data },
              signUpType: "yes",
            });
            setIsLoading(false);
          } else {
            if (respData?.data?.isExisting) {
              setError("text", { message: respData?.data?.message });
            } else {
              Toast.show(respData?.data?.message);
            }

            setIsLoading(false);
          }
        }
      }
      setIsLoading(false);
    },
    [isLoadingMemo]
  );

  useFocusEffect(
    useCallback(() => {
      dispatch(logOut());
    }, [])
  );

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader handleBackButton={() => navigation.goBack()} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : null}
        style={styles.KeyboardAvoidingViewStyle}
      >
        <ScrollView style={styles.scrollViewStyle}>
          <Animated.View entering={FadeIn.duration(400)}>
            <FastImage
              source={images.footMainLogo}
              style={styles.mainLogoStyle}
              resizeMode="contain"
            />
          </Animated.View>
          {/* Heading Text */}
          <Animated.Text style={styles.headingTextStyle}>
            {signUpType === "email"
              ? translate("signUpWithEmail")
              : signUpType === "mobile"
                ? translate("signUpWithMobileNo")
                : null}
          </Animated.Text>

          <Animated.Text style={styles.titleTextStyle} numberOfLines={3}>
            {signUpType === "email"
              ? translate("registerWithEmailText")
              : signUpType === "mobile"
                ? translate("registerWithMobileNoText")
                : null}
          </Animated.Text>
          <Animated.View style={styles.textinputCommonView}>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              name="text"
              render={({ field: { onChange, value } }) => (
                <CInput
                  reference={mobileText}
                  placeholderText={
                    signUpType === "mobile"
                      ? translate("MobileNumberPlaceHolder")
                      : signUpType === "email"
                        ? translate("EmailText")
                        : null
                  }
                  // onSubmit={handleSubmit(handleToSingUp)}
                  returnKeyType="next"
                  inputType={signUpType === "mobile" ? "mobile" : "normal"}
                  keyBoardType={
                    signUpType === "mobile" ? "number-pad" : "email-address"
                  }
                  maxLength={
                    signUpType === "mobile" ? mobileNoLengthMemo : null
                  }
                  value={value}
                  onChange={(e) => {
                    const sanitizedText = e.replace(/\s+/g, "");
                    onChange(sanitizedText);
                  }}
                  selectedCountryCode={(e) => {
                    const country_length = getMobileNumberLength(
                      e?.country_code
                    );
                    setMobileLength(country_length);
                    setValue("mobileNo", undefined);
                    const yourData = e?.dial_code?.split("+");

                    setCountryCode(yourData);
                    setValue("countryCode", yourData[1]);
                  }}
                  isError={errors?.text}
                  isErrorMsg={errors?.text?.message}
                  isCerBtnText={"Login"}
                  onCerBtnPress={() => {
                    navigation.replace("EmailWithLogin", {
                      loginType: signUpType,
                    });
                  }}
                  countryCodeValue={
                    getValues("countryCode")
                      ? `+${getValues("countryCode")}`
                      : countryCode
                  }
                />
              )}
            />
          </Animated.View>
        </ScrollView>
        <Animated.View style={styles.checkBoxMainView} entering={FadeIn}>
          <Controller
            control={control}
            rules={{
              required: true,
            }}
            name="checkBox"
            render={({ field: { onChange, value } }) => (
              <View style={styles.cmsMainView}>
                <Checkbox
                  isChecked={isCheckMemo}
                  toggleCheckbox={async (e) => {
                    await setIsCheck(!isCheckMemo);
                    onChange(!isCheckMemo);
                  }}
                />
                <View style={styles.checkboxConditionView}>
                  <Text style={styles.continue}>
                    <View>
                      <Text style={styles.and}>By clicking on you agree</Text>
                    </View>
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("CMS", {
                          type: "terms",
                        })
                      }
                    >
                      <Text style={styles.term}> Terms & Conditions</Text>
                    </TouchableOpacity>
                    <View>
                      <Text style={styles.and}> and </Text>
                    </View>
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("CMS", {
                          type: "privacy",
                        })
                      }
                    >
                      <Text style={styles.privacy}>Privacy Policy.</Text>
                    </TouchableOpacity>
                  </Text>
                </View>
              </View>
            )}
          />
        </Animated.View>
        <Animated.View style={styles.buttonMainView} entering={FadeIn}>
          <CButton
            onBtnClick={handleSubmit((e) => handleToSingUp(e))}
            loading={isLoadingMemo}
            disabled={!isCheckMemo}
          >
            {translate("GetOtpText")}
          </CButton>
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
export default memo(SignUpWithMobileAndEmail);
