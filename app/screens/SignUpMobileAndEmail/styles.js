import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Platform, StyleSheet } from "react-native";

export default styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainLogoStyle: {
    height: 120,
    width: 120,

    alignSelf: "center",
  },
  KeyboardAvoidingViewStyle: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  scrollViewStyle: {
    flex: 1,
  },
  headingTextStyle: {
    fontSize: 30,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
    marginTop: 45,
    textAlign: "center",
    marginHorizontal: 20,
  },
  titleTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
    textAlign: "center",
    marginTop: 6,
    marginHorizontal: 20,
  },
  textinputCommonView: {
    flex: 1,
    marginVertical: 40,
    marginHorizontal: 20,
  },
  checkBoxMainView: {
    marginHorizontal: 20,
  },
  buttonMainView: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: Platform.OS === "ios" ? 0 : 20,
  },
  cmsMainView: {
    flexDirection: "row",
    // alignItems: "center",
    marginHorizontal: 10,

    justifyContent: "center",
  },
  and: {
    fontSize: 14,
    fontWeight: "500",
    letterSpacing: 0.8,
    color: BaseColors.gray2,
    fontFamily: FontFamily.Barlow,
  },
  term: {
    color: BaseColors.primary3,
    textDecorationLine: "underline",
    textTransform: "capitalize",
    fontSize: 14,
    fontWeight: "500",
  },
  privacy: {
    color: BaseColors.primary3,
    textDecorationLine: "underline",
    fontSize: 14,
    fontWeight: "500",
    textTransform: "capitalize",
  },
});
