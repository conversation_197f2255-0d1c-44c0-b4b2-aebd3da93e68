/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Animated,
  Easing,
  Text,
  Image,
  SafeAreaView,
  ImageBackground,
} from "react-native";
// import FastImage from "react-native-fast-image";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import { images } from "@config/images";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { useDispatch, useSelector } from "react-redux";
import { hasPermission } from "@screens/AddStoryScreen/functions";
import dayjs from "dayjs";
import AuthActions from "@redux/reducers/auth/actions";

const FastImage = require("react-native-fast-image");
const { images } = require("@config/images");

const { setPurchaseModal } = AuthActions;
const SplashScreen = ({ navigation }) => {
  // Redux Variable
  const { isIntro, purchaseModal } = useSelector((s) => s.auth);
  const dispatch = useDispatch();
  // Animation Variable
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation for scaling down the logo
    Animated.timing(scaleAnim, {
      toValue: 0.5,
      duration: 300,
      easing: Easing.linear,
      useNativeDriver: true,
    }).start();

    // Fade in animation for texts and sponsor image
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000, // Adjust duration as needed
      easing: Easing.linear,
      useNativeDriver: true,
    }).start();

    setTimeout(() => {
      handleToNavigation();
    }, 2000);
  }, [scaleAnim, fadeAnim]);

  const handleToNavigation = async () => {
    await hasPermission();
    AsyncStorage.getItem("token")
      .then(async (value) => {
        if (value === null) {
          if (isIntro) {
            navigation.replace("IntroScreen");
          } else {
            navigation.replace("Auth");
          }
        } else {
          dispatch(
            setPurchaseModal({
              ...purchaseModal,
              appStartTime: dayjs(),
            })
          );
          navigation.replace("HomeScreen");
        }
      })
      .catch((s) => {});
  };

  return (
    <SafeAreaView style={styles.mainView}>
      <View style={styles.contentView}>
        <Animated.View style={{ opacity: fadeAnim }}>
          <Text style={styles.welcomeText}>{translate("welcomeToText")}</Text>
        </Animated.View>
        <View>
          <Animated.View
            style={{
              transform: [{ scale: scaleAnim }],
            }}
          >
            <FastImage
              source={images.footMainLogo}
              style={styles.footLogoStyle}
              resizeMode="contain"
            />
          </Animated.View>
        </View>
        <Animated.View style={{ opacity: fadeAnim }}>
          <Text style={styles.descTextStyle}>
            {translate("splashScreenText")}
          </Text>
        </Animated.View>
      </View>
      <Animated.View
        style={[styles.sponsorIconStyleView, { opacity: fadeAnim }]}
      >
        <Text style={styles.sponsorTextStyle}>{translate("powerByText")}</Text>
        <Image
          source={images.sponsorImage}
          style={styles.sponsorImageStyle}
          resizeMode="contain"
        />
      </Animated.View>
    </SafeAreaView>
  );
};
export default React.memo(SplashScreen);
