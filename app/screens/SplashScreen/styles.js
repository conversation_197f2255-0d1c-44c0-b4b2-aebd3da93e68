import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BaseColors.white,
  },
  SplashScreenBackgroundStyle: {
    flex: 1,
    // justifyContent: "center",
    alignItems: "center",
    height: Dimensions.get("window").height,
    width: Dimensions.get("window").width,
  },
  contentView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  welcomeText: {
    fontSize: 32,
    fontFamily: FontFamily.RobotoMedium,
    fontWeight: "500",
    color: BaseColors.black,
    textTransform: "capitalize",
    textAlign: "center",
  },
  footLogoStyle: {
    height: Dimensions.get("window").height / 4,
    width: 420,
    alignSelf: "center",
  },

  descTextStyle: {
    fontSize: 22,
    fontFamily: FontFamily.RobotoMedium,
    maxWidth: "80%",
    padding: 8,
    lineHeight: 30,
    textAlign: "center",
    color: "#2F2828",
    marginBottom: 120,
  },
  sponsorIconStyleView: { position: "absolute", bottom: 30 },
  sponsorImageStyle: {
    width:
      Platform.OS === "android"
        ? Dimensions.get("window").width - 50
        : Dimensions.get("window").width - 46,
    height: Platform.OS === "android" ? 57 : 65,
  },
  sponsorTextStyle: {
    fontFamily: FontFamily.InterMedium,
    fontSize: 18,
    color: BaseColors.black,
    textAlign: "center",
    marginBottom: 12,
  },
});
export default styles;
