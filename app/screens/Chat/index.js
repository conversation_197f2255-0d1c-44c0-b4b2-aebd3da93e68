import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Platform,
  ScrollView,
  RefreshControl,
  KeyboardAvoidingView,
} from "react-native";
import styles from "./styles";
import { isEmpty, isNull, truncate } from "lodash-es";
import { BaseColors } from "@config/theme";
import CSearch from "@components/CSearch";
import FastImage from "react-native-fast-image";
import { CustomIcon } from "@config/LoadIcons";
import { Swipeable } from "react-native-gesture-handler";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import ModalComponent from "@components/Modal";
import NoRecord from "@components/NoRecord";
import CHeader from "@components/CHeader";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import MiniLoader from "@components/MiniLoader";
import {
  blockUnBlockUser,
  clearChat,
  getBlock<PERSON>ist,
  getChatSearchData,
} from "./apiCallFunction";
import SocketActions from "@redux/reducers/socket/actions";
import Toast from "react-native-simple-toast";
import PurChasePlanModal from "@components/PurchasePlanModal";
import FeedBackModal from "@components/FeedbackModal";
import { navigationRef } from "@navigation/NavigationService";

const { getChatList, setChatList, setTotalMsgCount, setSelectedRoom } =
  SocketActions;

const ChatScreen = ({ navigation, search }) => {
  console.log("🚀 ~ ChatScreen ~ search:", search);
  const swipeableRef = useRef(null);

  const dispatch = useDispatch();

  // state
  const [visible, setModalVisible] = useState(false);
  const [blockScreen, setBlockScreen] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState(null);
  const [searchBtnData, setSearchBtnData] = useState(search);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [searchChatList, setSearchChatList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [blockUserList, setBlockUserList] = useState([]);
  const [conversationIdData, setConversationIdData] = useState("");
  const [refreshing, setRefreshing] = useState(false);

  // redux State
  const {
    chatRooms,
    chatLoader,
    chatListNextEnablePage,
    chatData,
    typingData,
    bottomLoader,
  } = useSelector((s) => s.socket);

  // Debug loader state
  useEffect(() => {
    console.log(
      "🚀 ~ ChatScreen ~ chatLoader:",
      chatLoader,
      "loading:",
      loading,
      "chatRooms length:",
      chatRooms?.length
    );
  }, [chatLoader, loading, chatRooms]);

  const { isCurrentPlan, activePlanData } = useSelector((a) => a.auth);

  dayjs.extend(utc);

  useEffect(() => {
    setSearchBtnData(search);
  }, [search]);

  // serach input handler function
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      setTypingTimeout(
        setTimeout(async () => {
          if (
            !isEmpty(isCurrentPlan) ||
            activePlanData?.is_prime_user ||
            activePlanData?.is_free_user
          ) {
            handleSearchChatList(searchBtnData, 1);
          } else {
            setIsPaymentModal(true);
          }
        }, 1000)
      );
    },
    [typingTimeout, searchBtnData]
  );

  useFocusEffect(
    useCallback(() => {
      setBlockScreen(false);
      setSearchBtnData(null);
      setSearchChatList([]);
      dispatch(setTotalMsgCount(0));
      dispatch(setSelectedRoom({}));
    }, [])
  );

  useEffect(() => {
    if (
      isEmpty(isCurrentPlan) &&
      !activePlanData?.is_prime_user &&
      !activePlanData?.is_free_user
    ) {
      setIsPaymentModal(true);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (!blockScreen) {
        // Ensure socket is initialized before getting chat list
        dispatch(SocketActions.initialization());
        setTimeout(() => {
          dispatch(getChatList("init", 1));
        }, 1000); // Small delay to ensure socket is ready
      }
    }, [blockScreen])
  );

  useEffect(() => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (!isNull(searchBtnData)) {
        setLoading(true);
        handleInputChange(searchBtnData);
      }
    } else {
      setIsPaymentModal(true);
    }
  }, [searchBtnData]);

  useEffect(() => {
    if (!isEmpty(chatData)) {
      dispatch(getChatList("init", 1));
    }
  }, [chatData]);

  // Add timeout to stop loader if it gets stuck
  useEffect(() => {
    if (chatLoader) {
      const timeoutId = setTimeout(() => {
        console.log("🚀 ~ ChatScreen ~ loader timeout, stopping manually");
        dispatch(SocketActions.setChatListLoader(false));
      }, 15000); // 15 second timeout

      return () => clearTimeout(timeoutId);
    }
  }, [chatLoader]);

  const getBlockListData = async (page = 1) => {
    setLoading(true);
    const resp = await getBlockList();
    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      setBlockUserList({
        page: page,
        next_enable: resp?.data?.hasNextPage,
        data:
          page > 1
            ? [...blockUserList?.data, ...resp?.data?.data]
            : resp?.data?.data,
      });
      setLoading(false);
    } else {
      setBlockUserList([]);
      setLoading(false);
    }
    setLoading(false);
  };

  const handleSearchChatList = async (searchBtnData, page = 1) => {
    const resp = await getChatSearchData(searchBtnData?.trim(), page);
    console.log("🚀 ~ handleSearchChatList ~ resp:", resp);
    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      setSearchChatList({
        page: page,
        next_enable: resp?.data?.hasNextPage,
        data:
          page > 1
            ? [...searchChatList?.data, ...resp?.data?.data]
            : resp?.data?.data,
      });
      setLoading(false);
    } else {
      setSearchChatList([]);
      setLoading(false);
    }
    setLoading(false);
  };

  const handleClearChatData = async () => {
    if (!isEmpty(conversationIdData)) {
      const newData = chatRooms?.filter(
        (item) => item?.conversation_id !== conversationIdData
      );

      dispatch(setChatList(newData));

      const resp = await clearChat(conversationIdData);
      if (resp !== undefined && resp?.data?.success) {
        setConversationIdData("");
        dispatch(getChatList("init", 1));
        Toast.show("Chat Deleted");
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  };

  const handleModalVisible = (receiver_id) => {
    setModalVisible(true);
    setConversationIdData(receiver_id);
  };

  // Render swipeAble delete button
  const renderRightActions = (receiver_id) => {
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.sendBtnMain}
          onPress={() => handleModalVisible(receiver_id)}
        >
          <CustomIcon name={"Delete"} size={20} color={BaseColors.white} />
        </TouchableOpacity>
      </View>
    );
  };

  // Function to format date
  const formattedDate = (inputDateString) => {
    // Parsing input date string
    const inputDate = dayjs(inputDateString);

    // Current date
    const today = dayjs();

    // Calculate the difference in days
    const diffInDays = today.diff(inputDate, "day");

    // Formatting the date
    let formattedDate = "";

    if (diffInDays === 0) {
      // If the input date is today, display only the time
      formattedDate = inputDate.format("HH:mm");
    } else if (diffInDays === 1) {
      // If the input date is yesterday, display 'Yesterday'
      formattedDate = "Yesterday";
    } else {
      // For other dates, display in the format 'Month Day, Year'
      formattedDate = inputDate.format("MMM D, YYYY");
    }
    return formattedDate;
  };

  const handleUnblock = async (user_id) => {
    const resp = await blockUnBlockUser(user_id);
    if (resp !== undefined && resp?.data?.success) {
      const filteredData = blockUserList?.data?.filter(
        (item) => item?.userData?.user_id !== user_id
      );
      setBlockUserList({
        ...blockUserList,
        data: filteredData,
      });
      Toast.show(resp?.data?.message || "User unblocked !");
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  };

  const RenderDataItem = ({ item, navigation }) => {
    useFocusEffect(
      useCallback(() => {
        swipeableRef.current.close();
      }, [])
    );
    return (
      <Swipeable
        renderRightActions={
          blockScreen || !isNull(searchBtnData)
            ? null
            : () => renderRightActions(item?.conversation_id)
        }
        ref={swipeableRef}
      >
        <TouchableOpacity
          style={styles.renderItemMainView}
          activeOpacity={0.8}
          onPress={() => {
            if (
              !isEmpty(isCurrentPlan) ||
              activePlanData?.is_prime_user ||
              activePlanData?.is_free_user
            ) {
              if (!blockScreen) {
                dispatch(setSelectedRoom(item));
                navigation.navigate("MessagesInfo", { userInfo: item });
              }
            } else {
              setIsPaymentModal(true);
            }
          }}
        >
          {/* Image */}
          <View>
            <FastImage
              source={{ uri: item?.userData?.user_dp }}
              style={styles.imgView}
            />

            {/* green tick */}
            {item?.isOnline ? <View style={styles.greenDot}></View> : null}
          </View>

          {/* User Titles */}
          <View style={styles.titleView}>
            {item?.userData?.username ? (
              <Text style={styles.userNameViewText}>
                {truncate(item?.userData?.full_name, {
                  length: 23,
                  omission: "...",
                })}
              </Text>
            ) : null}

            {/* Data and time */}
            {blockScreen ||
            item?.message_content === "" ? null : item?.createdAt ? (
              <Text style={styles.timeText}>{item?.createdAt}</Text>
            ) : null}

            {/*  Unblock View*/}
            {blockScreen ? (
              <View style={styles.unblockBtn}>
                <TouchableOpacity
                  style={styles.unblockBtnView}
                  activeOpacity={0.8}
                  onPress={() => handleUnblock(item?.userData?.user_id)}
                >
                  <Text style={styles.unblockBtnText}>Unblock</Text>
                </TouchableOpacity>
              </View>
            ) : null}

            {/* message and tick */}

            {!isEmpty(searchBtnData) ? null : (
              <View style={styles.tickAndMessage}>
                {item?.is_my_message ? (
                  <View style={{ flexDirection: "row" }}>
                    {(!isEmpty(typingData?.text) &&
                      typingData?.user_id === item?.userData?.user_id) ||
                    item?.is_read === "" ? null : (
                      <CustomIcon
                        name={
                          item?.is_read === "1" || item?.is_read === "0"
                            ? "two-tic"
                            : item?.is_read === "1" || item?.is_read === "0"
                              ? "two-tic"
                              : "tick"
                        }
                        size={
                          item?.is_read === "1" || item?.is_read === "0"
                            ? 16
                            : 11
                        }
                        color={
                          item?.is_read === "1"
                            ? BaseColors.blueTik
                            : BaseColors.gray6
                        }
                      />
                    )}
                  </View>
                ) : null}
                {!isEmpty(typingData?.text) &&
                typingData?.user_id === item?.userData?.user_id ? (
                  <Text style={styles.typingText}>Typing...</Text>
                ) : item?.type === "message" || item?.type === "reply" ? (
                  <Text style={styles.messageText}>
                    {truncate(item?.message_content || "", {
                      length: 30,
                      omission: "...",
                    })}
                  </Text>
                ) : item?.type === "audio" ? (
                  <Text style={styles.messageText}>Audio</Text>
                ) : item?.type === "post" ? (
                  <Text style={styles.messageText}>Post</Text>
                ) : item?.type === "sticker" ? (
                  <Text style={styles.messageText}>Sticker</Text>
                ) : item?.type === "reel" ? (
                  <Text style={styles.messageText}>Reel</Text>
                ) : item?.type === "story" ? (
                  <Text style={styles.messageText}>Story</Text>
                ) : item?.type === "profile" ? (
                  <Text style={styles.messageText}>Profile</Text>
                ) : item?.type === "story_reply" ? (
                  <Text style={styles.messageText}>Story</Text>
                ) : null}
              </View>
            )}

            {/* message Count */}
            {blockScreen ? null : Number(item?.message_count) > 0 ? (
              <View style={styles.messageCount}>
                <Text style={styles.messageCountText}>
                  {Number(item?.message_count) > 99
                    ? "99+"
                    : item?.message_count || 0}
                </Text>
              </View>
            ) : null}
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };

  const handleOptionsBtn = (selectedPopOverValue) => {
    if (selectedPopOverValue === "block") {
      getBlockListData();
      setBlockScreen(!blockScreen);
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    dispatch(getChatList("init", 1, "", false));
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    let bottom = true;
    if (isCloseToBottom && chatListNextEnablePage?.next_enable) {
      dispatch(
        getChatList("init", chatListNextEnablePage?.page + 1, "", false, bottom)
      );
    }
  };

  return (
    <View style={styles.main}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <ScrollView
          onScroll={handleScroll}
          scrollEventThrottle={0.5}
          keyboardShouldPersistTaps="handled"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.activeTab]} // Customize refresh indicator color
              tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {(loading || chatLoader) && isEmpty(chatRooms) ? (
            <View
              style={{
                alignItems: "center",
                justifyContent: "center",
                padding: 20,
              }}
            >
              <MiniLoader size="medium" />
              <TouchableOpacity
                style={{
                  marginTop: 10,
                  padding: 10,
                  backgroundColor: BaseColors.activeTab,
                  borderRadius: 5,
                }}
                onPress={() => {
                  console.log("🚀 ~ Manual stop loader pressed");
                  dispatch(SocketActions.setChatListLoader(false));
                  setLoading(false);
                }}
              >
                <Text style={{ color: "white", textAlign: "center" }}>
                  Stop Loader
                </Text>
              </TouchableOpacity>
            </View>
          ) : !isEmpty(searchBtnData) && !isNull(searchBtnData) && loading ? (
            <MiniLoader size="medium" />
          ) : (
            <View
              style={{ marginBottom: Platform.OS === "ios" ? 0 : 40, flex: 1 }}
            >
              <FlatList
                data={
                  blockScreen
                    ? blockUserList?.data
                    : !isEmpty(searchBtnData)
                      ? !isEmpty(searchChatList?.data) && !isNull(searchBtnData)
                        ? searchChatList?.data
                        : []
                      : chatRooms || []
                }
                renderItem={(item) => {
                  return (
                    <RenderDataItem navigation={navigation} item={item?.item} />
                  );
                }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                ListEmptyComponent={
                  <View style={styles.centerMain}>
                    <NoRecord
                      title={"Empty"}
                      type="chat"
                      description="noChat"
                    />
                  </View>
                }
                style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
              />
              {bottomLoader ? (
                <View style={{ marginBottom: 20 }}>
                  <MiniLoader size="medium" />
                </View>
              ) : null}
            </View>
          )}
        </ScrollView>
        <ModalComponent
          visible={visible}
          setModalVisible={setModalVisible}
          modalTitle={"Are you sure want to delete \n this chat?"}
          buttonTxt={"Yes"}
          cancelText={"No"}
          isLoader={false}
          onClickSaveBtn={() => {
            handleClearChatData();
            setModalVisible(false);
          }}
          centerIconName={"Delete"}
          modalHeder="Delete"
        />
        <PurChasePlanModal
          visible={isPaymentModal}
          setModalVisible={(e) => setIsPaymentModal(e)}
          text={"currentlyPlanText"}
          navigation={navigation}
        />

        {navigationRef.current.getCurrentRoute().name !== "HomeTab" && (
          <FeedBackModal title="Feedback" />
        )}
      </KeyboardAvoidingView>
    </View>
  );
};

export default ChatScreen;
