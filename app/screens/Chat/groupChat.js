import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Platform,
  ScrollView,
  RefreshControl,
  KeyboardAvoidingView,
} from "react-native";
import styles from "./styles";
import { isEmpty, isNull, truncate } from "lodash-es";
import { BaseColors } from "@config/theme";
import FastImage from "react-native-fast-image";
import { CustomIcon } from "@config/LoadIcons";
import { Swipeable } from "react-native-gesture-handler";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import NoRecord from "@components/NoRecord";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import MiniLoader from "@components/MiniLoader";
import { getGroupChatList, groupSearchMessage } from "./apiCallFunction";
import Oicons from "react-native-vector-icons/Octicons";
import SocketActions from "@redux/reducers/socket/actions";
import Toast from "react-native-simple-toast";

const { getChatList, setTotalMsgCount, setSelectedRoom } = SocketActions;

const GroupChat = ({ navigation, search }) => {
  const swipeableRef = useRef(null);

  const dispatch = useDispatch();

  // state
  const [typingTimeout, setTypingTimeout] = useState(null);
  const [searchBtnData, setSearchBtnData] = useState("");
  console.log("🚀 ~ GroupChat ~ searchBtnData:", searchBtnData);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [searchChatList, setSearchChatList] = useState([]);
  console.log("🚀 ~ GroupChat ~ searchChatList:", searchChatList);
  const [loading, setLoading] = useState(false);
  const [blockUserList, setBlockUserList] = useState([]);
  const [conversationIdData, setConversationIdData] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [gropChatList, setGropChatList] = useState([]);

  // redux State
  const {
    chatRooms,
    chatLoader,
    chatListNextEnablePage,
    chatData,
    typingData,
    bottomLoader,
  } = useSelector((s) => s.socket);

  const { isCurrentPlan, activePlanData } = useSelector((a) => a.auth);

  dayjs.extend(utc);

  useEffect(() => {
    setSearchBtnData(search);
  }, [search]);

  // serach input handler function
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      setTypingTimeout(
        setTimeout(async () => {
          if (
            !isEmpty(isCurrentPlan) ||
            activePlanData?.is_prime_user ||
            activePlanData?.is_free_user
          ) {
            handleSearchChatList(searchBtnData, 1);
          } else {
            setIsPaymentModal(true);
          }
        }, 1000)
      );
    },
    [typingTimeout, searchBtnData]
  );

  const groupChatList = async () => {
    const resp = await getGroupChatList();
    setGropChatList(resp?.data);
  };

  useFocusEffect(
    useCallback(() => {
      setSearchBtnData(null);
      setSearchChatList([]);
      dispatch(setTotalMsgCount(0));
      dispatch(setSelectedRoom({}));
      groupChatList();
    }, [])
  );

  useEffect(() => {
    if (
      isEmpty(isCurrentPlan) &&
      !activePlanData?.is_prime_user &&
      !activePlanData?.is_free_user
    ) {
      setIsPaymentModal(true);
    }
  }, []);

  useEffect(() => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (!isNull(searchBtnData)) {
        setLoading(true);
        handleInputChange(searchBtnData);
      }
    } else {
      setIsPaymentModal(true);
    }
  }, [searchBtnData]);

  useEffect(() => {
    if (!isEmpty(chatData)) {
      dispatch(getChatList("init", 1));
    }
  }, [chatData]);

  const handleSearchChatList = async (searchBtnData, page = 1) => {
    setLoading(true);
    const resp = await groupSearchMessage(searchBtnData?.trim(), page);

    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      setSearchChatList({
        page: page,
        next_enable: resp?.data?.pagination?.hasNextPage,
        data:
          page > 1
            ? [...searchChatList?.data, ...resp?.data?.data]
            : resp?.data?.data,
      });
      console.log(
        "check consition",
        !isEmpty(searchBtnData),
        !isEmpty(searchChatList?.data),
        searchChatList?.data
      );
      setLoading(false);
    } else {
      setSearchChatList([]);
      setLoading(false);
    }
    setLoading(false);
  };

  const handleModalVisible = (receiver_id) => {
    setConversationIdData(receiver_id);
  };

  // Render swipeAble delete button
  const renderRightActions = (receiver_id) => {
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.sendBtnMain}
          onPress={() => handleModalVisible(receiver_id)}
        >
          <CustomIcon name={"Delete"} size={20} color={BaseColors.white} />
        </TouchableOpacity>
      </View>
    );
  };

  const RenderDataItem = ({ item, navigation }) => {
    return (
      <TouchableOpacity
        style={styles.renderItemMainView}
        activeOpacity={0.8}
        onPress={() => {
          if (
            !isEmpty(isCurrentPlan) ||
            activePlanData?.is_prime_user ||
            activePlanData?.is_free_user
          ) {
            dispatch(setSelectedRoom(item));
            navigation.navigate("MessagesInfo", { userInfo: item });
          }
        }}
      >
        {/* Image */}
        <View>
          <FastImage source={{ uri: item?.imageUrl }} style={styles.imgView} />
        </View>

        {/* User Titles */}
        <View style={styles.titleView}>
          {item?.groupName ? (
            <Text style={styles.userNameViewText}>{item?.groupName}</Text>
          ) : null}

          {/* Data and time */}
          {item?.message_content === "" ? null : item?.createdAt ? (
            <Text style={styles.timeText}>
              {(() => {
                // Parse the ISO date string
                const inputDate = dayjs(item?.createdAt);

                // Current date
                const today = dayjs();

                // Calculate the difference in days
                const diffInDays = today.diff(inputDate, "day");

                // Format based on time difference
                if (diffInDays === 0) {
                  // If the input date is today, display only the time
                  return inputDate.format("HH:mm");
                } else if (diffInDays === 1) {
                  // If the input date is yesterday, display 'Yesterday'
                  return "Yesterday";
                } else {
                  // For other dates, display in the format 'Month Day, Year'
                  return inputDate.format("MMM D, YYYY");
                }
              })()}
            </Text>
          ) : null}

          {/* message and tick */}

          {!isEmpty(searchBtnData) ? null : (
            <View style={styles.tickAndMessage}>
              {item?.is_my_message ? (
                <View style={{ flexDirection: "row" }}>
                  {(!isEmpty(typingData?.text) &&
                    (typingData?.group_details_id === item?.group_details_id ||
                      typingData?.user_id === item?.userData?.user_id)) ||
                  item?.is_read === "" ? null : (
                    <CustomIcon
                      name={
                        item?.is_read === "1" || item?.is_read === "0"
                          ? "two-tic"
                          : item?.is_read === "1" || item?.is_read === "0"
                            ? "two-tic"
                            : "tick"
                      }
                      size={
                        item?.is_read === "1" || item?.is_read === "0" ? 16 : 11
                      }
                      color={
                        item?.is_read === "1"
                          ? BaseColors.blueTik
                          : BaseColors.gray6
                      }
                    />
                  )}
                </View>
              ) : null}
              {!isEmpty(typingData?.text) &&
              (typingData?.group_details_id === item?.group_details_id ||
                typingData?.user_id === item?.userData?.user_id) ? (
                <Text style={styles.typingText}>Typing...</Text>
              ) : item?.type === "group" || item?.type === "reply" ? (
                <Text style={styles.messageText}>
                  {truncate(item?.lastMessage || "", {
                    length: 30,
                    omission: "...",
                  })}
                </Text>
              ) : item?.type === "audio" ? (
                <Text style={styles.messageText}>Audio</Text>
              ) : item?.type === "post" ? (
                <Text style={styles.messageText}>Post</Text>
              ) : item?.type === "sticker" ? (
                <Text style={styles.messageText}>Sticker</Text>
              ) : item?.type === "reel" ? (
                <Text style={styles.messageText}>Reel</Text>
              ) : item?.type === "story" ? (
                <Text style={styles.messageText}>Story</Text>
              ) : item?.type === "profile" ? (
                <Text style={styles.messageText}>Profile</Text>
              ) : item?.type === "story_reply" ? (
                <Text style={styles.messageText}>Story</Text>
              ) : null}
            </View>
          )}

          {/* message Count */}
          {Number(item?.message_count) > 0 ? (
            <View style={styles.messageCount}>
              <Text style={styles.messageCountText}>
                {Number(item?.message_count) > 99
                  ? "99+"
                  : item?.message_count || 0}
              </Text>
            </View>
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    dispatch(getChatList("init", 1, "", false));
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    let bottom = true;
    if (isCloseToBottom && chatListNextEnablePage?.next_enable) {
      dispatch(
        getChatList("init", chatListNextEnablePage?.page + 1, "", false, bottom)
      );
    }
  };

  return (
    <View style={styles.main}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <ScrollView
          onScroll={handleScroll}
          scrollEventThrottle={0.5}
          keyboardShouldPersistTaps="handled"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.activeTab]} // Customize refresh indicator color
              tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {loading ? (
            <MiniLoader size="medium" />
          ) : (
            <View
              style={{ marginBottom: Platform.OS === "ios" ? 0 : 40, flex: 1 }}
            >
              <FlatList
                data={
                  !isEmpty(searchBtnData)
                    ? !isEmpty(searchChatList?.data)
                      ? searchChatList?.data
                      : []
                    : gropChatList
                }
                renderItem={(item) => {
                  return (
                    <RenderDataItem navigation={navigation} item={item?.item} />
                  );
                }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                ListEmptyComponent={
                  <View style={styles.centerMain}>
                    <NoRecord
                      title={"Empty"}
                      groupChat
                      type="chat"
                      onPressCreateGroup={() =>
                        navigation.navigate("CreateGroupChat")
                      }
                    />
                  </View>
                }
                style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
              />
            </View>
          )}
        </ScrollView>
        {!isEmpty(gropChatList) && (
          <TouchableOpacity
            style={styles.addUserContainer}
            activeOpacity={0.8}
            onPress={() => navigation.navigate("CreateGroupChat")}
          >
            <View style={styles.circle}>
              <Oicons
                name="person-add"
                size={30}
                color={BaseColors.activeTab}
              />
            </View>
          </TouchableOpacity>
        )}
      </KeyboardAvoidingView>
    </View>
  );
};

export default GroupChat;
