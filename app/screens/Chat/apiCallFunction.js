import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { isEmpty } from "lodash-es";
import Toast from "react-native-simple-toast";

// Search Chat List
export const getChatSearchData = async (searchBtnData, page) => {
  if (!isEmpty(searchBtnData?.trim())) {
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.searchChatList}?page=${page}&pageSize=10&username=${searchBtnData}`,
        "GET"
      );

      if (resp !== undefined) {
        return resp;
      } else {
        Toast.show(resp?.data?.message || "No Data Found");
      }
    } catch (error) {
      console.error("🚀 ~ getData ~ error:", error);
    }
  } else {
    return undefined;
  }
};

// Fetch Saved Reel Data
export const getBlockList = async () => {
  try {
    const resp = await getApiData(`${BaseSetting.endpoints.blockList}`, "GET");

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Block User Chat
export const blockUnBlockUser = async (receiver_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.blockUnblock}${receiver_id}`,
      "POST"
    );
    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const clearChat = async (receiver_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.clearChatList}${receiver_id}`,
      "POST"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// Get Audience Data Form DB
export const natureOfBusinessData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=nature_of_business`,
      "GET"
    );

    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Get Audience Data Form DB
export const getGroupChatList = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getGroupChat}`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Get Audience Data Form DB
export const createGroup = async (data) => {
  console.log("🚀 ~ createGroup ~ data:", data);
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.createGroup}`,
      "POST",
      data,
      {},
      true
    );
    console.log("🚀 ~ createGroup ~ resp:", resp);
    // if (resp !== undefined) {
    //   if (resp?.data?.success) {
    //     return resp?.data;
    //   } else {
    //     Toast.show(
    //       resp?.data?.message || "Something went wrong please try again"
    //     );
    //   }
    // }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Search Chat List
export const groupSearchMessage = async (searchBtnData, page) => {
  if (!isEmpty(searchBtnData?.trim())) {
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.groupSearch}?page=${page}&pageSize=10&search=${searchBtnData}`,
        "GET"
      );
      if (resp !== undefined) {
        return resp;
      } else {
        Toast.show(resp?.data?.message || "No Data Found");
      }
    } catch (error) {
      console.error("🚀 ~ getData ~ error:", error);
    }
  } else {
    return undefined;
  }
};
