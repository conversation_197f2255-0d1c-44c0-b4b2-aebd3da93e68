import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Dimensions,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import CInput from "@components/TextInput";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import CDropdown from "@components/CDropDown";
import styles from "./style";
import CButton from "@components/CButton";
import { getCompanyDetails, onSubmit, pickDocument } from "./apiFunctions";
import isEmpty from "lodash-es/isEmpty";
import {
  getMobileNumberLength,
  mobileValidation,
} from "@app/utils/commonFunction";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Toast from "react-native-simple-toast";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import CCamera from "@components/CameraButton/CCamera";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";
import DateTimePicker from "@components/CDateTimePicker";
import { genderData } from "@config/staticData";
import dayjs from "dayjs";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const { images } = require("@config/images");
const FastImage = require("react-native-fast-image");

const { setUserData, setAccessToken, setCompanyId } = authAction;
const PersonalDetailSchema = yup.object().shape({
  companyEmail: yup.string().email("validEmailAddress"),
  companyMobileNo: yup.string().matches(/^\d+$/, "onlyNumericValueMsg"),
  companyGSTNumber: yup.string(),
  panCardNumber: yup.string(),
  companyFullAddress: yup.string(),
  companyProfileBio: yup
    .string()
    .min(10, "ProfileMustCharactersErrMsg")
    .max(200, "ProfileBioMustNotErrMsg"),
  countryCode: yup.string().default("91"),
  companyPinCode: yup
    .string()
    .max(10, "pinCodeCharactersErrMsg")
    .min(5, "pinCodeCharactersMaxErrMsg")
    .matches(/^[a-zA-Z0-9]+$/, "pinCodeValidErrMsg"),
});

const SingUpWithFastRegistrationSecond = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const { user_id, companyId, accessToken } = useSelector((e) => e.auth);

  // useRef :
  const companyEmailRef = useRef(null);
  const companyMobileNoRef = useRef(null);
  const companyGSTNumberRef = useRef(null);
  const panCardNumberRef = useRef(null);

  const fullAddressRef = useRef(null);
  const profileBioRef = useRef(null);
  const companyPinCodeRef = useRef(null);
  const scrollRef = useRef(null);

  // states's
  const [profileImage, setProfileImage] = useState({});
  const [mobileNoLength, setMobileLength] = useState(10);

  const [isLoading, setIsLoading] = useState(false);

  const [countryCode, setCountryCode] = useState("91");
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [profilePictureModal, setProfilePictureModal] = useState(false);
  const [imagePath, setImagePath] = useState({});
  const [isEditMode, setIsEditMode] = useState(false);

  // memo's

  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const mobileNoLengthMemo = useMemo(() => mobileNoLength, [mobileNoLength]);

  const {
    control,
    setValue,
    handleSubmit,
    setError,
    getValues,
    clearErrors,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(PersonalDetailSchema),
  });

  const getDetail = async () => {
    setIsLoading(true);
    const resp = await getCompanyDetails();

    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      // setValue("companyEmail", resp?.data?.data?.company_email || undefined);
      if (
        !isEmpty(resp?.data?.data?.company_phone_number) ||
        resp?.data?.data?.company_phone_number !== undefined
      ) {
        setIsEditMode(true);
      }
      setValue("companyFullAddress", resp?.data?.data?.address || undefined);
      if (resp?.data?.data?.gender !== undefined) {
        setValue("gender", resp?.data?.data?.gender);
      }
      if (resp?.data?.data?.dob) {
        setValue("dateOfBirth", resp?.data?.data?.dob);
      }
      if (
        resp?.data?.data?.company_email !== "undefined" &&
        resp?.data?.data?.company_email !== "null"
      ) {
        setValue("companyEmail", resp?.data?.data?.company_email || undefined);
      }
      if (resp?.data?.data?.gst_number !== "undefined") {
        setValue("companyGSTNumber", resp?.data?.data?.gst_number || undefined);
      }
      if (resp?.data?.data?.pan_number !== "undefined") {
        setValue("panCardNumber", resp?.data?.data?.pan_number || undefined);
      }

      setValue("countryCode", resp?.data?.data?.company_phone_code || "91");
      setCountryCode("91");
      if (
        resp?.data?.data?.company_phone_number !== null &&
        resp?.data?.data?.company_phone_number !== undefined
      ) {
        setValue(
          "companyMobileNo",
          `${resp?.data?.data?.company_phone_number}` || undefined
        );
      }
      setCountryCode(resp?.data?.data?.company_phone_code || undefined);
      if (
        resp?.data?.data?.pincode !== undefined &&
        resp?.data?.data?.pincode !== null
      )
        setValue(
          "companyPinCode",
          String(resp?.data?.data?.pincode) || undefined
        );
      dispatch(setCompanyId(resp?.data?.data?.company_id || null));
      setValue("companyProfileBio", resp?.data?.data?.company_bio || undefined);
      if (!isEmpty(resp?.data?.data?.profile_picture)) {
        setValue("companyProfile", {
          uri: resp?.data?.data?.profile_picture,
          isOld: true,
        });
        setProfileImage({
          uri: resp?.data?.data?.profile_picture,
          isOld: true,
        });
      }

      setIsLoading(false);
    } else {
      console.log(
        resp.data?.message || "Something went wrong please try again"
      );
      setIsLoading(false);
    }

    setIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      setValue("companyId", companyId), setValue("userId", user_id);

      if (!isEmpty(accessToken)) {
        getDetail();
      }
    }, [])
  );

  const returnResponse = useCallback(
    async (value) => {
      Keyboard.dismiss();
      let isValid = true;

      if (value?.companyMobileNo !== undefined) {
        const mobileNumberValidation = await mobileValidation(
          value?.companyMobileNo,
          mobileNoLengthMemo
        );

        if (!mobileNumberValidation?.isValid) {
          isValid = false;
          setError("companyMobileNo", {
            message: mobileNumberValidation?.validMessage,
          });
        } else {
          isValid = true;
        }
      }

      if (isValid) {
        setIsLoading(true);
        const resp = await onSubmit(value, companyId, user_id);
        if (resp !== undefined) {
          if (resp?.data?.success && resp?.data?.data) {
            dispatch(setUserData(resp?.data?.data));
            dispatch(setAccessToken(resp?.data?.token));
            AsyncStorage.setItem("token", resp?.data?.token);
            if (route?.params?.isEdit && route?.params?.screen !== "Home") {
              Toast.show("Profile Update Successfully 😊");
              navigation.navigate("DrawerHome", { screen: "Profile" });
            } else {
              if (route?.params?.screen === "Home") {
                Toast.show("Profile Update Successfully 😊");
              } else {
                Toast.show("Registration Successfully 😊");
              }
              navigation.replace("HomeScreen");
            }
            setIsLoading(false);
          } else {
            if (resp?.data?.message === "Network error") {
              returnResponse(value);
            } else {
              setIsLoading(false);
              Toast.show(resp?.data?.message, Toast.LONG);
            }
          }
        }

        setIsLoading(false);
      }
      setIsLoading(false);
    },
    [isLoadingMemo]
  );

  const HandleSelectImage = async (type) => {
    const data = await pickDocument(type, imagePath);
    setProfilePictureModal(false);
    setIsLoading(false);
    setProfileImage(data);
    setValue("companyProfile", data);
  };

  useEffect(() => {
    if (isCameraOpen) {
      setProfilePictureModal(false);
    }
    if (!isCameraOpen && imagePath?.uri) {
      setProfilePictureModal(false);
      HandleSelectImage("captureImg");
    }
  }, [isCameraOpen]);

  const CustomHeader = ({ handleBackButton = () => {} }) => {
    return (
      <View
        style={{
          alignItems: "center",
          flexDirection: "row",
        }}
      >
        <TouchableOpacity
          style={[
            styles.backIconView,
            { borderColor: BaseColors.black, zIndex: 111 },
          ]}
          activeOpacity={0.8}
          onPress={handleBackButton}
        >
          <CustomIcon name="back-arrow" size={18} color={BaseColors.black} />
        </TouchableOpacity>
        <View style={{ position: "absolute", width: "100%" }}>
          <Image
            source={images.footMainLogo}
            style={styles.mainFootLogo}
            resizeMode={"contain"}
          />
        </View>
      </View>
    );
  };
  return (
    <SafeAreaView style={styles.mainView}>
      {isCameraOpen ? (
        <View style={{ flex: 1 }}>
          <CCamera
            setImagePath={setImagePath}
            isCameraOpen={isCameraOpen}
            setIsCameraOpen={setIsCameraOpen}
            recordVideo={false}
          />
        </View>
      ) : (
        <>
          <CustomHeader handleBackButton={() => navigation.goBack()} />
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : null}
            style={styles.KeyboardAvoidingViewStyle}
          >
            <ScrollView
              showsVerticalScrollIndicator={false}
              ref={scrollRef}
              bounces={false}
            >
              <Text style={styles.headingText}>
                {translate("letsCreateAccountText")}
              </Text>
              <TouchableOpacity
                style={styles.profileImgMainViewStyle}
                activeOpacity={0.8}
                onPress={() => setProfilePictureModal(true)}
              >
                {isEmpty(profileImage) ? (
                  <View
                    style={[
                      styles.profileImgMainView,
                      { backgroundColor: "#C4C4C4" },
                    ]}
                  >
                    <CustomIcon name="Camera" size={35} color={"#8E8383"} />
                  </View>
                ) : (
                  <View style={styles.profileImgMainView}>
                    <FastImage
                      source={{ uri: profileImage?.uri }}
                      style={styles.profileImgStyle}
                    />
                  </View>
                )}
                <TouchableOpacity
                  style={[styles.editMainViewStyle]}
                  activeOpacity={0.9}
                  onPress={() => setProfilePictureModal(true)}
                >
                  <CustomIcon
                    name={"Edit-Square"}
                    size={25}
                    color={BaseColors.activeTab}
                  />
                </TouchableOpacity>
              </TouchableOpacity>
              <Text style={styles.profileTextStyle}>
                {translate("CompanyLogoText")}
              </Text>
              {/* Form View */}
              <View style={styles.formMainView}>
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="companyEmail"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyEmailRef}
                        placeholderText={translate("companyEmailText")}
                        returnKeyType="next"
                        value={value}
                        onChange={(e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                        }}
                        onSubmit={() => {
                          companyMobileNoRef.current.focus();
                        }}
                        isError={errors?.companyEmail}
                        isErrorMsg={errors?.companyEmail?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="companyMobileNo"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyMobileNoRef}
                        placeholderText={translate("CompanyMobileText")}
                        returnKeyType="next"
                        value={value}
                        onChange={async (e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                          const mobileNumberValidation = await mobileValidation(
                            e,
                            mobileNoLengthMemo
                          );

                          if (!mobileNumberValidation?.isValid) {
                            isValid = false;
                            setError("companyMobileNo", {
                              message: mobileNumberValidation?.validMessage,
                            });
                          } else if (mobileNumberValidation?.isValid) {
                            clearErrors("companyMobileNo");
                          }
                        }}
                        maxLength={mobileNoLengthMemo || 10}
                        onSubmit={() => {}}
                        keyBoardType="phone-pad"
                        inputType="mobile"
                        selectedCountryCode={(e) => {
                          const country_length = getMobileNumberLength(
                            e?.country_code
                          );

                          setMobileLength(country_length);
                          setValue("companyMobileNo", undefined);
                          const yourData = e?.dial_code?.split("+");
                          setCountryCode(yourData[1]);
                          setValue("countryCode", yourData[1]);
                        }}
                        isError={errors?.companyMobileNo}
                        isErrorMsg={errors?.companyMobileNo?.message}
                        countryCodeValue={
                          getValues("countryCode")
                            ? `+${getValues("countryCode")}`
                            : `+${countryCode}`
                        }
                      />
                    </Animated.View>
                  )}
                />

                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="companyGSTNumber"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyGSTNumberRef}
                        placeholderText={translate("gstNumberText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          panCardNumberRef.current.focus();
                        }}
                        isError={errors?.companyGSTNumber}
                        isErrorMsg={errors?.companyGSTNumber?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="panCardNumber"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={panCardNumberRef}
                        placeholderText={translate("panCardText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          companyPinCodeRef.current.focus();
                        }}
                        isError={errors?.panCardNumber}
                        isErrorMsg={errors?.panCardNumber?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="dateOfBirth"
                  render={({ field: { onChange, value } }) => (
                    <View style={styles.commonView}>
                      <DateTimePicker
                        type={"date"}
                        placeholder={translate("dateOfBirthText")}
                        value={value}
                        onConfirm={onChange}
                        maximumDate={dayjs().subtract(18, "year").toDate()}
                        logo={"dateTime"}
                        title={"Select Date"}
                        isError={errors?.dateOfBirth}
                        isErrorMsg={errors?.dateOfBirth?.message}
                      />
                    </View>
                  )}
                />

                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="gender"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      entering={FadeInDown}
                      style={[styles.commonView]}
                    >
                      <CDropdown
                        labelplaceholder={translate("genderText")}
                        data={genderData}
                        setItem={(e) => {
                          onChange(e?.value);
                        }}
                        value={value}
                        isError={errors?.gender}
                        isErrorMsg={errors?.gender?.message}
                        position={"top"}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="companyPinCode"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyPinCodeRef}
                        placeholderText={translate("pin_zip_code_text")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          fullAddressRef.current.focus();
                        }}
                        isError={errors?.companyPinCode}
                        isErrorMsg={errors?.companyPinCode?.message}
                      />
                    </Animated.View>
                  )}
                />

                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="companyFullAddress"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={fullAddressRef}
                        placeholderText={translate("fullAddressText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          profileBioRef.current.focus();
                        }}
                        isError={errors?.companyFullAddress}
                        isErrorMsg={errors?.companyFullAddress?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: fullAddressRef,
                  }}
                  name="companyProfileBio"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={[styles.commonView, { marginBottom: 40 }]}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={profileBioRef}
                        placeholderText={translate("profileBioText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={handleSubmit(onSubmit)}
                        isError={errors?.companyProfileBio}
                        isErrorMsg={errors?.companyProfileBio?.message}
                        multiline
                        numberOfLines={5}
                        onFocus={() => {
                          setTimeout(() => {
                            scrollRef?.current?.scrollToEnd({ animated: true });
                          }, 200);
                        }}
                      />
                    </Animated.View>
                  )}
                />
              </View>
            </ScrollView>
            <View
              style={{
                marginHorizontal: 20,
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <View style={{ flex: 1 }}>
                <CButton
                  style={{ marginRight: 10 }}
                  type="outlined"
                  onBtnClick={() => {
                    if (
                      route?.params?.isEdit &&
                      route?.params?.screen !== "Home"
                    ) {
                      Toast.show("Profile Update Successfully 😊");
                      navigation.navigate("DrawerHome", {
                        screen: "Profile",
                      });
                    } else {
                      if (route?.params?.screen === "Home") {
                        Toast.show("Profile Update Successfully 😊");
                      } else {
                        Toast.show("Registration Successfully 😊");
                      }
                      navigation.replace("HomeScreen");
                    }
                  }}
                >
                  Skip
                </CButton>
              </View>
              <View style={{ flex: 1 }}>
                <CButton
                  style={styles.buttonView}
                  onBtnClick={handleSubmit((s) => {
                    returnResponse(s);
                  })}
                  loading={isLoadingMemo}
                >
                  Submit
                </CButton>
              </View>
            </View>
            {profilePictureModal ? (
              <AlreadyHaveStoryModal
                visible={profilePictureModal}
                setModalVisible={(e) => setProfilePictureModal(e)}
                title1="captureFromCamera"
                title2="chooseFromGallery"
                onPressTitle1={() => setIsCameraOpen(true)}
                onPressTitle2={() => HandleSelectImage()}
              />
            ) : null}
          </KeyboardAvoidingView>
        </>
      )}
    </SafeAreaView>
  );
};
export default SingUpWithFastRegistrationSecond;
