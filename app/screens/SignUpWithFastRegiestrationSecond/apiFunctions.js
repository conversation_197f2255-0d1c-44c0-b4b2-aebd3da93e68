import { getApiData } from "@app/utils/apiHelper";
import { cropImage, selectAndCropImage } from "@app/utils/commonFunction";
import BaseSetting from "@config/setting";
import { isEmpty, isUndefined } from "lodash-es";
import { PermissionsAndroid } from "react-native";
// import DocumentPicker from "react-native-document-picker";
import { PERMISSIONS, check } from "react-native-permissions";
import Toast from "react-native-simple-toast";

const DocumentPicker = require("react-native-document-picker");

export const pickDocument = (type = "", imagePath = "") => {
  return new Promise(async (resolve, reject) => {
    let resultData = [];
    try {
      await requestStoragePermission();
      if (type === "captureImg") {
        resultData = await cropImage(imagePath?.uri, 120, 120, "circle");
      } else {
        resultData = await selectAndCropImage(120, 120);
      }

      let result = [
        {
          fileCopyUri: null,
          name: "images.jpeg",
          size: resultData?.size,
          type: resultData?.mime || "images.jpeg",
          uri: resultData?.path,
        },
      ];
      // const result = await DocumentPicker.pick({
      //   type: [DocumentPicker.types.images], // You can specify the file types you want to allow
      // });

      if (result[0]?.uri) {
        if (
          result[0].type === "image/jpeg" ||
          result[0].type === "image/png" ||
          result[0].type === "image/jpg" ||
          result[0].type === "image/heic"
        ) {
          resolve(result[0]);
        } else {
          reject("Selected file is not an image");
          // Handle non-image files
        }
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker
        reject("User cancelled the picker");
      } else {
        // Error occurred while picking the document

        reject(err);
      }
    } finally {
      // setIsLoading(false);
    }
  });
};

const requestStoragePermission = () => {
  return new Promise((resolve, reject) => {
    if (Platform.OS === "android") {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
      ).then((result) => {
        if (result) {
          resolve();
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
          ).then((res) => {
            if (res) {
              resolve();
            } else {
              reject("User refused permission");
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.READ_EXTERNAL_STORAGE).then((res) => {
        if (res !== "granted") {
          resolve();
        }
      });
    }
  });
};

export const onSubmit = async (data, companyId, userId) => {
  let finalData = {
    // company_phone_code: data?.countryCode,
    // company_phone_number: data?.companyMobileNo,
    company_id: companyId,
    user_id: userId,
  };

  if (data?.companyMobileNo !== undefined && data?.companyMobileNo !== null) {
    finalData["company_phone_code"] = data?.countryCode;
    finalData["company_phone_number"] = data?.companyMobileNo;
  }
  if (data?.companyProfile) {
    if (!data?.companyProfile?.isOld) {
      finalData["file"] = data?.companyProfile;
    }
  }
  if (data?.companyEmail && !isUndefined(data?.companyEmail)) {
    finalData["company_email"] = data?.companyEmail;
  }

  if (data?.companyGSTNumber !== undefined) {
    finalData["gst_number"] = data?.companyGSTNumber;
  }
  if (data?.panCardNumber !== undefined) {
    finalData["pan_number"] = data?.panCardNumber;
  }

  if (data?.companyFullAddress !== undefined) {
    finalData["address"] = data?.companyFullAddress;
  }
  if (data?.companyProfileBio !== undefined) {
    finalData["company_bio"] = data?.companyProfileBio;
  }
  if (data?.companyPinCode !== undefined) {
    finalData["pincode"] = data?.companyPinCode;
  }

  if (data?.gender !== undefined && !isEmpty(data?.gender)) {
    finalData.gender = data?.gender;
  }
  if (data?.dateOfBirth !== undefined) {
    finalData.dob = data?.dateOfBirth;
  }
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.companyProfile,
      "POST",
      finalData,
      false,
      true
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ onSubmit ~ error:", error);
  }
};

export const natureOfBusinessData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=nature_of_business`,
      "GET"
    );

    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp;
      } else {
        console.log("Something went wrong please try again");
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Get Country Data Form DB
export const getCountryData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=countries`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(resp?.data?.message);
      }
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Get State Data Form DB
export const getCountryStateData = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=states&country_id=${id}`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(resp?.data?.message);
      }
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Get Company Detail
export const getCompanyDetails = async () => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.getCompanyDetail,
      "GET"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getCompanyDetails ~ error:", error);
  }
};
