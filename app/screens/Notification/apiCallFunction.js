import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";

// Fetch Saved Reel Data
export const getNotificationList = async (page) => {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.notificationList}?pageSize=15&page=${page}&timezone=${timezone}`,
      "GET"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

export const deleteNotification = async (id) => {
  try {
    const resp = await getApiData(
      id
        ? `${BaseSetting.endpoints.deleteNotification}/${id}`
        : `${BaseSetting.endpoints.deleteNotification}`,
      "POST",
      {}
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ verifyPayment ~ error:", error);
  }
};

export const readNotificationAPI = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.readNotification}/${id}`,
      "POST",
      {}
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ verifyPayment ~ error:", error);
  }
};
