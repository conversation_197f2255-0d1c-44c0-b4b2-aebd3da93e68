import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet, Dimensions } = require("react-native");

const HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  sendBtnMain: {
    backgroundColor: BaseColors.activeTab,
    height: 45,
    width: 45,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  unblockBtnView: {
    paddingHorizontal: 14,
    paddingVertical: 5,
    borderRadius: 5,
    backgroundColor: BaseColors.white,
    borderWidth: 0.5,
    elevation: 5,
    borderColor: "#D9D9D9",
  },
  unblockBtnText: {
    color: BaseColors.black,
    fontFamily: FontFamily.InterMedium,
  },
  unblockBtn: {
    position: "absolute",
    right: 0,
    alignItems: "center",
    justifyContent: "center",
  },
  centerMain: {
    flex: 1,
    marginTop: HEIGHT / 5,
  },
  tickAndMessage: {
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  secondMainScreen: {
    flex: 1,
    marginHorizontal: 6,
    marginTop: 12,
  },
  searchView: {
    marginHorizontal: 20,
    marginVertical: 10,
  },
  renderItemMainView: {
    flex: 1,
    flexDirection: "row",
    padding: 10,
    alignItems: "center",
    marginHorizontal: 10,
  },
  imgView: {
    height: 55,
    width: 55,
    borderRadius: 33,
    borderWidth: 1.5,
    borderColor: BaseColors.activeTab,
  },
  titleView: {
    flex: 1,
    marginLeft: 16,
  },
  userNameViewText: {
    fontSize: 17,
    fontFamily: FontFamily.InterSemiBold,
    color: BaseColors.black,
    textTransform: "capitalize",
  },
  messageText: {
    fontSize: 13.5,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.gray6,
  },
  typingText: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.activeTab,
  },
  timeText: {
    fontSize: 12,
    fontFamily: FontFamily.InterSemiBold,
    position: "absolute",
    right: 0,
  },
  messageCount: {
    position: "absolute",
    right: 0,
    bottom: -6,
    height: 24,
    width: 24,
    borderRadius: 23,
    backgroundColor: BaseColors.activeTab,
    alignItems: "center",
    justifyContent: "center",
  },
  messageCountText: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 11.5,
    color: BaseColors.white,
  },
  emptyAddressLottieView: {
    height: 270,
  },
  greenDot: {
    position: "absolute",
    right: 0,
    height: 18,
    width: 18,
    backgroundColor: BaseColors.activeTab,
    borderRadius: 25,
    top: 0,
    borderWidth: 3,
    borderColor: BaseColors.white,
  },
});
export default styles;
