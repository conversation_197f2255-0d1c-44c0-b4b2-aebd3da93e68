import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet, Dimensions, Platform } = require("react-native");

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.black,
  },
  headingTitleTextStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 24,
    color: BaseColors.white,
    marginLeft: 12,
  },

  headingView: {
    position: "absolute",
    top: Platform.OS === "android" ? 12 : 34,
    alignSelf: "center",
    zIndex: 9999,
  },
  userNameStyle: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.white,
    maxWidth: "50%",
  },
  contentView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  profileDPStyle: {
    height: 50,
    width: 50,
    borderRadius: 30,
  },
  btnViewStyle: {
    borderWidth: 1,
    borderColor: BaseColors.white,
    borderRadius: 5,
    padding: 7,
    paddingHorizontal: 22,
  },
  btnTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.white,
  },
  bottomView: {
    position: "absolute",
    bottom: 95,
    marginLeft: 12,
  },
  descTextStyle: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 14,
    color: BaseColors.white,
  },
  socialIconStyle: {
    position: "absolute",
    alignSelf: "flex-end",
    bottom: 50,
    right: 12,
  },
  socialIconViewStyle: {
    marginBottom: 20,
  },
  countTextStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 16,
    color: BaseColors.white,
    textAlign: "center",
    marginTop: 5,
  },
  likeView: {
    height: 100,
    width: 100,
    position: "absolute",
    bottom: 0,
    left: -35,
  },
  renderDataMainView: {
    flex: 1,
    height: Dimensions.get("screen").height,
  },
  videoStyle: {
    width: Dimensions.get("screen").width,
    height: Dimensions.get("screen").height,
  },
  moreLessTextStyle: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 14,
    color: BaseColors.activeTab,
  },
  descMainViewStyle: {
    flexDirection: "row",
    marginTop: 19,
  },
  videoView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  renderItemMainView: {
    flex: 1,
    height: Dimensions.get("window").height,
    paddingBottom: Platform.OS === "ios" ? 100 : 30,
  },
  listMainView: {
    height: Dimensions.get("window").height,
  },
  boostReelView: {
    borderWidth: 1,
    flexDirection: "row",
    backgroundColor: BaseColors.activeTab,
    marginVertical: 10,
    padding: 6,
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: 8,
    borderColor: BaseColors.activeTab,
  },
  boostTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.white,
  },
  boostBtnText: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.activeTab,
  },
  boostBtnView: {
    borderWidth: 1,
    padding: 6,
    borderRadius: 5,
    backgroundColor: BaseColors.white,
    borderColor: BaseColors.white,
  },
});

export default styles;
