import React, {
  memo,
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect,
} from "react";
import { RefreshControl, StatusBar, View } from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { BaseColors } from "@config/theme";
import ShareListModal from "@components/ShareListModal";
import MoreInfoModal from "@components/MoreInfoModal";
import { RenderItem } from "./component";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import { followFindObject, likeFindObject } from "./function";
import {
  getReelsList,
  handleAddComment,
  handleToGeCommentList,
  handleToGetLikeList,
  handleToInteractionData,
  handleToLikeDisLike,
  handleToReportUser,
  handleToSaveUnSaved,
  setReelDataList,
  viewReel,
} from "./apiFunctions";
import {
  getUserFollowList,
  handleFollowToggle,
  onSharePost,
} from "@app/utils/commonFunction";
import CommentList from "@components/CommentList";
import SwiperFlatList from "react-native-swiper-flatlist";
import Toast from "react-native-simple-toast";
import ReportModal from "@components/ReportModal";
import { translate } from "../../lang/Translate";
import { isEmpty } from "lodash-es";
import {
  ClickedCommentLikeButton,
  hideComment,
} from "@components/PostComponent/apiCallFunction";
import { getSavedReelData } from "@screens/SavedScreen/apiCallFunction";
import ContactInfoModal from "@components/ContactModal";
import PurChasePlanModal from "@components/PurchasePlanModal";
import {
  getCountryData,
  getCountryStateData,
  natureOfBusinessData,
} from "@screens/SingUpWithCompanyDetail/apiFunctions";
import FeedBackModal from "@components/FeedbackModal";
import { navigationRef } from "@navigation/NavigationService";

const {
  moreInfo,
  moreInfoUnSaved,
  selfUserForDataForUnSave,
  selfUserForDataForSave,
} = require("@config/staticData");

const {
  setReelsList,
  setUserFollowList,
  setCommentData,
  setSavedReelList,
  setUserLikeList,
  setModalData,
  setCreatedPostList,
} = authAction;

// ViewReels component responsible for rendering a list of reels
const ViewReels = ({ navigation, route }) => {
  const routeData = route?.params;
  const type = route?.params?.type;
  // Selected Reel Index from Saved Screen
  const selectedReelIndex = route?.params?.selectedReelIndex;

  // Redux Variable
  const dispatch = useDispatch();
  const {
    reelsList,
    userFollowList,
    commentData,
    savedReelList,
    isCurrentPlan,
    userLikeList,
    userData,
    createdPostList,
    userFollowingList,
    activePlanData,
  } = useSelector((s) => s.auth);
  const videoRef = useRef(null);

  // Ref's
  const holdTimerRef = useRef(null);
  const textInputRef = useRef(null);
  // State
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [shareListModal, setIsShareListModal] = useState(false);
  const [shareReelId, setShareReelId] = useState("");
  const [moreInfoModal, setModalInfoModal] = useState(false);
  const [isLikeAnim, setIsLikeAnim] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isLikeListLoader, setIsLikeListLoader] = useState(false);
  const [reelsId, setIsReelId] = useState("");
  const [isCommentList, setIsCommentList] = useState(false);
  const [commentLoading, setCommentLoading] = useState(false);
  const [commentModalLoading, setCommentModalLoading] = useState(false);
  const [reelData, setReelData] = useState(false);
  const [commentId, setCommentId] = useState("");
  const [isReportModal, setIsReportModal] = useState(false);
  const [reportTextInput, setReportTextinput] = useState("");
  const [reportReason, setReportReason] = useState({});
  const [isComment, setIsComment] = useState("");
  const [isReelScreen, setIsReelScreen] = useState(false);
  const [reasonErrorMessage, setIsReasonErrMsg] = useState("");
  const [selectedUname, setSelectedUname] = useState("");
  const [isCurrentIndexUser, setIsCurrentIndexUser] = useState("");
  const [likeListModal, setLikeListModal] = useState(false);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [isReelsArrList, setIsReelsArrList] = useState([]);
  const [searchValue, setOnChangeSearchValue] = useState("");
  const [countryList, setCountryData] = useState([]);
  const [stateList, setSelectedCountryState] = useState([]);
  const [natureOfBusinessList, setNatureOfBusinessData] = useState([]);
  const [isContactModal, setIsContactModal] = useState(false);
  const [userDataMain, setUerDataMain] = useState({});

  // Memo's
  const currentVideoIndexMemo = useMemo(
    () => currentVideoIndex,
    [currentVideoIndex]
  );
  const likeListModalMemo = useMemo(() => likeListModal, [likeListModal]);
  const reelIdMemo = useMemo(() => reelsId, [reelsId]);
  const isCommentMemo = useMemo(() => isComment, [isComment]);
  const shareListModalMemo = useMemo(() => shareListModal, [shareListModal]);
  const moreInfoModalMemo = useMemo(() => moreInfoModal, [moreInfoModal]);
  const isLikeAnimMemo = useMemo(() => isLikeAnim, [isLikeAnim]);
  const refreshingMemo = useMemo(() => refreshing, [refreshing]);
  const isCommentListMemo = useMemo(() => isCommentList, [isCommentList]);
  const isReelsArrListMemo = useMemo(() => isReelsArrList, [isReelsArrList]);
  const reportReasonMemo = useMemo(() => reportReason, [reportReason]);
  const shareReelIdMemo = useMemo(() => shareReelId, [shareReelId]);

  const commentLoadingMemo = useMemo(() => commentLoading, [commentLoading]);
  const reasonErrorMessageMemo = useMemo(
    () => reasonErrorMessage,
    [reasonErrorMessage]
  );
  const commentModalLoadingMemo = useMemo(
    () => commentModalLoading,
    [commentModalLoading]
  );
  const reportTextInputMemo = useMemo(() => reportTextInput, [reportTextInput]);

  const isReportModalMemo = useMemo(() => isReportModal, [isReportModal]);
  const reelDataMemo = useMemo(() => reelData, [reelData]);

  // Render Reels List
  const videoRefs = useRef(isReelsArrListMemo?.data ? isReelsArrListMemo?.data.map(() => React.createRef()) : []);

  useEffect(() => {
    const ref = videoRefs.current[0]?.current;
    if (ref && ref.seek) {
      ref.seek(0);
    }
  }, []);

  const reelsDataRef = useRef();

  useFocusEffect(
    useCallback(() => {
      if (type === "fromSavedScreen") {
        setIsReelsArrList(savedReelList);
        reelsDataRef.current = savedReelList;
      } else {
        setIsReelsArrList(reelsList);
        reelsDataRef.current = reelsList;
      }
      // fetchCountryData();
      // fetchCountryStateData(101);
      // fetchNatureOfBusinessData();
      return () => {
        reelsDataRef.current = reelsList;
        setIsReelsArrList(reelsList);
      };
    }, [reelsList, type, savedReelList])
  );

  const fetchCountryData = async (e) => {
    const resp = await getCountryData(e);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setCountryData(updatedData);
  };
  const fetchCountryStateData = async (id) => {
    const resp = await getCountryStateData(id);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setSelectedCountryState(updatedData);
  };

  // fetch Nature Of Business Data and manipulate response
  const fetchNatureOfBusinessData = async () => {
    const resp = await natureOfBusinessData();

    let updatedData = resp?.data?.data?.map((val) => {
      return {
        ...val,
        value: val.id,
      };
    });

    setNatureOfBusinessData(updatedData);
  };

  // Set Current Video Index Method
  const onChangeIndex = useCallback(
    async (index) => {
      setCurrentVideoIndex(index);

      const ref = videoRefs.current[index]?.current;
      if (ref && ref.seek) {
        ref.seek(0);
      }

      // Clear previous hold timer
      if (holdTimerRef.current) {
        clearTimeout(holdTimerRef.current);
      }

      // Start hold timer for 3 seconds
      holdTimerRef.current = setTimeout(() => {
        handleViewReel(index);
      }, 2000);

      const userId = reelsList?.data[index]?.user_data[0]?.user_id;
      setIsCurrentIndexUser(userId);

      if (index != null) {
        // 👇 Trigger API call when user scrolls past halfway
        const totalItems = type === "fromSavedScreen" ? savedReelList?.data?.length || 0 : reelsList?.data?.length || 0;
        if (index >= Math.floor(totalItems / 2)) {
          if (
            reelsList?.hasNextPage &&
            type !== "fromSavedScreen"
          ) {
            getReelList(reelsList?.currentPage + 1);
          } else if (
            savedReelList?.next_enable &&
            type === "fromSavedScreen"
          ) {
            fetchSavedReelData(savedReelList?.page + 1);
          }
        }
      }
    },
    [currentVideoIndexMemo, reelsList, savedReelList, type, holdTimerRef]
  );
  const handleViewReel = useCallback(
    (index) => {
      const reelId = reelsList?.data[index]?.reel_id;
      viewReel(reelId);
    },
    [currentVideoIndexMemo, shareReelId]
  );

  const handleToViewInteractionReel = useCallback(
    async (index) => {
      if (reelsList?.data[0]?.boost_post_id !== null) {
        const resp = await handleToInteractionData(
          reelsList?.data[index]?.reel_id
        );
      }
    },
    [currentVideoIndexMemo, shareReelId]
  );
  // Follow Button Animation
  const handleToFollowAnim = useCallback(
    (userId) => {
      if (
        !isEmpty(isCurrentPlan) ||
        activePlanData?.is_prime_user ||
        activePlanData?.is_free_user
      ) {
        const data = followFindObject(userId, reelsList?.data);

        if (data !== -1) {
          for (let i = 0; i < data.length; i++) {
            const object = data[i];

            reelsList.data[object].is_followed =
              !reelsList.data[object].is_followed;
          }
        }
        dispatch(setReelsList(reelsList));
      } else {
        setIsPaymentModal(true);
      }
    },
    [reelsList, isCurrentPlan]
  );

  // Handle To Follow
  const handleToFollow = async (userId, type) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      handleToFollowAnim(userId);
      if (type === "add") {
        userData.followings = userData.followings + 1;
      } else {
        userData.followings = userData.followings - 1;
      }
      const resp = await handleFollowToggle(userId);
      if (resp?.data?.success) {
        getUserList();
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        handleToFollowAnim(userId);
        if (type === "add") {
          userData.followings = userData.followings - 1;
        } else {
          userData.followings = userData.followings + 1;
        }
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  // Handle To Like Animation
  const handleToLikeAnim = (index) => {
    const updatedReelsList = { ...reelsList };
    updatedReelsList.data[index].is_liked =
      !updatedReelsList.data[index].is_liked;
    if (updatedReelsList.data[index].is_liked) {
      setIsLikeAnim(true);
      updatedReelsList.data[index].like_counts =
        Number(updatedReelsList.data[index].like_counts) + 1;
    } else {
      updatedReelsList.data[index].like_counts =
        Number(updatedReelsList.data[index].like_counts) - 1;
    }

    dispatch(setReelsList(updatedReelsList));
  };

  // Handle To Like
  const handleToLike = async (reelId, index) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      handleToLikeAnim(index);
      const resp = await handleToLikeDisLike(reelId);
      if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      } else {
        handleToLikeAnim(index);
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  const commentLikeDislikeAnimation = async (c_id, type, mainIndex, index) => {
    if (type !== "nested") {
      commentData.data[mainIndex].is_liked =
        !commentData.data[mainIndex].is_liked;
      if (commentData.data[mainIndex].is_liked) {
        commentData.data[mainIndex].likes_counts =
          commentData?.data[mainIndex].likes_counts + 1;
      } else {
        commentData.data[mainIndex].likes_counts =
          commentData?.data[mainIndex].likes_counts - 1;
      }
    } else {
      commentData.data[mainIndex].replies[index].is_liked =
        !commentData.data[mainIndex].replies[index].is_liked;
      if (commentData.data[mainIndex].replies[index].is_liked) {
        commentData.data[mainIndex].replies[index].likes_counts =
          commentData?.data[mainIndex].replies[index].likes_counts + 1;
      } else {
        commentData.data[mainIndex].replies[index].likes_counts =
          commentData?.data[mainIndex].replies[index].likes_counts - 1;
      }
    }

    dispatch(setCommentData(commentData));
  };

  const handleCommentLike = useCallback(
    async (c_id, type, mainIndex, index) => {
      if (
        !isEmpty(isCurrentPlan) ||
        activePlanData?.is_prime_user ||
        activePlanData?.is_free_user
      ) {
        await commentLikeDislikeAnimation(c_id, type, mainIndex, index);
        const resp = await ClickedCommentLikeButton(c_id);
        if (resp !== undefined) {
          if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
          } else {
            await commentLikeDislikeAnimation(c_id);
            Toast.show(resp?.data?.message || "Soothing went wrong");
          }
        }
      } else {
        setIsPaymentModal(true);
      }
    },
    [commentData, isCurrentPlan]
  );

  // Handle On Double Tap
  const onDoubleTap = () => {
    console.log("On double tap");
  };

  // For Pull To Refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true); // Set refreshing state to true
    // Fetch updated data here
    if (type === "fromSavedScreen") {
      fetchSavedReelData(1).then(() => {
        setRefreshing(false);
      });
    } else {
      getReelList(1).then(() => {
        setRefreshing(false); // Set refreshing state back to false when done
      });
    }
  }, [refreshingMemo]);

  const handleToCommentList = useCallback(async (id, page) => {
    setIsReelId(id);
    const resp = await handleToGeCommentList(id, page);

    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(
        setCommentData({
          page: page,
          next_enable: resp?.data?.hasNextPage,
          data:
            page > 1
              ? [...createdPostList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      setIsComment("");
      setIsCommentList(true);
    } else {
      console.log(resp?.data?.message || "Some thing went wrong");
    }
  }, []);

  // Handle To More Info; Modal
  const handleToMoreInfo = useCallback(
    async (item) => {
      await setReelData(item?.data);
      setModalInfoModal(item?.modalVisible);
    },
    [reelDataMemo, moreInfoModalMemo]
  );

  // For Handle More Info Button's Event's
  const handleToMoreInfoBtnEvent = useCallback(
    async (item) => {
      if (item?.id === 1) {
        if (
          !isEmpty(isCurrentPlan) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          setModalInfoModal(false);
          navigation.navigate("StoryPreview", {
            image: {
              ...reelData?.ReelData,
              thubnails: reelData?.ReelData?.thumbnailData?.thumbUrl,
              reelId: reelData?.reel_id,
            },
            video_duration: 30,
            sendedFile: {
              ...reelData?.ReelData,
              uri: reelData?.ReelData?.fileUrl,
            },
            type: "reels",
          });
        } else {
          setModalInfoModal(false);
          setIsPaymentModal(true);
        }
      } else if (item?.id === 2) {
        if (
          !isEmpty(isCurrentPlan) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          handleToSaveReel(reelData?.reel_id); // For Save Reel
        } else {
          setModalInfoModal(false);
          setIsPaymentModal(true);
        }
      } else if (item?.id === 3) {
        setModalInfoModal(false);
        if (
          !isEmpty(isCurrentPlan) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          navigation.navigate("MessagesInfo", {
            userInfo: {
              is_blocked: false,
              userData: {
                user_dp: reelData?.user_data[0]?.user_dp,
                user_id: reelData?.user_data[0]?.user_id,
                full_name: reelData?.user_data[0]?.full_name,
              },
            },
          });
        } else {
          setIsPaymentModal(true);
        }
      } else if (item?.id === 4) {
        if (
          !isEmpty(isCurrentPlan) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          await handleToFollow(reelDataMemo?.user_data[0]?.user_id); // For Remove Following
          setModalInfoModal(false);
        } else {
          setModalInfoModal(false);
          setIsPaymentModal(true);
        }
      } else if (item?.id === 5) {
        if (
          !isEmpty(isCurrentPlan) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          setModalInfoModal(false);
          setReportReason({});
          setReportTextinput("");
          setIsReasonErrMsg("");
          setIsReportModal(true); // For Report Account
        } else {
          setIsPaymentModal(true);
          setModalInfoModal(false);
        }
      } else if (item?.id === 6) {
        setModalInfoModal(false);
        dispatch(
          setModalData({
            type: "deleteReelsOnly",
            title: "deleteReelText",
            buttonTxt: "delete",
            cancelText: "No",
            icon: "Delete",
            visible: true,
            extraData: reelData?.reel_id,
          })
        );
      } else {
        Toast.show("Select any one Option"); // When user not select any option then error message
      }
    },
    [
      reelDataMemo,
      moreInfoModalMemo,
      reportReasonMemo,
      reportTextInputMemo,
      isCurrentPlan,
      reelsList,
    ]
  );

  const UpdateHomePostListRedux = (post_id) => {
    const updatedPosts = createdPostList?.data?.map((post) => {
      if (post.post_id === post_id || post.reel_id === post_id) {
        return {
          ...post,
          is_saved: post.is_saved ? false : true,
        };
      }
      return post;
    });

    dispatch(
      setCreatedPostList({
        ...createdPostList,
        data: updatedPosts,
      })
    );
  };

  const handleSaveUnSaveAnimation = async (reelId) => {
    const data = await reelsList?.data?.findIndex(
      (item) => item?.reel_id === reelId
    );
    if (data !== -1) {
      reelsList.data[data].is_saved = !reelsList.data[data].is_saved;
    }
    dispatch(setReelsList(reelsList));
    UpdateHomePostListRedux(reelId);
  };
  // For Save Reels
  const handleToSaveReel = useCallback(
    async (reelId) => {
      await handleSaveUnSaveAnimation(reelId);
      const resp = await handleToSaveUnSaved(reelId);
      if (resp !== undefined && resp?.data?.success) {
        Toast.show(resp?.data?.message || "Reel Saved");
        setModalInfoModal(false);
      } else {
        await handleSaveUnSaveAnimation(reelId);
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        setModalInfoModal(false);
      }
    },
    [reelsList]
  );

  // Get Reels List
  const getReelList = async (page) => {
    const resp = await getReelsList(page, userData?.user_id);

    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(
        setReelsList({
          page: page,
          hasNextPage: resp?.data?.hasNextPage,
          currentPage: page,
          data: setReelDataList(page, resp?.data?.data, reelsList, routeData),
        })
      );
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  };

  // Get Following list
  const getUserList = useCallback(async () => {
    const resp = await getUserFollowList(userData?.user_id);
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(setUserFollowList(resp?.data));
    } else {
      Toast.show("Something went wrong please try again");
    }
  }, [userData]);

  // handle To Report
  const handleToReport = async () => {
    const data = {
      user_id: reelDataMemo?.user_data[0]?.user_id,
      reason: translate(reportReasonMemo?.options),
      type: "reel",
      reporting_id: reelDataMemo?.reel_id,
    };

    if (reportReasonMemo?.options === "somethingElseText") {
      data.reason = reportTextInputMemo;
    }
    if (
      reportReasonMemo?.options === "somethingElseText" &&
      reportTextInputMemo === ""
    ) {
      setIsReasonErrMsg("Please enter,\nWhy you are report this user!");
    } else {
      const resp = await handleToReportUser(data);
      if (resp !== undefined && resp?.data?.success) {
        Toast.show(resp?.data?.message);
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
      }
    }
  };

  const handleToSubmitComment = useCallback(
    async (e) => {
      if (
        !isEmpty(isCurrentPlan) ||
        activePlanData?.is_prime_user ||
        activePlanData?.is_free_user
      ) {
        let updateComment = !isEmpty(selectedUname)
          ? isCommentMemo?.replace(selectedUname, "")
          : isCommentMemo;

        setCommentLoading(true);
        if (!isEmpty(updateComment.trim())) {
          const data = { reel_id: reelIdMemo, comment: updateComment.trim() };
          if (!isEmpty(commentId)) {
            data.reply_id = commentId;
          }
          const comment_count = likeFindObject(reelIdMemo, reelsList?.data);
          const resp = await handleAddComment(data);
          if (resp !== undefined && resp?.data?.success) {
            setIsComment("");
            setCommentId("");
            if (comment_count !== -1) {
              reelsList.data[comment_count].comment_counts =
                Number(reelsList.data[comment_count].comment_counts) + 1;
            }
            dispatch(setReelsList(reelsList));            
            await handleToCommentList(reelIdMemo, 1);
            setCommentLoading(false);
          } else {
            Toast.show(
              resp?.data?.message || "Something went wrong please try again"
            );
            setCommentLoading(false);
          }
        } else {
          setCommentLoading(false);
          Toast.show("Please enter your comment");
        }
        setCommentLoading(false);
      } else {
        setIsPaymentModal(true);
      }
    },
    [isCommentMemo, reelIdMemo, isCurrentPlan]
  );

  // Handle To Reply
  const handleReply = useCallback((value) => {
    setIsComment(`@${value?.user_data[0]?.username} `);
    setSelectedUname(`@${value?.user_data[0]?.username} `);
    setCommentId(value?.comment_id);
    if (textInputRef.current) {
      textInputRef.current.focus();
    }
  }, []);
  // Handle to Hide Comment
  const handleHideComment = async (value) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      const removeObjectsByCommentId = (dataArray, commentId) => {
        return dataArray.map((obj) => {
          if (obj.comment_id === commentId) {
            // Change the hide or unhide here
            obj.is_hide = obj.is_hide ? 0 : 1;
          }
          if (obj.replies && obj.replies.length > 0) {
            obj.replies = removeObjectsByCommentId(obj.replies, commentId);
          }
          return obj;
        });
      };

      const updatedData = commentData?.data?.map((obj) => {
        if (obj.comment_id === value?.comment_id) {
          // Change the nested hide or unhide here
          obj.is_hide = obj.is_hide ? 0 : 1;
        }
        if (obj.replies && obj.replies.length > 0) {
          obj.replies = removeObjectsByCommentId(
            obj.replies,
            value?.comment_id
          );
        }
        return obj;
      });

      dispatch(
        setCommentData({
          ...commentData,
          data: updatedData,
        })
      );
      const d = {
        comment_id: value?.comment_id,
        hide_id: value?.reel_id,
        post_type: "reel",
      };
      const resp = await hideComment(d);

      if (resp !== undefined && resp?.data?.success) {
        console.log("COmment is ");
      } else {
        Toast.show(resp?.data?.message || "Soothing went wrong");
      }
    } else {
      setIsPaymentModal(true);
    }
  };
  function removeDuplicates(arr, prop) {
    return arr.filter(
      (obj, index, self) =>
        index === self.findIndex((o) => o[prop] === obj[prop])
    );
  }
  const fetchSavedReelData = async (page = 1) => {
    const resp = await getSavedReelData(page, userData?.user_id);

    if (resp?.data?.success && resp?.data?.data && !isEmpty(resp?.data?.data)) {
      dispatch(
        setSavedReelList({
          page: page,
          next_enable: resp?.data?.hasNextPage || false,
          data:
            page > 1
              ? [...savedReelList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
    } else {
      dispatch(setSavedReelList([]));
    }
  };

  const handleToGetLikeListOne = async (id, page = 1, search) => {
    dispatch(setUserLikeList({}));
    setLikeListModal(true);
    setIsLikeListLoader(true);

    const resp = await handleToGetLikeList(id, search);
    if (resp !== undefined && resp?.data?.success) {
      const data = await reelsList?.data?.filter(
        (item) => item?.reel_id === id
      );
      setIsReelId(id);
      dispatch(
        setUserLikeList({
          page: page,
          next_enable: resp?.data?.data?.hasNextPage,
          viewData:
            page > 1
              ? [...commentData?.viewData, ...resp?.data?.data?.viewData]
              : resp?.data?.data?.viewData,
          totalLike: resp?.data?.data?.totalLikes || 0,
          viewCount: data[0]?.view_count || 0,
        })
      );
      setIsLikeListLoader(false);
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsLikeListLoader(false);
    }
  };

  const followUnFollowAnimation = (index) => {
    userLikeList.viewData[index].is_followed =
      !userLikeList.viewData[index].is_followed;
    dispatch(setUserLikeList(userLikeList));
  };
  const handleToBtnPressForFollow = async (data) => {
    const resp = await handleFollowToggle(data?.item);
    if (resp !== undefined && resp?.data?.success) {
      console.log("Success");
    } else {
      followUnFollowAnimation(data?.index);
    }
  };
  const getUserShareList =
    userFollowList?.data && userFollowingList?.data
      ? [...new Set([...userFollowList?.data, ...userFollowingList?.data])]
      : [];
  // For get User Story Data
  // useFocusEffect(
  //   useCallback(() => {
  //     if (type !== "fromSavedScreen") {
  //       getReelList(reelsList?.currentPage || 1);
  //     }
  //     getUserList();
  //   }, [])
  // );

  useEffect(() => {
    if (type !== "fromSavedScreen") {
      getReelList(1);
    }
    getUserList();
  }, [])

  useFocusEffect(
    React.useCallback(() => {
      // Start video playback when the screen gains focus
      setIsReelScreen(true);
      if (videoRef.current) {
        videoRef.current.props.paused = false;
      }

      // Pause video playback when the screen loses focus
      return () => {
        if (videoRef.current) {
          videoRef.current.props.pause = true;
        }
        setIsReelScreen(false);
      };
    }, [])
  );
  const handleToAction = (action, data, conversation_id, item) => {
    if (data?.user_id !== userData?.user_id) {
      if (action === "visit_my_profile") {
        if (data?.user_id !== userData?.user_id) {
          navigation.navigate("ProfileNew", {
            data: data,
            type: "anotherProfile",
          });
        } else {
          navigation.navigate("ProfileNew", { data: data });
        }
      } else if (action === "contact_now") {
        setUerDataMain(data);
        setIsContactModal(true);
      } else if (action === "send_message") {
        navigation.navigate("MessagesInfo", {
          userInfo: {
            is_blocked: conversation_id?.is_blocked,
            conversation_id: conversation_id?.conversation_id || "",
            userData: {
              user_dp: data?.user_dp,
              user_id: data?.user_id,
              full_name: data?.full_name,
            },
          },
        });
      } else {
        Toast.show("Current User Not perform any action");
      }
    } else {
      navigation.navigate("BoostedPostInsights", { data: item });
    }
  };

  const handleToPressBoost = (e) => {
    const data = {
      files: e?.ReelData,
      SelectedCountry: e?.country?.split(",").map(Number),
      SelectedState: e?.state?.split(",").map(Number),
      Audience: e?.audience?.split(",").map(Number),
    };
    navigation.navigate("CreateBoostPost", {
      data,
      countryData: countryList,
      stateData: stateList,
      natureOfBusinessDada: natureOfBusinessList,
      type: "reel",
      apiRes: { reel_id: e.reel_id },
    });
  };

  // Render Reels List
  const renderItem = useCallback(
    ({ item, index }) => {
      return (
        <View style={styles.renderItemMainView}>
          <RenderItem
            item={item}
            index={index}
            isLikeAnim={isLikeAnimMemo}
            videoRefs={videoRefs}
            setIsLikeAnim={setIsLikeAnim}
            currentVideoIndex={currentVideoIndexMemo}
            setIsShareListModal={(e) => {
              if (
                !isEmpty(isCurrentPlan) ||
                activePlanData?.is_prime_user ||
                activePlanData?.is_free_user
              ) {
                setShareReelId(item?.reel_id);
                setIsShareListModal(e);
              } else {
                setIsPaymentModal(true);
              }
            }}
            currentRoute={route?.name}
            handleToFollow={handleToFollow}
            setModalInfoModal={handleToMoreInfo}
            handleToLike={(e, index) => {
              if (
                !isEmpty(isCurrentPlan) ||
                activePlanData?.is_prime_user ||
                activePlanData?.is_free_user
              ) {
                handleToLike(e, index);
              } else {
                setIsPaymentModal(true);
              }
            }}
            onDoubleTap={onDoubleTap}
            handleToComment={(e) => {
              if (
                !isEmpty(isCurrentPlan) ||
                activePlanData?.is_prime_user ||
                activePlanData?.is_free_user
              ) {
                handleToCommentList(e, 1);
              } else {
                setIsPaymentModal(true);
              }
            }}
            handleToAction={(action, data, conversation_id, item) => {
              handleToAction(action, data, conversation_id, item);
            }}
            handleToLikeCount={(e) => {
              if (
                !isEmpty(isCurrentPlan) ||
                activePlanData?.is_prime_user ||
                activePlanData?.is_free_user
              ) {
                handleToGetLikeListOne(e, 1);
              } else {
                setIsPaymentModal(true);
              }
              // setLikeListModal(true);
            }}
            videoRef={videoRef}
            type={type}
            navigation={navigation}
            userData={userData}
            handleToPressBoost={(e) => handleToPressBoost(e)}
          />
        </View>
      );
    },
    [
      currentVideoIndexMemo,
      isLikeAnimMemo,
      reelsList,
      route?.name,
      shareReelId,
      shareListModal,
    ]
  );

  const renderLikeList = useCallback(() => {
    return (
      <>
        <ShareListModal
          visible={likeListModalMemo}
          setModalVisible={() => {
            setLikeListModal(!likeListModalMemo);
            setOnChangeSearchValue("");
          }}
          setLoader={setIsLikeListLoader}
          loader={isLikeListLoader}
          buttonText={"Share"}
          type={"reel"}
          title={"playAndLikeText"}
          likeCount={userLikeList?.totalLike}
          likeViewCount={userLikeList?.viewCount}
          listData={userLikeList?.viewData || []}
          onChangeSearch={(e) => {
            setOnChangeSearchValue(e);

            handleToGetLikeListOne(reelIdMemo, 1, e);
          }}
          onPressItemClick={(e) => {
            setLikeListModal(!likeListModalMemo);
            setOnChangeSearchValue("");
            if (e?.user_id !== userData?.user_id) {
              navigation.navigate("ProfileNew", {
                data: e,
                type: "anotherProfile",
              });
            } else {
              navigation.navigate("ProfileNew", { data: e });
            }
          }}
          searchValue={searchValue}
          handleBtnPress={(e) => {
            handleToBtnPressForFollow(e);
          }}
        />
        {/* ) : null} */}
      </>
    );
  }, [userLikeList, likeListModalMemo, isLikeListLoader]);

  const handleToPressUser = (e) => {
    if (e?.user_id !== userData?.user_id) {
      navigation.navigate("ProfileNew", {
        data: e,
        type: "anotherProfile",
      });
    } else {
      navigation.navigate("ProfileNew", { data: e });
    }
  };

  return (
    <View style={styles.mainView}>
      <StatusBar barStyle="dark-content" />
      {/* Reels Header */}
      <View style={styles.headingView}>
        <CHeader
          camera={true}
          headingTitle={"reelTxt"}
          isBackIcon={false}
          headerTitleStyle={styles.headingTitleTextStyle}
          cameraColor={BaseColors.white}
          backIconColor={BaseColors.white}
          cameraSize={28}
          cameraIcon="camera-outline"
          onPressCamera={() =>
            navigation.navigate("AddReelScreen", { goToPost: true })
          }
          handleBackButton={() => navigation.goBack()}
        />
      </View>

      {/* FlatList component to render list of reels */}
      <View style={styles.listMainView}>
        <SwiperFlatList
          data={type === "fromSavedScreen" ? savedReelList?.data : reelsList?.data}
          index={selectedReelIndex || 0}
          vertical={true}
          renderItem={renderItem}
          pagingEnabled
          keyExtractor={(item, index) => index.toString()}
          windowSize={7}
          initialNumToRender={14}
          maxToRenderPerBatch={14}
          updateCellsBatchingPeriod={14}
          decelerationRate="fast"
          legacyImplementation
          onChangeIndex={({ index }) => {
            onChangeIndex(index);
            handleToViewInteractionReel(index);
          }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.primary]} // Customize refresh indicator color
              tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
              style={{ zIndex: 1111111111, paddingTop: 10 }}
            />
          }
          viewabilityConfig={{ itemVisiblePercentThreshold: 80 }}
        />
      </View>

      {/* Share list modal */}
      <ShareListModal
        visible={shareListModalMemo}
        setModalVisible={() => {
          setIsShareListModal(!shareListModalMemo);
          setShareReelId("");
        }}
        buttonText={"Share"}
        type="share"
        shareItem={shareReelIdMemo}
        listData={removeDuplicates(getUserShareList, "username") || []}
        handleBtnPress={(s, i) => {
          onSharePost(s?.item, i);
        }}
        onPressItemClick={(e) => {
          setIsShareListModal(!shareListModalMemo);
          setShareReelId("");
          handleToPressUser(e)
        }}
      />

      {/* Report Modal */}
      <ReportModal
        visible={isReportModalMemo}
        setModalVisible={() => setIsReportModal(false)}
        selectedOptionForReason={(e) => {
          setReportReason(e);
        }}
        textInputData={reportTextInputMemo}
        setTextInputValue={(e) => {
          setReportTextinput(e);
          setIsReasonErrMsg("");
        }}
        reasonValue={reportReasonMemo}
        isErrMessage={reasonErrorMessageMemo}
        onBtnPress={() => {
          handleToReport();
        }}
      />
      {renderLikeList()}
      {/* More info Modal */}
      {moreInfoModalMemo ? (
        <MoreInfoModal
          listData={
            reelDataMemo?.user_data[0]?.user_id !== userData?.user_id
              ? !reelDataMemo?.is_saved
                ? reelDataMemo?.is_followed
                  ? moreInfo
                  : moreInfo.filter((item) => item?.id !== 4) || []
                : reelDataMemo?.is_followed
                  ? moreInfoUnSaved
                  : moreInfoUnSaved.filter((item) => item?.id !== 4) || []
              : !reelDataMemo?.is_saved
                ? selfUserForDataForSave
                : selfUserForDataForUnSave
          }
          visible={moreInfoModalMemo}
          onBtnPress={(e) => {
            handleToMoreInfoBtnEvent(e);
          }}
          setModalVisible={() => setModalInfoModal(!moreInfoModalMemo)}
        />
      ) : null}

      {/* Comment List Modal */}
      <CommentList
        visible={isCommentListMemo}
        setModalVisible={setIsCommentList}
        listData={commentData?.data || []}
        commentLodging={commentLoadingMemo}
        setCommentLodging={setCommentLoading}
        comment={isCommentMemo}
        handleHideComment={handleHideComment}
        handleReply={handleReply}
        setComment={(e) => setIsComment(e)}
        commentModalLodging={commentModalLoadingMemo}
        submitCommentText={handleToSubmitComment}
        textInputRef={textInputRef}
        handleCommentLike={handleCommentLike}
        handleBottom={() =>
          handleToCommentList(reelIdMemo, false, commentData?.page + 1, true)
        }
        UserId={isCurrentIndexUser}
        onPressItemClick={(e) => {
          setIsCommentList(false);
            handleToPressUser(e);
          }}
      />

      <ContactInfoModal
        visible={isContactModal}
        setModalVisible={(e) => setIsContactModal(e)}
        listData={userDataMain}
      />
      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
      {navigationRef.current.getCurrentRoute().name !== "HomeTab" && (
        <FeedBackModal title="Feedback" />
      )}
    </View>
  );
};

export default memo(ViewReels);
