import { array } from "prop-types";

// Find Object's indexes using user id
export const followFindObject = (id, array) => {
  // Check if reelsList is defined and iterable
  if (array) {
    const indices = array.reduce((acc, obj, index) => {
      if (obj.user_id === id) {
        acc.push(index);
      }
      return acc;
    }, []);

    return indices;
  } else {
    console.error("list is empty");
  }
};

// Find object using reel id
export const likeFindObject = (id, array) => {
  // Check if reelsList is defined and iterable
  if (array) {
    const object = array.findIndex((item) => item?.reel_id === id);

    return object;
  } else {
    console.error("list is empty");
  }
};
