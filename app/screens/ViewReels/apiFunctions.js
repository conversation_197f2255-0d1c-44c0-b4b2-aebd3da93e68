import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

// For Get Reel List Api Call
export const getReelsList = async (page, userID) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getReelsList}/${userID}?pageSize=14&page=${page}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getReelsList ~ error:", error);
  }
};

// Handle Like and Dislike api call
export const handleToLikeDisLike = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.reelsLikeDislike}/${id}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ handleToLikeDisLike ~ error:", error);
  }
};

// Get Comment list Api Call
export const handleToGeCommentList = async (id, page) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.reelsCommentList}/${id}?pageSize=20&page=${page}&slug=reel`,
      "GET"
    );

    return resp;
  } catch (error) {
    console.error("🚀 ~ handleToGeCommentList ~ error:", error);
  }
};

// Toggle Save And Unsaved Api Call
export const handleToSaveUnSaved = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.saveUnSavedReel}/${id}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ handleToSaveUnSaved ~ error:", error);
  }
};

export const handleToReportUser = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.reportUser,
      "POST",
      data,
      false,
      false
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ handleToReportUser ~ error:", error);
  }
};

export const handleAddComment = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.commentsAdd,
      "POST",
      data,
      false,
      false
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ handleAddComment ~ error:", error);
  }
};

export const viewReel = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.viewReel}/${id}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ viewReel ~ error:", error);
  }
};

export const setReelDataList = (page, resArr, reelsList, routeData = {}) => {
  let dataArr = [];
  if (routeData?.type === "selectedReel" && page === 1) {
    dataArr = [...new Set([...reelsList?.data, ...resArr])];
  } else {
    dataArr = page > 1 ? [...new Set([...reelsList?.data, ...resArr])] : resArr;
  }
  return dataArr;
};

export const handleToGetLikeList = async (id, search, page = 1) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.likeList}/${id}?page=${page}&pageSize=10${search ? `&search=${search}` :  ""}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ handleToGetLikeList ~ error:", error);
  }
};

export const handleToInteractionData = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.reachPostCount}/${id}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ handleToInteractionData ~ error:", error);
  }
};
