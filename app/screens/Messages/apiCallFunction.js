import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";

// Block User Chat
export const blockUnBlockUser = async (receiver_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.blockUnblock}${receiver_id}`,
      "POST"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const clearChatData = async (conversation_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.clearChat}${conversation_id}`,
      "POST"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const exitGroup = async (group_details_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.exitGroup}`,
      "POST",
      {
        group_details_id: group_details_id,
      }
    );
    if (resp?.data?.success) {
      Toast.show(resp?.data?.message);
      return resp?.data;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const deleteMessage = async (group_details_id, message_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.deleteMessage}`,
      "POST",
      {
        message_id: message_id,
        group_details_id: group_details_id,
      }
    );
    if (resp?.data?.success) {
      Toast.show(resp?.data?.message);
      return resp?.data;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};
