import {
  Dimensions,
  ImageBackground,
  Keyboard,
  Modal,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import CHeader from "@components/CHeader";
import { GiftedChat } from "react-native-gifted-chat";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import styles from "./styles";
import CEmojiPiker from "@components/CEmojiPiker/CEmojiPiker";
import { isArray, isEmpty, startCase } from "lodash-es";
import CustomChatInput from "@components/CustomChatInput/CustomChatInput";
import CSticker from "@components/CSticker";
import RenderBubble from "@components/RenderBubble/RenderBubble";
import { images } from "@config/images";
import NoRecord from "@components/NoRecord";
import ModalComponent from "@components/Modal";
import ReportModal from "@components/ReportModal";
import socketAction from "@redux/reducers/socket/actions";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import Toast from "react-native-simple-toast";
import {
  blockUnBlockUser,
  clearChatData,
  deleteMessage,
  exitGroup,
} from "./apiCallFunction";
import { handleToReportUser } from "@screens/ViewReels/apiFunctions";
import { translate } from "../../lang/Translate";
import TrackPlayer, { State } from "react-native-track-player";
import MiniLoader from "@components/MiniLoader";
import { useFocusEffect } from "@react-navigation/native";
import { generateRandomId } from "@app/utils/commonFunction";
import GroupConformationModal from "@components/GroupConformationModal";
import CButton from "@components/CButton";
import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { FontFamily } from "@config/typography";

const { emit, setIsReadTic } = socketAction;

let isTypingInput = false;
let stopTypingTimeout;

const MessagesInfo = ({ navigation, route }) => {
  // ref
  const inputRef = useRef(null);
  const dispatch = useDispatch();

  // state
  const receiverInfo = route?.params?.userInfo?.userData || {};
  const userInfoData = route?.params?.userInfo || {};

  const [messages, setMessages] = useState([]);
  const { userData } = useSelector((auth) => auth.auth);

  const { typingData, chatData, isReadTic } = useSelector(
    (auth) => auth.socket
  );
  console.log("🚀 ~ MessagesInfo ~ typingData:", typingData);

  const [isOpenEmojiModal, setIsOpenEmojiModal] = useState(false);
  const [seenSticker, setSeenSticker] = useState(false);
  const [message, setMessage] = useState("");
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(288);
  const [isPlay, setIsPlay] = useState(false);
  const [replyViewData, setReplyViewData] = useState("");
  const [visible, setModalVisible] = useState(false);
  const [reportTextInput, setReportTextinput] = useState("");
  const [isReportModal, setIsReportModal] = useState(false);
  const [reportReason, setReportReason] = useState({});
  const [reasonErrorMessage, setIsReasonErrMsg] = useState("");
  const [soundObj, setSoundObj] = useState([]);
  const [currentSoundIndex, setCurrentSoundIndex] = useState(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [showPopover, setShowPopover] = useState(false);
  const [isLoader, setIsLoader] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [userInfo, setUserInfo] = useState(userInfoData);
  const [screenLoader, setScreenLoader] = useState(false);
  console.log("🚀 ~ MessagesInfo ~ screenLoader:", screenLoader);
  const [optionData, setOptionData] = useState([
    { key: "report", icon: "Danger-Triangle", text: "Report" },
  ]);

  const [isCurrentPlaySound, setIsCurrentPlaySound] = useState({
    isPlay: false,
    isCurrentUrl: "",
  });
  const [isPage, setIsPage] = useState({
    page: 1,
    next_enable: false,
  });

  const [showModalText, setShowModalText] = useState({
    modalTitle: "",
    buttonTxt: "",
    cancelText: "",
    type: "",
    icon: "",
    modalHeder: "",
  });

  const [startAudioRecord, setStartAudioRecord] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [isAudioDuration, setIsAudioDuration] = useState("");
  const [sendLoader, setSendLoader] = useState(false);
  const [exitGroupModal, setExitGroupModal] = useState(false);
  const [exitGropLoader, setExitGropLoader] = useState(false);
  const [editMessage, setEditMessage] = useState({});
  const [deleteModal, setDeleteModal] = useState({
    visible: false,
    content: {},
  });

  // memo
  const isReportModalMemo = useMemo(() => isReportModal, [isReportModal]);
  const reportTextInputMemo = useMemo(() => reportTextInput, [reportTextInput]);
  const reportReasonMemo = useMemo(() => reportReason, [reportReason]);

  const reasonErrorMessageMemo = useMemo(
    () => reasonErrorMessage,
    [reasonErrorMessage]
  );

  useEffect(() => {
    if (!isEmpty(userInfo?.conversation_id)) {
      setOptionData([
        {
          key: "block",
          icon: "material-symbols_block",
          text: userInfo?.is_blocked ? "Unblock" : "Block",
        },
        { key: "report", icon: "Danger-Triangle", text: "Report" },
        {
          key: "clearChat",
          icon: "Clear-Chat-2",
          text: "Clear Chat",
        },
      ]);
    }
  }, [userInfo]);

  // select emoji
  const handlePick = (onEmojiSelected) => {
    setMessage((prevMessage) => prevMessage + onEmojiSelected?.emoji);
  };

  useEffect(() => {
    async function setup() {
      await TrackPlayer.setupPlayer();
      TrackPlayer.addEventListener("playback-state", onPlaybackStateChange);
    }

    async function stopAudio() {
      await TrackPlayer.stop();
    }

    setup();

    // Clean up event listeners when component unmounts
    return () => {
      stopAudio();
      TrackPlayer?.destroy();
      TrackPlayer.removeEventListener("playback-state", onPlaybackStateChange);
    };
  }, []);

  const onPlaybackStateChange = ({ state }) => {
    if (state === State.Playing) {
      setIsPlaying(true);
    } else if (state === State.Ended) {
      setIsCurrentPlaySound({
        isPlay: false,
        isCurrentUrl: "",
      });
      setIsPlaying(false);
    }
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      (event) => {
        setKeyboardVisible(true);
        const screenHeight = Dimensions.get("window").height;
        const keyboardHeightEstimate =
          screenHeight - event.endCoordinates.screenY;
        setKeyboardHeight(keyboardHeightEstimate);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      readMessage();
    }, [])
  );

  useEffect(() => {
    const isGroupChat = userInfoData?.group_details_id;

    if (!isEmpty(isReadTic)) {
      // Check if the read receipt is for the current chat
      const isForCurrentChat = isGroupChat
        ? isReadTic?.group_details_id === userInfoData?.group_details_id
        : isReadTic?.conversation_id === userInfo?.conversation_id;

      if (isForCurrentChat) {
        const tempMsgArr = [...messages];
        const finalArr = tempMsgArr.map((s) => {
          return {
            ...s,
            is_read: `${isReadTic?.is_read}`,
          };
        });
        dispatch(setIsReadTic({}));
        setMessages(finalArr);
      }
    }
  }, [isReadTic, userInfoData, userInfo]);

  useEffect(() => {
    const isGroupChat = userInfoData?.group_details_id;

    if (
      !isEmpty(chatData) &&
      (isGroupChat
        ? chatData?.group_details_id === userInfoData?.group_details_id
        : chatData?.conversation_id === userInfo?.conversation_id ||
          chatData?.receiver_id === userInfo?.user_id)
    ) {
      const tempObj = {
        ...chatData,
        _id: chatData?.message_id,
        user: {
          _id: 1,
        },
        text: chatData.message_content,
        messageType: chatData?.type,
        createdAt: chatData?.createdAt,
        duration: chatData?.duration || "",
      };

      // setMessages((previousMessages) =>
      //   GiftedChat.append(previousMessages, [tempObj]),
      // );
      let tempArr = [...messages];
      tempArr.unshift(tempObj);

      setMessages(tempArr);
      readMessage();
      dispatch({
        type: socketAction.SET_RECEIVED_CHAT_DATA,
        chatData: {},
      });
    }
  }, [chatData, userInfo]);

  const readMessage = useCallback(() => {
    // Check if it's a group chat or single chat
    const isGroupChat = userInfoData?.group_details_id;

    let options = {
      user_id: userData?.user_id,
    };

    if (isGroupChat) {
      // For group chat
      options.group_details_id = userInfoData?.group_details_id;
    } else {
      // For single chat
      options.receiver_id = receiverInfo?.user_id;
    }

    const eventName = isGroupChat ? "group_read_message" : "read_message";
    console.log(
      "🚀 ~ readMessage ~ eventName:",
      eventName,
      "options:",
      options
    );

    dispatch(
      emit(eventName, options, (s) => {
        console.log("Read=====>");
      })
    );
  }, [receiverInfo, userInfoData]);

  // Function to mark specific message as read (for group chats)
  const markMessageAsRead = useCallback(
    (messageId) => {
      const isGroupChat = userInfoData?.group_details_id;

      if (isGroupChat) {
        const options = {
          group_details_id: userInfoData?.group_details_id,
          user_id: userData?.user_id,
          message_id: messageId,
        };

        dispatch(
          emit("group_read_message", options, (s) => {
            console.log("Message marked as read=====>");
          })
        );
      }
    },
    [userInfoData, userData]
  );

  const getMessagesList = useCallback(
    async (page = 1) => {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

      // Check if it's a group chat or single chat
      const isGroupChat = userInfoData?.group_details_id;

      let options = {
        user_id: userData?.user_id,
        page: page,
        pageSize: 20,
        timezone: timezone,
      };

      if (isGroupChat) {
        // For group chat
        options.group_details_id = userInfoData?.group_details_id;
        options.user_id = userData?.user_id;
      } else {
        // For single chat
        options.receiver_id = receiverInfo?.user_id;
      }

      setScreenLoader(true);
      try {
        const eventName = isGroupChat
          ? "group_conversations"
          : "single_message";
        console.log("🚀 ~ MessagesInfo ~ eventName:", eventName);
        console.log("🚀 ~ MessagesInfo ~ options:", options);
        dispatch(
          emit(eventName, options, (respData) => {
            if (respData?.success) {
              const pagination = respData?.data?.pagination;
              const messageList =
                !isEmpty(respData?.data?.data) && isArray(respData?.data?.data)
                  ? respData?.data?.data
                  : [];

              let isMessageData = [];
              if (!isEmpty(messageList)) {
                isMessageData = messageList.map((s) => ({
                  ...s,
                  _id: s?.message_id || generateRandomId(10),
                  user: {
                    _id: s?.sender_id === userData?.user_id ? 0 : 1,
                  },
                  text: s?.message_content || s?.message_type,
                  messageType: s?.type,
                  createdAt: s?.createdAt,
                  duration: s?.duration || "",
                  is_read: s?.is_read || "-1",
                }));
              }

              setIsPage({
                page: pagination?.currentPage,
                next_enable: pagination?.hasNextPage,
              });
              if (!isEmpty(isMessageData)) {
                const finalData = [...new Set([...messages, ...isMessageData])];
                setMessages(finalData);
                setScreenLoader(false);
              } else {
                setScreenLoader(false);
              }
            }
          })
        );
        setScreenLoader(false);
      } catch (error) {
        setScreenLoader(false);
        console.error("🚀 ~ file: index.js:79 ~ ShortlistApi ~ error:", error);
      }
    },
    [route, userData, receiverInfo, messages, isPage, editMessage]
  );

  useFocusEffect(
    useCallback(() => {
      getMessagesList();
    }, [])
  );

  // Function to handle sending a message

  const handleSend = useCallback(
    async (messageObj = {}, msg = []) => {
      // Stop typing when sending message
      stopTypingManually();

      setSendLoader(true);
      if (messages?.length === 0) {
        await getMessagesList(Number(isPage?.page) || 1);
      }

      // Check if this is a group chat
      if (userInfoData?.group_details_id) {
        // Group chat payload
        let groupOptions = {
          group_details_id: userInfoData?.group_details_id,
          user_id: userData?.user_id,
          message_content: messageObj?.text?.trim(),
          type: messageObj?.messageType || "text",
        };
        console.log("🚀 ~ MessagesInfo ~ groupOptions:", groupOptions);
        console.log(
          "🚀 ~ MessagesInfo ~ isGroupChat:",
          !!userInfoData?.group_details_id
        );
        if (messageObj?.reply_id) {
          groupOptions.reply_id = messageObj?.reply_id;
        }
        if (messageObj?.duration) {
          groupOptions.duration = messageObj?.duration;
        }
        if (messageObj?.meta) {
          groupOptions.meta = messageObj?.meta;
        }

        dispatch(
          emit("group_send_message", groupOptions, (resData) => {
            console.log("🚀 ~ MessagesInfo ~ resData:", resData);
            if (resData?.status) {
              let messageObject = {
                ...resData?.data,
                _id: resData?.data?.message_id,
                user: {
                  _id: 0,
                },
                text: resData?.data?.message_content,
                createdAt: dayjs(resData?.data?.createdAt).format("hh:mm A"),
                messageType: resData?.data?.type,
                duration: resData?.data?.duration || "",
                is_read: resData?.data?.is_read || "-1",
              };
              console.log("🚀 ~ MessagesInfo ~ messageObject:", messageObject);

              if (messages.length === 0) {
                msg.unshift(messageObject);
              } else {
                messages.unshift(messageObject);
              }

              setMessages(messages);
              setMessage("");
              setReplyViewData("");
              setSendLoader(false);
            } else {
              Toast.show(resData?.message || "Error while sending a message");
              setSendLoader(false);
            }
          })
        );
      } else {
        // Single chat logic (existing)
        let options = {
          receiver_id: receiverInfo?.user_id,
          user_id: userData?.user_id,
          message: messageObj?.text.trim(),
          type: messageObj?.messageType,
        };
        if (messageObj?.reply_id) {
          options.reply_id = messageObj?.reply_id;
        }
        if (messageObj?.duration) {
          options.duration = messageObj?.duration;
        }
        if (messageObj?.meta) {
          options.meta = messageObj?.meta;
        }

        dispatch(
          emit("send_message", options, (resData) => {
            if (resData?.success) {
              let messageObject = {
                ...resData?.data,
                _id: resData?.data?.message_id,
                user: {
                  _id: 0,
                },
                text: resData?.data?.message_content,
                createdAt: dayjs(resData?.data?.createdAt).format("hh:mm A"),
                messageType: resData?.data?.type,
                duration: resData?.data?.duration || "",
                is_read: resData?.data?.is_read || "-1",
              };

              if (messages.length === 0) {
                msg.unshift(messageObject);
              } else {
                messages.unshift(messageObject);
              }

              if (resData?.data?.conversation_id) {
                setUserInfo({
                  ...userInfo,
                  conversation_id: resData?.data?.conversation_id,
                });
              }

              setMessages(messages);
              setMessage("");
              setReplyViewData("");
              setSendLoader(false);
            } else {
              Toast.show(resData?.message || "Error while sending a message");
              setSendLoader(false);
            }
          })
        );
      }
    },
    [
      messages,
      route,
      receiverInfo,
      userData,
      replyViewData,
      isPlaying,
      isLoader,
      startAudioRecord,
      timeLeft,
      isAudioDuration,
      sendLoader,
    ]
  );

  // this Function for Edit Message
  const handleEditMessage = useCallback(
    async (messageObj = {}, msg) => {
      // Stop typing when sending message
      stopTypingManually();

      setSendLoader(true);

      // Check if this is a group chat
      if (userInfoData?.group_details_id) {
        // Group chat payload
        let groupOptions = {
          message_id: messageObj?.message_id,
          new_message_content: msg,
        };
        const resp = await getApiData(
          BaseSetting.endpoints.editGroupMessage,
          "POST",
          groupOptions
        );
        console.log("🚀 ~ MessagesInfo ~ resp:", resp);
        if (resp?.data?.success) {
          setSendLoader(false);
          getMessagesList(Number(isPage?.page) || 1);
          setMessage("");
          setEditMessage({});
        } else {
          setSendLoader(false);
        }
      } else {
        // Group chat payload
        let groupOptions = {
          message_id: messageObj?.message_id,
          new_message_content: msg,
        };
        const resp = await getApiData(
          BaseSetting.endpoints.editMessage,
          "POST",
          groupOptions
        );
        console.log("🚀 ~ MessagesInfo ~ resp:", resp?.data);
        if (resp?.data?.success) {
          setSendLoader(false);
          getMessagesList(Number(isPage?.page) || 1);
          setMessage("");
          setEditMessage({});
        } else {
          setSendLoader(false);
        }
      }
    },
    [
      messages,
      route,
      receiverInfo,
      userData,
      replyViewData,
      isPlaying,
      isLoader,
      startAudioRecord,
      timeLeft,
      isAudioDuration,
      sendLoader,
    ]
  );

  const onSwipe = (data) => {
    if (!isEmpty(data)) {
      inputRef.current.focus();
      setReplyViewData(data);
    }
  };

  const handleRemoveData = () => {
    if (!isEmpty(message)) {
      const currentValue = message;
      const characters = Array.from(currentValue);
      characters.pop();
      const updatedValue = characters.join("");
      setMessage(updatedValue);
    }
  };

  const renderBubble = useCallback(
    (props) => {
      return (
        <RenderBubble
          props={props}
          onPlayAudio={(audioPath) => {
            // onPlayAudio(audioPath);
            startAudio(audioPath);
          }}
          isPlay={isPlay}
          onSwipe={onSwipe}
          messagesArray={messages}
          soundObj={soundObj}
          currentSoundIndex={currentSoundIndex}
          setSoundObj={(s) => setSoundObj(s)}
          isCurrentPlaySound={isCurrentPlaySound}
          navigation={navigation}
          onActionPress={(key, content) => {
            if (key === "edit") {
              setEditMessage({ key: key, content: content });
              setMessage(content.text);
            } else {
              setTimeout(() => {
                setDeleteModal({ visible: true, content: content });
              }, 600);
            }
            console.log("message cotent------", key, content);
          }}
        />
      );
    },
    [
      messages,
      isPlay,
      soundObj,
      currentSoundIndex,
      isCurrentPlaySound,
      editMessage,
    ]
  );

  // const onPlayAudio = async (audioPath) => {
  //   const tempArr = messages.filter(
  //     (s) => s.messageType === "audio" && s?.text.includes("https://")
  //   );
  //   const isIndex = tempArr.findIndex((s) => s?.text === audioPath);
  //   if (currentSoundIndex !== null) {
  //     soundObj[currentSoundIndex].stop();
  //     setCurrentSoundIndex(null);
  //     setIsCurrentPlaySound({
  //       isPlay: false,
  //       isCurrentUrl: "",
  //     });
  //   }
  //   if (isIndex > -1 && audioPath !== isCurrentPlaySound?.isCurrentUrl) {
  //     setIsCurrentPlaySound({
  //       isPlay: true,
  //       isCurrentUrl: audioPath,
  //     });
  //     soundObj[isIndex].play((success) => {
  //       if (success) {
  //         setIsCurrentPlaySound({
  //           isPlay: false,
  //           isCurrentUrl: "",
  //         });
  //       } else {
  //         console.log("playback failed due to audio decoding errors");
  //       }
  //     });
  //     setCurrentSoundIndex(isIndex);
  //   }
  // };

  const startAudio = async (audioUrl) => {
    if (isCurrentPlaySound?.isCurrentUrl !== audioUrl) {
      if (isPlaying) {
        await TrackPlayer.stop();
      }
      await TrackPlayer.reset();
      // await TrackPlayer.setupPlayer();
      await TrackPlayer.add({
        id: audioUrl,
        url: audioUrl,
      });

      await TrackPlayer.play();
      setIsCurrentPlaySound({
        isPlay: true,
        isCurrentUrl: audioUrl,
      });
      setIsPlaying(true);
    } else if (isCurrentPlaySound?.isCurrentUrl === audioUrl) {
      if (isCurrentPlaySound?.isPlay) {
        await TrackPlayer.pause();
        setIsPlaying(false);
        setIsCurrentPlaySound({
          isPlay: false,
          isCurrentUrl: audioUrl,
        });
      } else {
        await TrackPlayer.play();
        setIsCurrentPlaySound({
          isPlay: true,
          isCurrentUrl: audioUrl,
        });
        setIsPlaying(true);
      }
    }
  };

  const stopSound = async () => {
    setIsPlaying(false);
    await TrackPlayer.stop();
    setIsCurrentPlaySound({
      isPlay: false,
      isCurrentUrl: "",
    });
  };

  const handleOptionsBtn = (selectedPopOverValue) => {
    if (selectedPopOverValue === "block") {
      if (userInfo?.is_blocked) {
        handleBlockUnblock();
      } else {
        setShowModalText({
          modalTitle: `Are you sure you want to Block \n ${startCase(String(receiverInfo?.full_name))}?`,
          buttonTxt: "Yes",
          cancelText: "No",
          type: "block",
          icon: "material-symbols_block",
          modalHeader: "Block",
        });
        if (Platform.OS === "ios") {
          setShowPopover(false);
          setTimeout(() => {
            setModalVisible(true);
          }, 600);
        } else {
          setModalVisible(true);
        }
      }
    } else if (selectedPopOverValue === "clearChat") {
      setShowModalText({
        modalTitle: "Are you sure want to delete \n this chat?",
        buttonTxt: "Yes",
        cancelText: "No",
        type: "delete",
        icon: "Delete",
        modalHeder: "Delete",
      });
      if (Platform.OS === "ios") {
        setShowPopover(false);
        setTimeout(() => {
          setModalVisible(true);
        }, 600);
      } else {
        setModalVisible(true);
      }
    } else if (selectedPopOverValue === "report") {
      if (Platform.OS === "ios") {
        setShowPopover(false);
        setTimeout(() => {
          setIsReportModal(true);
        }, 600);
      } else {
        setIsReportModal(true);
      }
    } else if (selectedPopOverValue === "exit_group") {
      setShowPopover(false);
      setTimeout(() => {
        setExitGroupModal(true);
      }, 600);
    }
  };

  const resetStopTypingTimeout = () => {
    const isGroupChat = userInfoData?.group_details_id;

    let options = {
      user_id: userData?.user_id,
    };

    if (isGroupChat) {
      options.group_details_id = userInfoData?.group_details_id;
    } else {
      options.receiver_id = receiverInfo?.user_id;
    }

    if (isTypingInput) {
      clearTimeout(stopTypingTimeout);
    }

    stopTypingTimeout = setTimeout(() => {
      isTypingInput = false;
      const eventName = isGroupChat ? "group_stopTyping" : "stopTyping";
      console.log(
        "🚀 ~ STOP typing (timeout) ~ eventName:",
        eventName,
        "options:",
        options
      );
      dispatch(emit(eventName, options));
      stopTypingTimeout = undefined;
    }, 1000); // Reduced to 1 second for better UX
  };

  const stopTypingManually = () => {
    const isGroupChat = userInfoData?.group_details_id;

    let options = {
      user_id: userData?.user_id,
    };

    if (isGroupChat) {
      options.group_details_id = userInfoData?.group_details_id;
    } else {
      options.receiver_id = receiverInfo?.user_id;
    }

    if (isTypingInput) {
      clearTimeout(stopTypingTimeout);
      isTypingInput = false;
      const eventName = isGroupChat ? "group_stopTyping" : "stopTyping";
      console.log(
        "🚀 ~ STOP typing (manual) ~ eventName:",
        eventName,
        "options:",
        options
      );
      dispatch(emit(eventName, options));
      stopTypingTimeout = undefined;
    }
  };

  const onTyping = (value) => {
    const isGroupChat = userInfoData?.group_details_id;

    let options = {
      user_id: userData?.user_id,
    };

    if (isGroupChat) {
      options.group_details_id = userInfoData?.group_details_id;
      options.user_name = userData?.full_name || userData?.username;
    } else {
      options.receiver_id = receiverInfo?.user_id;
    }

    if (value && value.trim().length > 0) {
      if (isTypingInput === false) {
        isTypingInput = true;
        const eventName = isGroupChat ? "group_isTyping" : "isTyping";
        console.log(
          "🚀 ~ START typing ~ eventName:",
          eventName,
          "options:",
          options
        );
        dispatch(emit(eventName, options));
      }
      resetStopTypingTimeout();
    } else {
      // Stop typing when input is empty
      if (isTypingInput === true) {
        stopTypingManually();
      }
    }
  };

  const handleBlockUnblock = async () => {
    setModalLoading(true);
    const resp = await blockUnBlockUser(receiverInfo?.user_id);
    if (resp !== undefined && resp?.data?.success) {
      const updatedArray = optionData?.map((item) => {
        if (item.key === "block") {
          return {
            ...item,
            text: item?.text === "Block" ? "UnBlock" : "Block",
          };
        }
        return item;
      });
      setOptionData(updatedArray);
      setUserInfo({
        ...userInfo,
        is_blocked: userInfo?.is_blocked ? false : true,
      });
      setModalLoading(false);
      setShowPopover(false);
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    } else {
      setModalLoading(false);
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
    setModalLoading(false);
  };

  const handleClearChatData = async () => {
    if (!isEmpty(messages)) {
      setModalLoading(true);
      const resp = await clearChatData(userInfo?.conversation_id);
      if (resp !== undefined && resp?.data?.success) {
        setMessages([]);
        setModalLoading(false);
        Toast.show("All chat is clear");
      } else {
        setModalLoading(false);
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  };

  const handleToReport = useCallback(async () => {
    setIsLoader(true);
    const data = {
      user_id: userData?.user_id,
      reason: translate(reportReason?.options),
      type: "chat",
      reporting_id: receiverInfo?.user_id,
    };

    if (reportReason?.options === "somethingElseText") {
      data.reason = reportTextInput;
    }
    if (
      reportReason?.options === "somethingElseText" &&
      reportTextInput === ""
    ) {
      setIsReasonErrMsg("Please enter,\nWhy you are report this user!");
    } else {
      const resp = await handleToReportUser(data);
      if (resp !== undefined && resp?.data?.success) {
        Toast.show(resp?.data?.message);
        setShowPopover(false);
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
        setIsLoader(false);
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        setShowPopover(false);
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
        setIsLoader(false);
      }
    }
    setShowPopover(false);
    setIsLoader(false);
  });

  const handleExitGroup = async () => {
    setExitGropLoader(true);
    const resp = await exitGroup(userInfo?.group_details_id);
    setExitGropLoader(false);
    setExitGroupModal(false);
  };

  const handelDeleteMessage = async (content = {}) => {
    setExitGropLoader(true);
    const resp = await deleteMessage(
      userInfo?.group_details_id,
      content?.message_id
    );
    if (resp?.success) {
      setDeleteModal({ visible: false, content: {} });
      await getMessagesList(Number(isPage?.page) || 1);
    }
    setExitGropLoader(false);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: BaseColors.white }}>
      <ImageBackground
        source={images.messageBg}
        resizeMode="cover"
        style={styles.bgSet}
      />

      <View style={styles.hederView}>
        <CHeader
          chat={true}
          isBackIcon={true}
          handleBackButton={() => {
            navigation.goBack();
          }}
          chatTitle={receiverInfo?.full_name || userInfoData?.groupName}
          userImage={receiverInfo?.user_dp || userInfoData?.imageUrl}
          optionData={
            userInfoData?.group_details_id
              ? [{ key: "exit_group", icon: "Leave", text: "Exit Group" }]
              : optionData
          }
          handleOptionsBtn={handleOptionsBtn}
          onPressChatHeader={() => {
            if (userInfoData?.group_details_id) {
              navigation.navigate("GroupInfo", {
                groupId: userInfoData?.group_details_id,
              });
            } else {
              navigation.navigate("ProfileNew1", {
                data: {
                  user_id: receiverInfo?.user_id,
                  user_dp: receiverInfo?.user_dp,
                },
                type: "anotherProfile",
              });
            }
          }}
          isGroupChat={userInfoData?.group_details_id ? true : false}
          groupMember={userInfoData?.groupMemberCount}
          showPopover={showPopover}
          setShowPopover={setShowPopover}
          isOnline={userInfoData?.isOnline || false}
        />
      </View>

      {screenLoader ? (
        <View style={styles.centerMain}>
          <MiniLoader size="medium" />
        </View>
      ) : null}

      <View style={{ flex: 1, marginBottom: 12 }}>
        <GiftedChat
          messages={[...new Set([...messages])]}
          user={{
            _id: 1,
          }}
          // custom inputBar
          renderInputToolbar={() => (
            <CustomChatInput
              onSendPress={(obj) => {
                if (!sendLoader) {
                  if (editMessage && editMessage?.key === "edit") {
                    handleEditMessage(editMessage?.content, message);
                  } else {
                    handleSend(obj, messages);
                  }
                }
              }}
              isEditMessage={editMessage}
              cancelEditMessage={() => {
                setEditMessage({});
                setMessage("");
              }}
              onTyping={(e) => onTyping(e)}
              onAttachmentFunc={() => onAttachmentFunc()}
              disable={userInfo?.is_blocked ? true : false}
              stopSound={stopSound}
              setMessage={setMessage}
              message={message}
              setKeyboardVisible={setKeyboardVisible}
              setIsOpenEmojiModal={setIsOpenEmojiModal}
              setKeyboardHeight={setKeyboardHeight}
              isOpenEmojiModal={isOpenEmojiModal}
              inputRef={inputRef}
              replyViewData={replyViewData}
              setReplyViewData={setReplyViewData}
              typingData={
                !isEmpty(typingData?.text) &&
                (userInfoData?.group_details_id
                  ? typingData?.group_details_id ===
                      userInfoData?.group_details_id &&
                    typingData?.user_id !== userData?.user_id
                  : typingData?.user_id === receiverInfo?.user_id)
              }
              isKeyboardVisible={isKeyboardVisible}
              startAudioRecord={startAudioRecord}
              setStartAudioRecord={setStartAudioRecord}
              timeLeft={timeLeft}
              setTimeLeft={setTimeLeft}
              isAudioDuration={isAudioDuration}
              setIsAudioDuration={setIsAudioDuration}
              sendLoader={sendLoader}
              setSendLoader={setSendLoader}
            />
          )}
          renderChatEmpty={() =>
            !screenLoader && (
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                  transform: [{ scaleY: -1 }],
                }}
              >
                <NoRecord title={"Empty"} type="chat" description="noChat" />
              </View>
            )
          }
          renderMessage={renderBubble}
          renderMessageAudio={renderBubble}
          messagesContainerStyle={{
            paddingBottom: !isEmpty(replyViewData) ? 60 : 12,
          }}
          inverted={!isEmpty(messages)}
          loadEarlier={isPage?.next_enable}
          infiniteScroll
          renderLoadEarlier={() => null}
          keyboardShouldPersistTaps="handled"
          onLoadEarlier={() => {
            if (isPage?.next_enable) {
              getMessagesList(Number(isPage?.page) + 1);
            }
          }}
        />
      </View>

      {/* Emoji  and sticker View */}

      {isOpenEmojiModal && !isKeyboardVisible ? (
        <>
          <View style={styles.mainEmojiContent}>
            <View style={styles.emojiAndStrikerMain}>
              {/* Emoji and sticker select View */}
              <View style={styles.emojiAndStrikerContent}>
                <TouchableOpacity
                  style={[
                    styles.selectedBox,
                    {
                      backgroundColor: seenSticker
                        ? BaseColors.white
                        : BaseColors.gray10,
                    },
                  ]}
                  activeOpacity={0.8}
                  onPress={() => setSeenSticker(!seenSticker)}
                >
                  <CustomIcon
                    name={"Smile"}
                    size={22}
                    color={BaseColors.black102}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.selectedBox,
                    {
                      backgroundColor: seenSticker
                        ? BaseColors.gray10
                        : BaseColors.white,
                    },
                  ]}
                  activeOpacity={0.8}
                  onPress={() => setSeenSticker(!seenSticker)}
                >
                  <CustomIcon
                    name={"cssIcon"}
                    size={18.5}
                    color={BaseColors.black102}
                  />
                </TouchableOpacity>
              </View>

              {/* Remove icon view */}
              <TouchableOpacity
                style={styles.removeIconsMain}
                activeOpacity={0.8}
                onPress={handleRemoveData}
              >
                <CustomIcon
                  name={"BsBackspace"}
                  size={24}
                  color={BaseColors.black102}
                />
              </TouchableOpacity>
            </View>
          </View>
          {seenSticker ? (
            <View style={{ minHeight: keyboardHeight - 28 }}>
              <CSticker
                onSelect={(s) =>
                  handleSend({
                    text: s.trim(),
                    reply_id: !isEmpty(replyViewData)
                      ? replyViewData?.currentMessage?.message_id
                      : null,
                    messageType: "sticker",
                    meta: !isEmpty(replyViewData) ? "stickerReply" : "",
                  })
                }
              />
            </View>
          ) : (
            <CEmojiPiker handlePick={handlePick} height={keyboardHeight - 28} />
          )}
        </>
      ) : null}

      {/* Delete and clear chat Modal */}

      {visible ? (
        <ModalComponent
          visible={visible}
          loading={modalLoading}
          setModalVisible={setModalVisible}
          modalTitle={showModalText?.modalTitle}
          buttonTxt={showModalText?.buttonTxt}
          cancelText={showModalText?.cancelText}
          isLoader={false}
          onClickSaveBtn={() => {
            if (showModalText?.type === "block") {
              setModalLoading(true);
              handleBlockUnblock();
            } else if (showModalText?.type === "delete") {
              handleClearChatData();
            }
            setModalVisible(false);
            setShowPopover(false);
          }}
          centerIconName={showModalText?.icon}
          modalHeder={showModalText?.modalHeder}
        />
      ) : null}

      {/* Exit Group Confirmation Modal */}
      {exitGroupModal && (
        <GroupConformationModal
          visible={exitGroupModal}
          header={"Exit Group"}
          content={`Do you really want to Exit this Group?`}
          optionOneLabel={"Yes"}
          optionTwoLabel={"Cancel"}
          onOptionOne={() => {
            // Add your exit group logic here
            handleExitGroup();
            // Call your API/socket function to exit group
          }}
          loading={exitGropLoader}
          onOptionTwo={() => setExitGroupModal(false)}
          onRequestClose={() => setExitGroupModal(false)}
        />
      )}

      {/* Report Modal */}
      {isReportModalMemo ? (
        <ReportModal
          visible={isReportModalMemo}
          setModalVisible={() => setIsReportModal(false)}
          selectedOptionForReason={(e) => {
            setReportReason(e);
          }}
          textInputData={reportTextInputMemo}
          setTextInputValue={(e) => {
            setReportTextinput(e);
            setIsReasonErrMsg("");
          }}
          reasonValue={reportReasonMemo}
          isErrMessage={reasonErrorMessageMemo}
          onBtnPress={() => {
            handleToReport();
          }}
          isLoading={isLoader}
        />
      ) : null}

      <Modal
        animationType="slide"
        transparent={true}
        animationInTiming={5000}
        animationOutTiming={5000}
        visible={deleteModal.visible}
        onRequestClose={() => {
          setDeleteModal({
            visible: false,
            content: {},
          });
        }}
      >
        <View style={styles.ovarlayStyle}>
          <View style={[styles.deletemodalView, { height: "20%" }]}>
            <View>
              <Text
                style={[
                  {
                    fontFamily: FontFamily.RobotoRegular,
                    fontSize: 18,
                    color: "#F75555",
                  },
                ]}
              >
                {"Are you sure delete this message?"}
              </Text>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  marginTop: 20,
                }}
              >
                <CButton
                  onBtnClick={() => handelDeleteMessage(deleteModal?.content)}
                  loading={exitGropLoader}
                >
                  Yes
                </CButton>
                <CButton
                  type="outlined"
                  onBtnClick={() =>
                    setDeleteModal({
                      visible: false,
                      content: {},
                    })
                  }
                >
                  No
                </CButton>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default MessagesInfo;
