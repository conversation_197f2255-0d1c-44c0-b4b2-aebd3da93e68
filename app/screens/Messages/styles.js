import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, Platform, StyleSheet } from "react-native";

const HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  bgSet: {
    position: "absolute",
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
    opacity: 0.1,
    height: HEIGHT,
  },
  centerMain: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  centerMainEmptyView: {
    height: Dimensions.get("window").height / 1.5,
    justifyContent: "center",
  },
  emojiAndStrikerMain: {
    flexDirection: "row",
    justifyContent: "center",
    marginHorizontal: 15,
  },
  selectedBox: {
    width: 60,
    justifyContent: "center",
    alignItems: "center",
  },
  emojiAndStrikerContent: {
    flexDirection: "row",
    borderWidth: 1,
    borderRadius: 5,
    overflow: "hidden",
    borderColor: "#BDBDBD",
    minHeight: 28,
    marginBottom: 10,
  },
  timerMain: {
    position: "absolute",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    bottom: -12,
  },
  timerText: {
    color: BaseColors.black,
    fontSize: 10.5,
  },
  hederView: {
    marginVertical: 10,
  },
  smileIcon: {
    marginLeft: 10,
  },
  sendBtnMain: {
    backgroundColor: BaseColors.activeTab,
    height: 45,
    width: 45,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  micIcons: {
    marginHorizontal: 10,
  },
  inputToolbar: {
    borderTopWidth: 0,
    marginBottom: 5,
    paddingHorizontal: 15,
  },
  textInput: {
    borderRadius: 10,
    paddingHorizontal: 10,
    marginHorizontal: 10,
    backgroundColor: BaseColors.gray8,
  },
  sendIcon: {
    marginRight: 10,
    marginBottom: 5,
  },
  accessoryStyle: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 10,
  },
  accessoryWrapperStyle: {
    marginLeft: 5,
    marginRight: 5,
  },
  emojiIcon: {
    marginRight: 5,
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: -12,
    paddingTop: 12,
    width: "100%",
  },
  shareButton: {
    borderWidth: 1,
    height: 40,
    width: 40,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 12,
    borderRadius: 23,
    backgroundColor: "#F0F9FF",
    borderColor: BaseColors.primary,
  },
  inputContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginHorizontal: 12,
    backgroundColor: BaseColors.gray8,
  },
  input: {
    flex: 1,
    marginRight: 10,
    padding: 8,
    fontFamily: FontFamily.medium,
    fontSize: 18,
    color: BaseColors.labelColor,
    height: "auto",
  },
  sendButton: {
    height: 45,
    width: 45,
  },
  toastMainView: {
    borderWidth: 0.6,
    marginTop: 12,
    marginHorizontal: 16,
    padding: 6,
    backgroundColor: "transparent",
    borderRadius: 8,
    borderColor: BaseColors.gray,
    alignItems: "center",
    justifyContent: "center",
  },
  toastText: {
    fontSize: 14,
    fontFamily: FontFamily.regular,
    color: BaseColors.gray,
    textAlign: "center",
    marginBottom: Platform.OS === "ios" ? 0 : 3,
  },
  staticMsgView: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "flex-end",
    marginBottom: 10,
    marginRight: 12,
  },
  staticMsgTxt: {
    flex: 1,
    color: "#0596FE",
    fontSize: 14,
    borderWidth: 1,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 22,
    borderColor: "#0596FE",
  },
  pdfImg: { width: 30, height: 30 },
  mainMsgView: {
    marginHorizontal: 16,
    paddingTop: 5,
    paddingBottom: 5,
    flex: 1,
    flexDirection: "row",
    marginBottom: 20,
  },
  msgTxt: {
    fontSize: 16,
    fontFamily: FontFamily.medium,
    maxWidth: "85%",
    borderRadius: 10,
  },
  fileMsgView: {
    alignItems: "center",
    maxWidth: "65%",
    padding: 10,
    borderRadius: 10,
  },
  mainEmojiContent: { width: "100%", backgroundColor: BaseColors.White },
  removeIconsMain: {
    position: "absolute",
    right: 0,
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  loader: {
    height: 100,
    width: 100,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalView: {
    margin: 20,
    backgroundColor: BaseColors.white,
    borderRadius: 20,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    minWidth: 280,
    maxWidth: 340,
  },
  header: {
    fontSize: 20,
    color: BaseColors.black,
    fontFamily: FontFamily.InterBold,
    textAlign: "center",
    marginBottom: 10,
  },
  modalcontent: {
    fontSize: 16,
    color: BaseColors.black,
    fontFamily: FontFamily.InterRegular,
    textAlign: "center",
    marginBottom: 20,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  deletemodalView: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderRadius: 12,
    width: "100%",
    height: "90%",
  },
});
export default styles;
