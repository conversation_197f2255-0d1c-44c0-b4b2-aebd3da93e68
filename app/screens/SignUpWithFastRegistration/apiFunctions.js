import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { isUndefined } from "lodash-es";

export const onSubmit = async (data, signUpType, googleId, isEdit) => {
  let finalData = {
    full_name: data?.fullName?.trim(),
    username: data?.userName,
    phone_code: data?.countryCode,
    phone_number: data?.mobileNo,
    email: data?.email,
    type: signUpType,
    nature_of_business_id: data?.companyNatureOfBusiness,
    country: data?.country,
    state: data?.state,
  };

  if (!isUndefined(data?.companyName)) {
    finalData["company_name"] = data?.companyName?.trim();
  }

  if (signUpType === "social" && isEdit === false) {
    finalData.social_connection_id = googleId;
  }

  if (data?.userId) {
    finalData["user_id"] = data?.userId;
  }
  if (!data?.userId && signUpType !== "social") {
    finalData["password"] = data?.password;
  }
  if (data?.referralCode) {
    finalData["code"] = data?.referralCode;
  }
  if (data?.profileImage !== undefined) {
    finalData["profile_picture"] = data?.profileImage;
  }

  try {
    const resp = await getApiData(
      `${!data?.userId ? BaseSetting.endpoints.userProfile : BaseSetting.endpoints.UpdateUserData}`,
      "POST",
      finalData,
      true,
      true
    );

    return resp;
  } catch (error) {
    console.error("🚀 ~ onSubmit ~ error:", error);
  }
};

export const getData = async (data) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getUserData}${data}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

export const referralApiCall = async (referralCode) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.addReferralCode}?code=${referralCode}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ referralApiCall ~ error:", error);
  }
};

export const natureOfBusinessData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=nature_of_business`,
      "GET"
    );

    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp;
      } else {
        console.log("Something went wrong please try again");
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};
export const getCountryData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=countries`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(resp?.data?.message);
      }
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Get State Data Form DB
export const getCountryStateData = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=states&country_id=${id}`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(resp?.data?.message);
      }
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};
