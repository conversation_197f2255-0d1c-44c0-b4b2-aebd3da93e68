import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Image,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Keyboard,
  Dimensions,
} from "react-native";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { PermissionsAndroid, Platform } from "react-native";
import { PERMISSIONS, check } from "react-native-permissions";
import CInput from "@components/TextInput";
import Toast from "react-native-simple-toast";
import CDropdown from "@components/CDropDown";
import CButton from "@components/CButton";
import styles from "./styles";
import { getData, onSubmit, referralApiCall } from "./apiFunctions";
import {
  checkImg,
  cropImage,
  getMobileNumberLength,
  mobileValidation,
  selectAndCropImage,
} from "@app/utils/commonFunction";
import isUndefined from "lodash-es/isUndefined";
import isEmpty from "lodash-es/isEmpty";
import { CustomIcon } from "@config/LoadIcons";
import authAction from "@redux/reducers/auth/actions";
import { useDispatch, useSelector } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { BaseColors } from "@config/theme";
import ReferCodeModal from "@components/ReferCodeModal";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";
import CCamera from "@components/CameraButton/CCamera";
import { natureOfBusinessData } from "./apiFunctions";
import { getCountryStateData } from "./apiFunctions";
import { getCountryData } from "./apiFunctions";
import AsyncStorage from "@react-native-async-storage/async-storage";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const { images } = require("@config/images");
const FastImage = require("react-native-fast-image");

const {
  setCompanyId,
  setUserId,
  setIsReferralCode,
  setUserData,
  setAccessToken,
} = authAction;
const PersonalDetailSchema = yup.object().shape({
  fullName: yup
    .string()
    .required("enterFullName")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter")
    .trim("whiteSpaceErrMsg"),
  userName: yup
    .string()
    .required("enterYourUsername")
    .matches(/^\S+$/, "usernameWhiteSpace")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter"),
  email: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  mobileNo: yup
    .string()
    .required("enterMobileNumber")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  password: yup
    .string()
    .required("enterYourPasswordErrMSg")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "passwordsErrMSg"
    ),

  countryCode: yup.string().default("91"),
  userId: yup.string(),
  referralCode: yup.string(),
  country: yup.string().required("selectCountry"),
  companyNatureOfBusiness: yup.string().required("natureOfBusinessErrMsg"),
});

const PersonalDetailSchemaUpdateTime = yup.object().shape({
  fullName: yup
    .string()
    .required("enterFullName")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter")
    .trim("whiteSpaceErrMsg"),
  userName: yup
    .string()
    .required("enterYourUsername")
    .matches(/^\S+$/, "usernameWhiteSpace")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter"),
  email: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  mobileNo: yup
    .string()
    .required("enterMobileNumber")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  countryCode: yup.string().default("91"),
  userId: yup.string(),
  referralCode: yup.string(),
  country: yup.string().required("selectCountry"),
  companyNatureOfBusiness: yup.string().required("natureOfBusinessErrMsg"),
});

const PersonalDetailSchemaWithoutPassword = yup.object().shape({
  fullName: yup
    .string()
    .required("enterFullName")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "You can enter maximum 15 character")
    .trim("whiteSpaceErrMsg"),
  userName: yup
    .string()
    .required("enterYourUsername")
    .matches(/^\S+$/, "usernameWhiteSpace")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter"),
  email: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  mobileNo: yup.string().required("enterMobileNumber"),
  countryCode: yup.string().default("91"),
  userId: yup.string(),
  referralCode: yup.string(),
  country: yup.string().required("selectCountry"),
  companyNatureOfBusiness: yup.string().required("natureOfBusinessErrMsg"),
});

const SignUpWithFastRegistration = ({ navigation, route }) => {
  const routeData = route?.params?.data;

  // Redux Variable
  const dispatch = useDispatch();
  const { user_id, isReferralCode, userData } = useSelector((e) => e.auth);

  // useRef :
  const companyRef = useRef(null);
  const fullNameRef = useRef(null);
  const userNameRef = useRef(null);
  const emailRef = useRef(null);
  const mobileNoRef = useRef(null);
  const passRef = useRef(null);
  const companyNatureOfBusinessRef = useRef(null);
  const stateRef = useRef(null);
  const countryRef = useRef(null);

  // State's
  const [mobileNoLength, setMobileLength] = useState(10);
  const [profileImage, setProfileImage] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [referralCodeModal, setIsReferralModal] = useState(false);
  const [referralCode, setReferralCode] = useState("");
  const [isReferralCodeErr, setIsReferralCodeRrr] = useState("");
  const [countryCode, setCountryCode] = useState("91");
  const [profilePictureModal, setProfilePictureModal] = useState(false);
  const [imagePath, setImagePath] = useState({});
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [natureOfBusinessData1, setNatureOfBusiness] = useState([]);
  const [countryData, setCountryData] = useState([]);
  const [selectedCountryID, setSelectedCountryID] = useState("");
  const [selectedCountryState, setSelectedCountryState] = useState([]);
  const [gender, setGender] = useState("male");

  // Memo's
  const mobileNoLengthMemo = useMemo(() => mobileNoLength, [mobileNoLength]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const referralCodeModalMemo = useMemo(
    () => referralCodeModal,
    [referralCodeModal]
  );
  const natureOfBusinessDataMemo = useMemo(
    () => natureOfBusinessData1,
    [natureOfBusinessData1]
  );
  const referralCodeMemo = useMemo(() => referralCode, [referralCode]);
  const isReferralCodeErrMemo = useMemo(
    () => isReferralCodeErr,
    [isReferralCodeErr]
  );

  // Form Variable
  const {
    control,
    setValue,
    handleSubmit,
    getValues,
    clearErrors,
    reset,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(
      (isEmpty(user_id) && !routeData?.social_connection) ||
        (isEmpty(route?.params?.id) &&
          !routeData?.social_connection &&
          routeData?.social_connection !== undefined)
        ? PersonalDetailSchema
        : routeData?.social_connection &&
            (isEmpty(profileImage) || profileImage === undefined)
          ? PersonalDetailSchemaWithoutPassword
          : PersonalDetailSchemaUpdateTime
    ),
  });
  const handleKeyPress = ({ nativeEvent: { key: keyValue } }) => {
    if (keyValue === "") {
    }
  };

  useEffect(() => {
    setValue("countryCode", "91");
  }, []);

  const fetchData = async () => {
    const data = await natureOfBusinessData();
    setNatureOfBusiness(data?.data?.data);
  };

  const fetchCountryStateData = async (id) => {
    const resp = await getCountryStateData(id);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setSelectedCountryState(updatedData);
  };

  const fetchCountryData = async () => {
    const resp = await getCountryData();
    if (resp !== undefined) {
      let updatedData = resp?.map((val) => {
        return {
          ...val,
          label: val.name,
          value: val.id,
        };
      });

      setCountryData(updatedData);
    }
  };
  const pickDocument = async (type) => {
    setIsLoading(true);

    try {
      await requestStoragePermission();
      let resultData = [];
      if (type === "captureImg") {
        resultData = await cropImage(imagePath?.uri, 120, 120, "circle");
        setImagePath({});
      } else {
        resultData = await selectAndCropImage(120, 120);
      }

      let result = [
        {
          fileCopyUri: null,
          name: "images.jpeg",
          size: resultData?.size,
          type: resultData?.mime || "images.jpeg",
          uri: resultData?.path,
        },
      ];
      if (result[0]?.uri) {
        if (checkImg(result[0])?.isValid) {
          setProfileImage(result[0]?.uri);
          setValue("profileImage", result[0]);
          setProfilePictureModal(false);
          setError("profileImage", "");
        } else {
          Toast.show(checkImg(result[0])?.errMsg);
          setProfilePictureModal(false);
        }
        if (
          result[0].type === "image/jpeg" ||
          result[0].type === "image/png" ||
          result[0].type === "image/jpg" ||
          result[0].type === "image/heic"
        ) {
        }

        return result[0];
      }
    } catch (err) {
      console.error("Profile Pick Error ====>", err);
      setProfilePictureModal(false);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  const requestStoragePermission = async () => {
    if (Platform.OS === "android") {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
      ).then((result) => {
        if (result) {
          console.log("Permission is OK");
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
          ).then((res) => {
            if (res) {
            } else {
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.READ_EXTERNAL_STORAGE).then((res) => {
        if (res !== "granted") {
          console.log("permission granted === ");
        }
      });
    }
  };

  const handleToReferralApiCall = useCallback(async () => {
    setIsLoading(true);
    if (isEmpty(referralCode)) {
      setIsReferralCodeRrr("Please enter your referral code");
      setIsLoading(false);
    } else {
      const resp = await referralApiCall(referralCode);

      if (resp?.data?.success) {
        setValue("referralCode", referralCode);
        setIsLoading(false);
        dispatch(setIsReferralCode(true));
        setIsReferralModal(false);
      } else {
        setIsReferralCodeRrr(resp?.data?.message);
        setReferralCode("");
        setIsLoading(false);
      }
      setIsLoading(false);
    }
  }, [isLoadingMemo, referralCode]);

  const handleToValidation = async (data) => {
    const isValid =
      Number(selectedCountryID) == 101 && isUndefined(data?.state);
    const mobileNumberValidation = await mobileValidation(
      getValues("mobileNo"),
      mobileNoLengthMemo
    );
    if (!mobileNumberValidation?.isValid) {
      setError("mobileNo", {
        message: mobileNumberValidation?.validMessage,
      });
    }
    if (isValid) {
      setError("state", {
        message: "Please select state",
      });
    }

    if (!isValid && mobileNumberValidation?.isValid) {
      handleToSubmit(data);
    }
  };
  const handleToSubmit = useCallback(
    async (data) => {
      const mobileNumberValidation = await mobileValidation(
        data?.mobileNo,
        mobileNoLengthMemo
      );
      if (!mobileNumberValidation?.isValid) {
        setError("mobileNo", {
          message: mobileNumberValidation?.validMessage,
        });
      }
      if (mobileNumberValidation?.isValid) {
        const type = routeData?.social_connection
          ? "social"
          : routeData?.singUpType || userData?.type || "email";
        const googleId = routeData?.social_connection
          ? routeData?.googleId
          : null;
        setIsLoading(true);
        Keyboard.dismiss();
        const respData = await onSubmit(
          data,
          type,
          googleId,
          route?.params?.isEdit || false
        );
        if (respData?.data?.success) {
          if (isEmpty(user_id)) {
            dispatch(setCompanyId(respData?.data?.data?.company_id));
            AsyncStorage.setItem("token", respData?.data?.token);
            dispatch(setAccessToken(respData?.data?.token));
            dispatch(setUserId(respData?.data?.data?.user_id));
          }
          dispatch(setUserData(respData?.data?.data));

          navigation.navigate("SingUpWithFastRegistrationSecond", {
            user_id: respData?.data?.data?.user_id,
            company_id: respData?.data?.data?.company_id,
            isEdit: route?.params?.isEdit || false,
            screen: route?.params?.screen,
          });
          reset();
          setProfileImage({});
          setIsLoading(false);
        } else {
          if (respData?.data?.message === "Network error") {
            handleToSubmit(data);
          } else {
            Toast.show(respData?.data?.message);
            setIsLoading(false);
          }
        }

        setIsLoading(false);
      }
    },
    [isLoadingMemo]
  );

  const getUserData = useCallback(async () => {
    setIsLoading(true);
    const respData = await getData(user_id || route?.params?.id);
    if (respData?.data?.success && respData?.data?.data) {
      setValue("fullName", respData?.data?.data?.full_name);
      setValue("countryCode", String(respData?.data?.data?.phone_code));
      setCountryCode(String(respData?.data?.data?.phone_code));
      setValue("mobileNo", String(respData?.data?.data?.phone_number));
      setValue("email", respData?.data?.data?.email);
      setValue("userName", respData?.data?.data?.username);
      setValue("userId", respData?.data?.data?.user_id);
      setValue("profileImage", respData?.data?.data?.profile_picture_name);
      setProfileImage(respData?.data?.data?.profile_picture_url || {});
      setGender(respData?.data?.data?.gender || "male");
      const country_length = await getMobileNumberLength(
        respData?.data?.data?.sortname
      );
      if (respData?.data?.data?.company_name) {
        setValue("companyName", respData?.data?.data?.company_name);
      }
      setValue(
        "companyNatureOfBusiness",
        respData?.data?.data?.nature_of_business_id || undefined
      );
      setSelectedCountryID(Number(respData?.data?.data?.country));
      setValue("country", Number(respData?.data?.data?.country) || undefined);
      setValue("state", Number(respData?.data?.data?.state) || undefined);
      setMobileLength(country_length);
    } else {
      setIsLoading(false);
    }
    setIsLoading(false);
  }, [isLoadingMemo, user_id]);

  useFocusEffect(
    useCallback(() => {
      fetchData();
      fetchCountryData();
      fetchCountryStateData(101);
      if (!isEmpty(user_id) || !isEmpty(route?.params?.id)) {
        getUserData();
      }
      if (!isEmpty(routeData) || !isUndefined(routeData)) {
        handleToSetValue();
      }
      if (!isReferralCode) {
        if (Platform.OS === "ios") {
          setTimeout(() => {
            setIsReferralModal(true);
          }, 1000);
        } else {
          setIsReferralModal(true);
        }
      }
    }, [user_id, routeData])
  );
  const handleToSetValue = () => {
    if (routeData?.singUpType === "phone") {
      setValue("mobileNo", routeData?.text);
      setValue("countryCode", routeData?.countryCode);
    } else if (routeData?.social_connection) {
      setValue("fullName", routeData?.name);
      setValue("email", routeData?.email);
      setValue("profileImage", routeData?.profile);
      setProfileImage(routeData?.profile);
    } else {
      setValue("email", routeData?.text);
    }
  };

  useEffect(() => {
    if (isCameraOpen) {
      setProfilePictureModal(false);
    }
    if (!isCameraOpen && imagePath?.uri) {
      // setProfilePictureModal(false);
      pickDocument("captureImg");
    }
  }, [isCameraOpen]);

  const CustomHeader = ({ handleBackButton = () => {} }) => {
    return (
      <View
        style={{
          alignItems: "center",
          flexDirection: "row",
        }}
      >
        <TouchableOpacity
          style={[
            styles.backIconView,
            { borderColor: BaseColors.black, zIndex: 111 },
          ]}
          activeOpacity={0.8}
          onPress={handleBackButton}
        >
          <CustomIcon name="back-arrow" size={18} color={BaseColors.black} />
        </TouchableOpacity>
        <View style={{ position: "absolute", width: "100%" }}>
          <Image
            source={images.footMainLogo}
            style={styles.mainFootLogo}
            resizeMode={"contain"}
          />
        </View>
      </View>
    );
  };
  return (
    <SafeAreaView style={styles.mainView}>
      {/* Camera open component */}
      {isCameraOpen ? (
        <View style={{ flex: 1 }}>
          <CCamera
            setImagePath={setImagePath}
            isCameraOpen={isCameraOpen}
            setIsCameraOpen={setIsCameraOpen}
            recordVideo={false}
          />
        </View>
      ) : (
        <>
          <CustomHeader handleBackButton={() => navigation.goBack()} />
          {/* <CHeader handleBackButton={() => navigation.goBack()} /> */}
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={styles.KeyboardAvoidingViewStyle}
          >
            <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
              <Text style={styles.headingText}>
                {translate("letsCreateAccountText")}
              </Text>
              <TouchableOpacity
                style={styles.profileImgMainViewStyle}
                activeOpacity={0.8}
                onPress={() => setProfilePictureModal(true)}
              >
                {isEmpty(profileImage) ? (
                  <View style={styles.profileImgMainView}>
                    <FastImage
                      source={
                        gender === "male"
                          ? images.manAvatar
                          : gender === "female"
                            ? images.womanAvatar
                            : images.manAvatar
                      }
                      style={styles.profileImgStyle}
                    />
                  </View>
                ) : (
                  <View style={styles.profileImgMainView}>
                    {/* <Image
                      source={{ uri: profileImage }}
                      style={styles.profileImgStyle}
                    /> */}
                    <FastImage
                      source={{ uri: profileImage, priority: "high" }}
                      style={styles.profileImgStyle}
                    />
                  </View>
                )}
                <TouchableOpacity
                  style={styles.editMainViewStyle}
                  activeOpacity={0.9}
                  onPress={async () => setProfilePictureModal(true)}
                >
                  <CustomIcon
                    name={"Edit-Square"}
                    size={25}
                    color={BaseColors.activeTab}
                  />
                </TouchableOpacity>
              </TouchableOpacity>
              <Text style={styles.profileTextStyle}>
                {translate("profilePhotoText")}
              </Text>

              {errors?.profileImage?.message ? (
                <View style={styles.errorMsgMainView}>
                  <CustomIcon
                    name={"BsExclamationCircle"}
                    size={18}
                    color={"#D6002E"}
                  />
                  <Text style={styles.errorTxt}>
                    {errors?.profileImage?.message}
                  </Text>
                </View>
              ) : null}
              {/* Form View */}
              <View style={styles.formMainView}>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyName"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={companyRef}
                        placeholderText={"Company Name"}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          fullNameRef.current.focus();
                        }}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="fullName"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={fullNameRef}
                        placeholderText={translate("fullNameText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          userNameRef.current.focus();
                        }}
                        isError={errors?.fullName}
                        isErrorMsg={errors?.fullName?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="userName"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={userNameRef}
                        placeholderText={translate("userNameText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        maxLength={150}
                        onSubmit={() => {
                          (routeData?.singUpType !== "phone" &&
                            (!isEmpty(routeData?.text) ||
                              !isEmpty(routeData?.email))) ||
                          userData?.type === "email" ||
                          userData?.type === "social"
                            ? emailRef.current.focus()
                            : mobileNoRef.current.focus();
                        }}
                        isError={errors?.userName}
                        isErrorMsg={errors?.userName?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="email"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={emailRef}
                        placeholderText={translate("EmailText")}
                        returnKeyType="next"
                        value={value}
                        onChange={(e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                        }}
                        onSubmit={() => {
                          mobileNoRef.current.focus();
                        }}
                        isError={errors?.email}
                        isErrorMsg={errors?.email?.message}
                        disabled={
                          (routeData?.singUpType !== "phone" &&
                            (!isEmpty(routeData?.text) ||
                              !isEmpty(routeData?.email))) ||
                          userData?.type === "email" ||
                          userData?.type === "social"
                            ? true
                            : false
                        }
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="mobileNo"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={mobileNoRef}
                        placeholderText={translate("MobileNumberPlaceHolder")}
                        returnKeyType="next"
                        value={value}
                        onChange={async (e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                          const mobileNumberValidation = await mobileValidation(
                            e,
                            mobileNoLengthMemo
                          );
                          if (!mobileNumberValidation?.isValid) {
                            setError("mobileNo", {
                              message: mobileNumberValidation?.validMessage,
                            });
                          } else if (mobileNumberValidation?.isValid) {
                            clearErrors("mobileNo");
                          }
                        }}
                        onSubmit={() => {
                          clearErrors("mobileNo");
                          isEmpty(user_id) || isEmpty(route?.params?.id)
                            ? passRef.current.focus()
                            : Keyboard.dismiss();
                        }}
                        keyBoardType="number-pad"
                        maxLength={mobileNoLengthMemo}
                        inputType="mobile"
                        isError={errors?.mobileNo}
                        isErrorMsg={errors?.mobileNo?.message}
                        selectedCountryCode={(e) => {
                          const country_length = getMobileNumberLength(
                            e?.country_code
                          );
                          setMobileLength(country_length);
                          setValue("mobileNo", undefined);
                          const yourData = e?.dial_code?.split("+");
                          setCountryCode(yourData);
                          setValue("countryCode", yourData[1]);
                        }}
                        disabled={
                          (routeData?.singUpType === "phone" &&
                            !isEmpty(routeData?.text)) ||
                          userData?.type === "phone"
                            ? true
                            : false
                        }
                        onKeyPress={handleKeyPress}
                        countryCodeValue={
                          getValues("countryCode")
                            ? `+${getValues("countryCode")}`
                            : `+${countryCode}`
                        }
                      />
                    </Animated.View>
                  )}
                />
                {isEmpty(user_id) && !routeData?.social_connection ? (
                  <Controller
                    control={control}
                    rules={{
                      required:
                        isEmpty(user_id) && !routeData?.social_connection
                          ? true
                          : false,
                    }}
                    name="password"
                    render={({ field: { onChange, value } }) => (
                      <Animated.View
                        style={styles.commonView}
                        entering={FadeInDown}
                      >
                        <CInput
                          reference={passRef}
                          placeholderText={translate("passWordText")}
                          returnKeyType="next"
                          value={value}
                          passwordInputField={true}
                          onChange={(e) => {
                            const sanitizedText = e.replace(/\s+/g, "");
                            onChange(sanitizedText);
                          }}
                          onKeyPress={(e) => {
                            handleKeyPress(e);
                          }}
                          onSubmit={() => {
                            // passwordText.current.focus();
                          }}
                          isError={errors?.password}
                          isErrorMsg={errors?.password?.message}
                        />
                      </Animated.View>
                    )}
                  />
                ) : null}
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="companyNatureOfBusiness"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      entering={FadeInDown}
                      style={styles.commonView}
                    >
                      <CDropdown
                        reference={companyNatureOfBusinessRef}
                        labelplaceholder={translate(
                          "CompanyNatureBusinessText"
                        )}
                        data={natureOfBusinessDataMemo}
                        setItem={(e) => onChange(e?.value)}
                        value={value}
                        isError={errors?.companyNatureOfBusiness}
                        isErrorMsg={errors?.companyNatureOfBusiness?.message}
                      />
                    </Animated.View>
                  )}
                />

                <View style={styles.addressMainView}>
                  <Controller
                    control={control}
                    rules={{
                      required: true,
                    }}
                    name="country"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <Animated.View
                          entering={FadeInDown}
                          style={styles.commonView}
                        >
                          <CDropdown
                            reference={countryRef}
                            labelplaceholder={translate("countryText")}
                            data={countryData}
                            setItem={(e) => {
                              onChange(e?.value);
                              setSelectedCountryID(e?.value);
                              if (e?.value !== 101) {
                                clearErrors("state");
                              }
                            }}
                            value={value}
                            isError={errors?.country}
                            isErrorMsg={errors?.country?.message}
                            WIDTH_DROP_DOWN={
                              value === 101
                                ? Dimensions.get("window").width / 2 - 30
                                : Dimensions.get("window").width - 40
                            }
                            position={"top"}
                          />
                        </Animated.View>
                      );
                    }}
                  />

                  {selectedCountryID === 101 ? (
                    <Controller
                      control={control}
                      rules={{
                        required: selectedCountryID === 101,
                      }}
                      name="state"
                      render={({ field: { onChange, value } }) => {
                        return (
                          <Animated.View
                            entering={FadeInDown}
                            style={[styles.commonView, { marginLeft: 20 }]}
                          >
                            <CDropdown
                              reference={stateRef}
                              labelplaceholder={translate("stateText")}
                              data={selectedCountryState}
                              setItem={(e) => onChange(e?.value)}
                              value={value}
                              isError={errors?.state}
                              isErrorMsg={errors?.state?.message}
                              WIDTH_DROP_DOWN={
                                Dimensions.get("window").width / 2 - 30
                              }
                              position={"top"}
                            />
                          </Animated.View>
                        );
                      }}
                    />
                  ) : null}
                </View>
              </View>
            </ScrollView>
            <CButton
              style={styles.buttonView}
              onBtnClick={handleSubmit(handleToValidation)}
              loading={isLoadingMemo}
            >
              {translate("nextText")}
            </CButton>
            <ReferCodeModal
              visible={referralCodeModalMemo}
              setModalVisible={() => setIsReferralModal(!referralCodeModalMemo)}
              setReferralCode={(e) => {
                setReferralCode(e), setIsReferralCodeRrr("");
              }}
              referralValue={referralCodeMemo}
              isError={isReferralCodeErrMemo}
              onSubmit={() => handleToReferralApiCall()}
              isLoading={isLoadingMemo}
            />
            {profilePictureModal ? (
              <AlreadyHaveStoryModal
                visible={profilePictureModal}
                setModalVisible={(e) => setProfilePictureModal(e)}
                title1="captureFromCamera"
                title2="chooseFromGallery"
                onPressTitle1={() => setIsCameraOpen(true)}
                onPressTitle2={() => pickDocument("selectImg")}
              />
            ) : null}
          </KeyboardAvoidingView>
        </>
      )}
    </SafeAreaView>
  );
};
export default memo(SignUpWithFastRegistration);
