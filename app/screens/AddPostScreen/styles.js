import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";
import { getStatusBarHeight, isIPhoneX } from "react-native-status-bar-height";

const { width, height } = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainInputStyle: {
    marginTop: 10,
  },
  sharedTextView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 20,
    backgroundColor: BaseColors.white,
    paddingHorizontal: 5,
  },
  switchCardView: {
    borderRadius: 20,
    overflow: "hidden",
  },
  textView: {
    fontSize: 17,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  paginationView: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
  },
  renderPaginationDotsView: {
    width: 12,
    height: 12,
    borderRadius: 25,
    marginHorizontal: 5,
    borderWidth: 1.5,
    borderColor: BaseColors.activeTab,
  },
  headerMainView: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 18,
  },
  cancelTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  headingTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: "#322F2F",
  },

  mainButton: {
    position: "absolute",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
    bottom: 0,
    height: "10%",
  },
  addMoreMain: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  addMoreText: {
    backgroundColor: "rgba(38, 38, 38, 0.8)",
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 50,
    color: "#fff",
    marginBottom: 10,
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center",
    flexDirection: "row",
    gap: 5,
  },
  mainView: {
    backgroundColor: BaseColors.white,
    flex: 1,
  },
  mainFootLogo: {
    height: 120,
    width: 120,
    marginTop: Platform.OS === "ios" ? 0 : 0,
    alignSelf: "center",
  },
  headingText: {
    fontSize: 34,
    fontFamily: FontFamily.RobotSemiBold,
    color: "#343434",
    textAlign: "center",
  },
  contentView: {
    marginHorizontal: 25,
    marginBottom: 80,
  },
  descText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#000000",
    marginTop: 15,
    textAlign: "center",
  },
  cInputStyle: {
    marginTop: 25,
  },
  cInputStyle1: {
    marginTop: 10,
  },
  btnStyle: {
    marginTop: 45,
  },
  PsdText: {
    marginTop: 10,
  },
  dropdown: {
    // position: "absolute",
    // top: 70,
    height: 200,
    borderRadius: 8,
    elevation: 4,
    shadowColor: "#000",
    borderWidth: 1,
    borderColor: BaseColors.gray,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    maxHeight: 200,
    backgroundColor: "#fff",
    overflow: "hidden",
  },
  dropdownItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderColor: "#f0f0f0",
  },
  hashIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#ed1941",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  hashText: {
    color: "white",
    fontWeight: "bold",
    fontSize: 18,
  },
  tagText: {
    fontWeight: "bold",
    fontSize: 16,
  },
  subText: {
    color: "#888",
    fontSize: 12,
  },
  tag: {
    flexDirection: "row",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 5,
    marginRight: 5,
    marginBottom: 5,
    alignItems: "center",
    borderWidth: 1,
  },
  textinputstyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#555454",
  },
  tagItem: {
    backgroundColor: "#f0f0f0",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 5,
    marginRight: 5,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  selectedTagsContainer: {
    marginBottom: 10,
    padding: 10,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  selectedTagsLabel: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: "#495057",
    marginBottom: 8,
  },
  selectedTagsWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  selectedTagItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: BaseColors.activeTab,
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 15,
    marginRight: 8,
    marginBottom: 6,
  },
  selectedTagText: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: "#ffffff",
    marginRight: 6,
  },
  removeTagIcon: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoBold,
    color: "#ffffff",
    backgroundColor: "rgba(255,255,255,0.3)",
    borderRadius: 8,
    width: 16,
    height: 16,
    textAlign: "center",
    lineHeight: 16,
  },
});

export default styles;
