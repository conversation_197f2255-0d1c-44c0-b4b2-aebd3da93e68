import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Dimensions, Image, SafeAreaView, View } from "react-native";
import isEmpty from "lodash-es/isEmpty";
import CButton from "@components/CButton";
import CImagePicker from "@components/CImagePicker";
import { translate } from "../../lang/Translate";
import styles from "./styles";
import Loader from "@components/Loader";
import { useFocusEffect } from "@react-navigation/native";
import CCamera from "@components/CameraButton/CCamera";
import MultipleImageView from "@components/MultipleImageView/MultipleImageView";
import CHeader from "@components/CHeader";
import { cropImage } from "@app/utils/commonFunction";
import { getStatusBarHeight, isIPhoneX } from "react-native-status-bar-height";

const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;
const Video = require("react-native-video").default;

const AddPostScreen = ({ navigation, route }) => {
  // it will true when user back from create post form
  const cameraImg = route?.params?.cameraImg || false;
  // it will true when user from home
  const goToPost = route?.params?.goToPost || false;

  // get Device HEIGHT WIDTH
  HEIGHT = Dimensions.get("window").height;
  WIDTH = Dimensions.get("window").width;

  // State's
  const [selectedImage, setSelectedImage] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [imagePath, setImagePath] = useState(null);
  const [videoPath, setVideo] = useState(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [timer, setTimer] = useState(-1);
  const [addMore, setAddMore] = useState(false);
  const [SelectedPostData, setSelectedPostData] = useState(false);

  // Memo's
  const selectedImageMemo = useMemo(() => selectedImage, [selectedImage]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);

  // set Default Data when user come from home
  useFocusEffect(
    useCallback(() => {
      if (goToPost) {
        setIsCameraOpen(false);
        setImagePath(null);
        setVideo(null);
        setTimer(-1);
        setAddMore(false);
        setSelectedImage([]);
      }
    }, [goToPost])
  );

  // click camera button
  const handleCamera = () => {
    setIsCameraOpen(true);
    setImagePath(null);
    setVideo(null);
    setTimer(-1);
  };

  // click Add More Button
  const handleAddMore = () => {
    setAddMore(!addMore);
  };

  // set default Data when user open devise camera
  useEffect(() => {
    if (cameraImg) {
      setIsCameraOpen(true);
      setImagePath(null);
      setVideo(null);
      setTimer(-1);
    }
  }, [cameraImg]);

  const handleNext = async () => {
    if (selectedImageMemo?.length === 1) {
      const updateImage = await cropImage(
        imagePath?.uri ? imagePath?.uri : selectedImageMemo[0]?.uri,
        WIDTH - 40,
        HEIGHT / 3.5
      );
      if (updateImage) {
        const selectedImg = [
          {
            fileCopyUri: null,
            name: selectedImageMemo[0]?.name,
            size: updateImage?.size,
            type: updateImage?.mime,
            uri: updateImage?.path,
          },
        ];
        console.log(
          "🚀 ~ handleNext ~ imagePath:",
          selectedImageMemo,
          selectedImg
        );

        navigation.navigate("CreatePostForm", {
          images: selectedImg,
          cameraImg: imagePath?.uri ? true : false,
        });
      }
    } else {
      navigation.navigate("CreatePostForm", {
        images: imagePath?.uri ? [imagePath] : selectedImageMemo,
        cameraImg: imagePath?.uri ? true : false,
      });
    }
  };

  return (
    <View style={{flex: 1, marginTop: isCameraOpen && isIPhoneX() ? getStatusBarHeight() : 0}}>

   {isCameraOpen ? null: ( <SafeAreaView style={styles.mainView}>
      <Loader loading={isLoadingMemo} />
      {/* For the header */}
      {isCameraOpen ? null : (
        <CHeader
          handleBackButton={
            imagePath?.uri || videoPath?.uri
              ? () => handleCamera()
              : () => navigation.goBack()
          }
          headingTitle="createPost"
          camera
          onPressCamera={() => handleCamera()}
          CancelTxt="Cancel"
          headerTitleStyle={{ fontSize: 20 }}
        />
      )}

      {/* camera clicked Image showing*/}
      {isEmpty(selectedImageMemo) || isCameraOpen || imagePath?.uri ? null : (
        <MultipleImageView
          selectedImage={selectedImageMemo}
          addMore={addMore}
          handleAddMore={handleAddMore}
          addMoreBtn={true}
        />
      )}

      {/* capture image by camera at that time time image is show*/}
      {imagePath?.uri && (
        <View>
          <Image
            source={{ uri: `file://${imagePath?.uri}` }}
            style={{ width: "100%", height: "92%" }}
          />
        </View>
      )}

      {/* camera clicked Video showing*/}
      {videoPath?.uri ? (
        <View style={styles.container}>
          <Video
            source={{ uri: `file://${videoPath?.uri}` }}
            style={{ width: "100%", height: "100%" }}
            controls={true}
            onError={() => Toast.show("Failed to load video")}
          />
        </View>
      ) : null}

      {/* Open Image Picker */}
      {/* if the camera is open then this  CImagePicker not show */}
      {isCameraOpen ? null : (
        <View style={{marginBottom: SelectedPostData ? 140 : 60}}>
          <CImagePicker
            onImageSelect={(e) => {
              setSelectedImage(e);
            }}
            multiSelect={addMore ? true : false}
            assetType="Photos"
            goToPost={goToPost}
            setSelectedPostData={setSelectedPostData}
          />
        </View>
      )}

      {/* showing next button */}
      {(SelectedPostData && !isCameraOpen) ||
      imagePath?.uri ||
      videoPath?.uri ? (
        <Animated.View style={styles.mainButton} entering={FadeIn}>
          <CButton style={{ width: "90%" }} onBtnClick={handleNext}>
            {translate("nextText")}
          </CButton>
        </Animated.View>
      ) : null}
    </SafeAreaView>)}
    <CCamera
    setImagePath={setImagePath}
    setVideo={setVideo}
    videoDuration={10}
    isCameraOpen={isCameraOpen}
    setIsCameraOpen={setIsCameraOpen}
    setTimer={setTimer}
    timer={timer}
    recordVideo={false}
  />
    </View>
  );
};

export default memo(AddPostScreen);
