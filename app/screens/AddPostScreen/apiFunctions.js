import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import isUndefined from "lodash-es/isUndefined";
import isEmpty from "lodash-es/isEmpty";
import Toast from "react-native-simple-toast";

// submit create Post Form Data To DB
export const onsubmit = async (data, setIsLoading, token, type, uploadType) => {
  let countryContains = data?.SelectedCountry?.includes(101);

  authHeaders = {
    "Content-Type": "multipart/form-data",
    authorization: token ? `Bearer ${token}` : "",
  };
  const formData = new FormData();

  if (data?.PostDescription) {
    formData.append("description", data?.PostDescription.trim());
  }
  if (data?.group_details_ids) {
    formData.append(
      "group_details_ids",
      JSON.stringify(data?.group_details_ids)
    );
  }
  if (data?.post_id) {
    formData.append("post_id", data?.post_id);
  }
  if (!isEmpty(data?.SelectedCountry)) {
    formData.append("country", data?.SelectedCountry);
  }
  if (!isEmpty(data?.Audience)) {
    formData.append("audience", data?.Audience);
  }

  if (
    !isEmpty(data?.SelectedState) &&
    countryContains &&
    !isEmpty(data?.SelectedCountry)
  ) {
    formData.append("state", data?.SelectedState);
  }
  formData.append(
    "is_private",
    isUndefined(data?.priority)
      ? false
      : data?.priority === "Private"
        ? true
        : false
  );
  formData.append("is_shareable", data?.is_shareable || false);
  formData.append(
    "status",
    type === "Share"
      ? isUndefined(data?.priority)
        ? "public"
        : data?.priority === "Private"
          ? "private"
          : "public"
      : "draft"
  );
  if (isUndefined(data?.post_id)) {
    for (let i = 0; i < data?.files.length; i++) {
      if (uploadType === "uploadPost") {
        formData.append(`files[]`, data?.files[i]);
      } else {
        formData.append(`file`, data?.files[i]);
      }
    }
  }
  console.log("🚀 ~ onsubmit ~ formData:", formData);

  try {
    const resp = await getApiData(
      uploadType === "uploadPost"
        ? BaseSetting.endpoints.createPost
        : BaseSetting.endpoints.createReel,
      "POST",
      formData,
      authHeaders,
      false
    );

    if (resp?.data?.success || resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsLoading(false);
    }
  } catch (error) {
    setIsLoading(false);
  }
};

// Get Country Data Form DB
export const getCountryData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=countries`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Get State Data Form DB
export const getCountryStateData = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=states&country_id=${id}`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Get Audience Data Form DB
export const natureOfBusinessData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=nature_of_business`,
      "GET"
    );

    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};
