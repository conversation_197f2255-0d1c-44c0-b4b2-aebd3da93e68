import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Dimensions, SafeAreaView, View } from "react-native";
// import Animated, { FadeIn } from "react-native-reanimated";
import isEmpty from "lodash-es/isEmpty";
import CButton from "@components/CButton";
import CImagePicker from "@components/CImagePicker";
import { translate } from "../../lang/Translate";
import styles from "./styles";
import Loader from "@components/Loader";
import { useFocusEffect } from "@react-navigation/native";
import CCamera from "@components/CameraButton/CCamera";
import CHeader from "@components/CHeader";
import { isNull } from "lodash-es";
import { getStatusBarHeight, isIPhoneX } from "react-native-status-bar-height";

const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;
const RNConvertPhAsset = require("react-native-convert-ph-asset").default;

const AddReelScreen = ({ navigation, route }) => {
  // it will true when user back from create post form
  const cameraImg = route?.params?.cameraImg || false;
  // it will true when user from home
  const goToPost = route?.params?.goToPost || false;

  // For Profile
  const screenName = route?.params?.screenName;

  // get Device HEIGHT WIDTH
  HEIGHT = Dimensions.get("window").height;
  WIDTH = Dimensions.get("window").width;

  // State's
  const [selectedVideo, setSelectedVideo] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [videoPath, setVideo] = useState(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [SelectedPostData, setSelectedPostData] = useState(false);

  // Memo's
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const selectedVideoMemo = useMemo(() => selectedVideo, [selectedVideo]);

  // set Default Data when user come from home
  useFocusEffect(
    useCallback(() => {
      if (goToPost) {
        setIsCameraOpen(false);
        setVideo(null);
        setSelectedVideo([]);
      }
    }, [goToPost])
  );

  // set default Data when user open devise camera
  useEffect(() => {
    if (cameraImg) {
      setIsCameraOpen(true);
      setVideo(null);
    }
  }, [cameraImg]);

  useEffect(() => {
    if (!isEmpty(videoPath) && !isNull(videoPath)) {
      if (screenName === "profile") {
        navigation.navigate("Auth", {
          screen: "IntroPreview",
          params: { video: [videoPath], type: "addIntroPreview" },
        });
      } else {
        navigation.navigate("CreatePostForm", {
          video: [videoPath],
        });
      }
    }
  }, [videoPath]);

  // click camera button
  const handleCamera = () => {
    setIsCameraOpen(true);
    setVideo(null);
  };

  const setIsSelectFile = useCallback(async () => {
    if (
      selectedVideoMemo[0]?.uri &&
      selectedVideoMemo[0]?.uri.includes("ph://")
    ) {
      const tempArr = [...selectedVideoMemo];
      RNConvertPhAsset.convertVideoFromUrl({
        url: selectedVideoMemo[0]?.uri,
        convertTo: "mov",
        quality: "original",
      })
        .then((response) => {
          tempArr[0].uri = response?.path;
          tempArr[0].name = response?.filename;
          tempArr[0].type = "video/mov";
          if (screenName === "profile") {
            navigation.navigate("Auth", {
              screen: "IntroPreview",
              params: { video: tempArr, type: "addIntroPreview" },
            });
          } else {
            navigation.navigate("CreatePostForm", {
              video: tempArr,
            });
          }
        })
        .catch(() => {
          return e;
        });
    } else {
      if (screenName === "profile") {
        navigation.navigate("Auth", {
          screen: "IntroPreview",
          params: { video: selectedVideoMemo, type: "addIntroPreview" },
        });
      } else {
        navigation.navigate("CreatePostForm", {
          video: selectedVideoMemo,
        });
      }
    }
  }, [selectedVideoMemo]);

  return (
    <View style={{flex: 1, marginTop: isCameraOpen && isIPhoneX() ? getStatusBarHeight() : 0}}>

   {isCameraOpen ? null:( <SafeAreaView style={styles.mainView}>
      <Loader loading={isLoadingMemo} />
      {/* For the header */}
      {isCameraOpen ? null : (
        <CHeader
          handleBackButton={() => navigation.goBack()}
          headingTitle={screenName === "profile" ? "gallery" : "createReel"}
          camera
          onPressCamera={() => handleCamera()}
          CancelTxt="Cancel"
          headerTitleStyle={{ fontSize: 20 }}
        />
      )}

      {/* Camera open component */}
      <CCamera
        setVideo={setVideo}
        videoDuration={30}
        isCameraOpen={isCameraOpen}
        setIsCameraOpen={setIsCameraOpen}
        ClickPhoto={false}
      />

      {/* Open Image Picker */}
      {/* if the camera is open then this  CImagePicker not show */}
      {isCameraOpen ? null : (
        <View style={{marginBottom: SelectedPostData ? 140 : 60}}>
          <CImagePicker
            onImageSelect={(e) => {
              setSelectedVideo(e);
            }}
            assetType="Videos"
            goToPost={goToPost}
            setSelectedPostData={setSelectedPostData}
          />
        </View>
      )}

      {/* showing next button */}
      {SelectedPostData && !isCameraOpen && !isEmpty(selectedVideoMemo) ? (
        <Animated.View style={styles.mainButton} entering={FadeIn}>
          <CButton
            style={{ width: "90%" }}
            onBtnClick={() => setIsSelectFile()}
          >
            {translate("nextText")}
          </CButton>
        </Animated.View>
      ) : null}
    </SafeAreaView>)}

    <CCamera
    setVideo={setVideo}
    videoDuration={30}
    isCameraOpen={isCameraOpen}
    setIsCameraOpen={setIsCameraOpen}
    ClickPhoto={false}
    />
    </View>
  );
};

export default memo(AddReelScreen);
