import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  sharedTextView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 20,
    backgroundColor: BaseColors.white,
    paddingHorizontal: 5,
  },
  switchCardView: {
    borderRadius: 20,
    overflow: "hidden",
  },
  textView: {
    fontSize: 17,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  paginationView: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
  },
  renderPaginationDotsView: {
    width: 12,
    height: 12,
    borderRadius: 25,
    marginHorizontal: 5,
    borderWidth: 1.5,
    borderColor: BaseColors.activeTab,
  },
  headerMainView: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 18,
  },
  cancelTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  headingTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: "#322F2F",
  },

  mainButton: {
    position: "absolute",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
    bottom: 0,
    height: "10%",
  },
  addMoreMain: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  addMoreText: {
    backgroundColor: "rgba(38, 38, 38, 0.8)",
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 50,
    color: "#fff",
    marginBottom: 10,
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center",
    flexDirection: "row",
    gap: 5,
  },
  mainView: {
    backgroundColor: BaseColors.white,
    flex: 1,
  },
  mainFootLogo: {
    height: 120,
    width: 120,
    marginTop: Platform.OS === "ios" ? 0 : 0,
    alignSelf: "center",
  },
  headingText: {
    fontSize: 34,
    fontFamily: FontFamily.RobotSemiBold,
    color: "#343434",
    textAlign: "center",
  },
  contentView: {
    marginHorizontal: 25,
    marginBottom: 80,
  },
  descText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#000000",
    marginTop: 15,
    textAlign: "center",
  },
  cInputStyle: {
    marginTop: 25,
  },
  cInputStyle1: {
    marginTop: 10,
  },
  btnStyle: {
    marginTop: 45,
  },
  PsdText: {
    marginTop: 10,
  },
});

export default styles;
