/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, BackHandler, StatusBar } from 'react-native';
import Toast from 'react-native-simple-toast';
import { getApiData } from '../utils/apiHelper';
import BaseSetting from '../config/setting';
import { isEmpty } from 'lodash';
import { defaultStyle } from '../utils/commonFunction';
import { BaseColors } from '../config/theme';
import WebviewLoader from '../components/WebViewLoader';
import NavigationDrawerHeader from '../components/NavigationDrawerHeader';
const { WebView } = require("react-native-webview");

/**
 * About Screen
 * @module About
 *
 */
export default function TermsAndCondition({ navigation, route }) {
  // const [loader, setLoader] = useState(true);
  const [data, setData] = useState('');

  const dataMemo = useMemo(() => data, [data]);
  const handleBackButtonClick = useCallback(() => {
    navigation.goBack();
    return true;
  }, [navigation]);

  const defaultHead = `<meta name="viewport" content="width=device-width, initial-scale=1">
${defaultStyle}`;

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    fetchdata();
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /**
   * fetch about data
   * @function fetchdata
   */

  const fetchdata = useCallback(async () => {
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.policy}?slug=terms-and-conditions`,
        'GET',
      );
      if (resp?.data?.success && resp?.data?.data) {
        setData(resp?.data?.data || 'test');
      } else {
        setData('');
        Toast.show(resp?.message || 'In else');
      }
      // setLoader(false);
    } catch (error) {
      setData('');
      Toast.show('somethingWentWrong');
      console.log('ERRR', error);
      // setLoader(false);
    }
  }, [BaseSetting]);

  return (
    <>
      <NavigationDrawerHeader
        navigationProps={navigation}
        from="screen"
        type="text"
        title="Terms & Conditions"
        isBackFunc
        onBackPress={() => handleBackButtonClick()}
        headersStyle={{
          paddingHorizontal: 20,
        }}
      />

      <View
        style={{
          flex: 1,
          backgroundColor: BaseColors.white,
          paddingHorizontal: 14,
        }}>
        {!isEmpty(dataMemo) ? (
          <WebView
            originWhitelist={['*']}
            source={{
              html: !isEmpty(dataMemo?.html_body)
                ? `${defaultHead}${dataMemo.html_body}`
                : '',
            }}
            startInLoadingState
            setBuiltInZoomControls={false}
            renderLoading={() => <WebviewLoader />}
            scrollEnabled
            scalesPageToFit={false}
            onLoad={() => {}}
          />
        ) : null}
      </View>
    </>
  );
}
