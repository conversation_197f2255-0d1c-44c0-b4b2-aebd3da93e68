import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const BoostPost = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.boostPost,
      "POST",
      data
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ BoostPost ~ error:", error);
  }
};
// Open Razorpay modal for payment
export const paymentForBoost = async (
  price,
  userData,
  currency,
  boost_id,
  type,
) => {
  try {

    const data = {
      "customer_details": {
          "customer_phone": `${userData?.company_phone_number || userData?.phone_number}`,
          "customer_email": userData?.company_email !== null && userData?.company_email !== 'null' ? userData?.company_email :userData?.email,
          "customer_name": userData?.company_name
      },
      "link_notify": {
          "send_sms": true,
          "send_email": true
      },
      "link_amount": Number(price),
      link_currency: currency || "INR",
      "link_purpose": type === 'reel' ? "Boost Reel" : "Boost Post",
      "boost_post_id": boost_id,
  }

  const resp = await getApiData(
    BaseSetting.endpoints.createBoostPaymentLink,
    "POST",
    data
  );
    return resp;
  } catch (error) {
    console.error("🚀 ~ purchasePlan ~ error:", error);
  }
};

export const verifyPayment = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.verifyBoostPayment,
      "POST",
      data
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ verifyPayment ~ error:", error);
  }
};
