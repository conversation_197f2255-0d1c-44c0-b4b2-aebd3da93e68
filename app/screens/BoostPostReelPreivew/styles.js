import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  countryView: {
    marginHorizontal: 20,
    marginTop: 20,
  },
  countryTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.lightBlack10,
  },
  dataTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.placeHolderColor,
  },
  separatorStyle: {
    borderBottomWidth: 5,
    marginTop: 20,
    borderColor: BaseColors.textinputBackGroundColor,
  },
  countryTextStyle1: {
    fontSize: 16,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
  },
  summaryViewStyle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  secondSeparator: {
    borderBottomWidth: 0.76,
    marginVertical: 12,
    borderColor: BaseColors.gray,
    marginHorizontal: 20,
  },
  inputLabelStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
  },
  budgetContainerStyle: {
    marginHorizontal: 20,
  },
  sliderContainerStyle: {
    marginVertical: 16,
  },
  razorPayImgStyle: {
    height: 32,
    width: 156,
  },
  bottomContainer: {
    marginHorizontal: 20,
    marginTop: 10,
    marginBottom: 20,
  },
  bottomContent: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.grayFontColor,
    textAlign: "justify",
  },
  buttonView: {
    marginHorizontal: 20,
    marginVertical: 10,
  },
});

export default styles;
