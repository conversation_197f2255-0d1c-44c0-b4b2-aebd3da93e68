import { Text, View } from "react-native";
import styles from "./styles";

//   Data Component View
export const DataComponentView = ({ headingText, ValueText }) => {
  return (
    <View style={styles.countryView}>
      <Text style={styles.countryTextStyle}>{headingText}</Text>
      <Text style={styles.dataTextStyle}>{ValueText}</Text>
    </View>
  );
};

//   For Separator
export const Separator = () => {
  return <View style={styles.separatorStyle} />;
};
