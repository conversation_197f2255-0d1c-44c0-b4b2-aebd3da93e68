//   Function For Display data
export const findDataWithName = (mainArr, idArray) => {
  const filteredNames = mainArr
    .filter((obj) => idArray?.includes(obj.id))
    .map((obj) => obj.name);
  const commaSeparatedString = filteredNames.join(", ");
  return commaSeparatedString;
};

export const findDataWithTitle = (mainArr, idArray) => {
  const filteredNames = mainArr
    .filter((obj) => idArray?.includes(obj.id))
    .map((obj) => obj.title);
  const commaSeparatedString = filteredNames.join(", ");
  return commaSeparatedString;
};
