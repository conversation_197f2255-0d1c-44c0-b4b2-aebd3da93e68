import { View, Text, SafeAreaView, Dimensions, ScrollView } from "react-native";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import styles from "./styles";
import CHeader from "@components/CHeader";
import MultipleImageView from "@components/MultipleImageView/MultipleImageView";
import Video from "react-native-video";
import { BaseColors } from "@config/theme";
import { DataComponentView, Separator } from "./component";
import { findDataWithName, findDataWithTitle } from "./function";
import { callOfActionArr } from "@config/staticData";
import FastImage from "react-native-fast-image";
import CButton from "@components/CButton";
import { useDispatch, useSelector } from "react-redux";
import { BoostPost, paymentForBoost, verifyPayment } from "./apiFunciton";
import Toast from "react-native-simple-toast";
import VerifyModal from "@components/VerifyModal";
import Loader from "@components/Loader";
import { Modal } from "react-native";
import WebViewScreen from "@screens/WebViewScreen";
import { useFocusEffect } from "@react-navigation/native";
import socketAction from '@redux/reducers/socket/actions'
import { isEmpty } from "lodash-es";

const images = require("@config/images").images.cashfreeLogo;

const {setIsBoostPaymentStatus} = socketAction

const BoostPostReelPreview = ({ navigation, route }) => {

  const dispatch = useDispatch()
  //   Device Height and Width
  const HEIGHT = Dimensions.get("window").height;

  //   Route Data
  const File = route?.params?.postData?.data?.files;
  const type = route?.params?.postData?.type;
  const BoostData = route?.params?.boostData;
  const countryData = route?.params?.postData?.countryData;
  const post_id = route?.params?.postData?.apiRes?.post_id;
  const reel_id = route?.params?.postData?.apiRes?.reel_id;
  const stateData = route?.params?.postData?.stateData;
  const audienceData = route?.params?.natureOfBusinessDada;

  // State's
  const [isLoading, setIsLoading] = useState(false);
  const [isCenterLoader, setIsCenterLoader] = useState(false);
  const [isModal, setIsModal] = useState(false);

  // Memo
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const isCenterLoaderMemo = useMemo(() => isCenterLoader, [isCenterLoader]);
  const isModalMemo = useMemo(() => isModal, [isModal]);
  // UserData
  const { userData } = useSelector((a) => a.auth);
  const { isBoostPaymentStatus } = useSelector((a) => a.socket);

  // This Use Focus effect call when emit we get "payment_status" and this emit we get once payment status success or failed
  useFocusEffect(
    useCallback(() =>{
      if(!isEmpty(isBoostPaymentStatus) && isBoostPaymentStatus?.status === 'SUCCESS'){
        setIsModal(true);
        setIsCenterLoader(false);
        // dispatch(setIsBoostPaymentStatus({}));
      } else if(!isEmpty(isBoostPaymentStatus) && isBoostPaymentStatus?.status === 'FAILED'){
        dispatch(setIsBoostPaymentStatus({}));
        setIsCenterLoader(false);
        Toast.show(
          "Payment failed!"
        );
      }
  },[isBoostPaymentStatus, route]))

  // Boost Post Api Function
  const boostPostApiFunction = async () => {
    setIsLoading(true);
    const data = {
      post_id: type === "post" ? post_id : reel_id,
      country: BoostData?.data?.SelectedCountry.join(", "),
      audience: BoostData?.data?.Audience.join(", "),
      price: Math.floor(
        Number(route?.params?.amount) * Number(route?.params?.days)
      ),
      days: Number(route?.params?.days),
      action: BoostData?.data?.callOfAction,
      type: type,
    };

    if (BoostData?.data?.SelectedCountry?.indexOf(101) !== -1) {
      data.state = BoostData?.data?.SelectedState?.join(", ");
    }

    // Create boost post or reels using this call and get boost id
    const resp = await BoostPost(data);

    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      const boost_amount = Number(resp?.data?.data?.amount_due) ||
      Math.floor(
        Number(route?.params?.amount) * Number(route?.params?.days)
      );

      // Get Payment link from backend side
      const paymentLink = await paymentForBoost(
        boost_amount,
        userData,
        resp?.data?.data?.currency || "INR",
        resp?.data?.data?.boost_post_id,
        type,
      );

      //Once payment link get in response we are opening that url in webview
      if(paymentLink?.data?.link_url){
        navigation.navigate('WebViewScreen', {
          uri: paymentLink?.data?.link_url
      })
      }
      setIsLoading(false);
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsLoading(false);
    }
    setIsLoading(false);
  };

  return (
    <SafeAreaView style={styles.mainView}>
      {/* Screen Loader */}
      <Loader loading={isCenterLoaderMemo} />
      {/* For Header */}
      <CHeader
        headingTitle={type === "post" ? "reviewPost" : "reviewReel"}
        handleBackButton={() => navigation.goBack()}
      />
      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        {/* Image View */}
        {type === "post" ? (
          <MultipleImageView
            selectedImage={File}
            addMoreBtn={false}
            showDots={File.length > 1 ? true : false}
          />
        ) : type === "reel" ? (
          <Video
            source={{ uri: `${File[0]?.uri || File?.fileUrl}` }}
            style={{
              height: HEIGHT / 2.5,
              backgroundColor: BaseColors.lightGray,
            }}
            controls={false}
            repeat
            resizeMode={"contain"}
          />
        ) : null}

        {/* For Country View */}
        <DataComponentView
          headingText={"Country"}
          ValueText={findDataWithName(
            countryData,
            BoostData?.data?.SelectedCountry,
            "name",
            "id"
          )}
        />

        {/* For State */}
        {BoostData?.data?.SelectedCountry?.indexOf(101) !== -1 ? (
          <DataComponentView
            headingText={"State"}
            ValueText={findDataWithName(
              stateData,
              BoostData?.data?.SelectedState
            )}
          />
        ) : null}
        {/* Separator */}
        <Separator />

        {/* For Target Audience */}
        <DataComponentView
          headingText={"Target Audience"}
          ValueText={findDataWithTitle(audienceData, BoostData?.data?.Audience)}
        />

        {/* For Call Of Action */}
        <DataComponentView
          headingText={"Call To Action"}
          ValueText={
            callOfActionArr.find(
              (e) => e.value === BoostData?.data?.callOfAction
            )?.label
          }
        />

        {/* Separator */}
        <Separator />

        {/* Summary View */}
        <View style={styles.countryView}>
          <Text style={styles.countryTextStyle1}>Cost Summary</Text>
        </View>
        {/* For Call Of Action */}
        <DataComponentView
          headingText={"Duration Day"}
          ValueText={`${route?.params?.days} Days`}
        />
        <View style={styles.summaryViewStyle}>
          <DataComponentView
            headingText={"Per day budget"}
            ValueText={`₹${route?.params?.amount} x ${route?.params?.days}`}
          />
          <DataComponentView
            headingText={"Total"}
            ValueText={`₹${Math.floor(
              Number(route?.params?.amount) * Number(route?.params?.days)
            )}`}
          />
        </View>
        <View style={styles.secondSeparator} />

        {/* For Payment Metho */}
        <View style={styles.budgetContainerStyle}>
          <Text style={styles.inputLabelStyle}>Payment Method</Text>
          <View style={styles.sliderContainerStyle}>
            <FastImage
              source={images}
              resizeMode="contain"
              style={styles.razorPayImgStyle}
            />
          </View>
        </View>
        {/* Separator */}
        <Separator />

        {/* Bottom View */}
        <View style={styles.bottomContainer}>
          <Text style={styles.bottomContent}>
            By creating an ad, you agree to footbizz Terms and Advertising
            guidelines. All ads are listed in their public as Library. Learn
            more
          </Text>
        </View>
      </ScrollView>
      <View style={styles.buttonView}>
        <CButton
          onBtnClick={() => boostPostApiFunction()}
          loading={isLoadingMemo}
        >
          Boost {type === "post" ? "Post" : "Reel"}
        </CButton>
      </View>
      {isModal ? (
        <VerifyModal
          visible={isModal}
          setIsModal={setIsModal}
          type={"boost"}
          text={
            type === "post"
              ? "Your Post is successfully Boost"
              : "Your Reel is successfully Boost"
          }
          navigation={navigation}
        />
      ) : null}
    </SafeAreaView>
  );
};

export default BoostPostReelPreview;
