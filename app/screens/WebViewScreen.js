// WebViewScreen.js
import WebviewLoader from "@components/WebViewLoader";
import { BaseColors } from "@config/theme";
import React, { useEffect } from "react";
import { SafeAreaView, StatusBar } from "react-native";
import { useSelector } from "react-redux";
const { WebView } = require("react-native-webview");

const WebViewScreen = ({ navigation, route }) => {

  const {isBoostPaymentStatus} = useSelector((s) => s.socket);

  useEffect(() => {
    if(isBoostPaymentStatus?.status && route?.name === 'WebViewScreen'){
      setTimeout(() => {
        navigation.goBack();
      }, 4000);
    }
  },[isBoostPaymentStatus])

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: BaseColors.White}}>
      <WebView
        originWhitelist={['*']}
        source={{ uri: route?.params?.uri }}
        startInLoadingState
        renderLoading={() => <WebviewLoader />}
        onLoad={() => {}}
      />
    </SafeAreaView>
  );
};

export default WebViewScreen;
