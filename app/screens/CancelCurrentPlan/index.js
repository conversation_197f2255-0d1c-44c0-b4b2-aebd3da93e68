import { View, Text, FlatList, TouchableOpacity, Platform } from "react-native";
import React, { useCallback, useMemo, useState } from "react";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { translate } from "../../lang/Translate";
import CButton from "@components/CButton";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import { cancelPlan } from "./apiFunction";
import Loader from "@components/Loader";
import { useFocusEffect } from "@react-navigation/native";
import { isEmpty } from "lodash-es";
import * as RNIap from "react-native-iap";

const { setIsCurrentPlan, setActivePlanData } = authAction;
const CancelCurrentPlan = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const { isCurrentPlan, activePlanData, adminSettingData } = useSelector((auth) => auth.auth);


  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const is_production = adminSettingData?.find(
    (obj) => obj?.slug === "ISPRODUCTION"
  )?.value;

  useFocusEffect(
    useCallback(() => {
      if (isEmpty(isCurrentPlan) && isEmpty(activePlanData)) {
        navigation.goBack();
      }
    }, [isCurrentPlan, activePlanData])
  );

  const cancelApiCall = useCallback(
    async (id, type) => {
      setIsLoading(true);
      const data = {
        DEVICE: Platform.OS,
        package_name: "com.footbizz",
      };
      if(type === 'special_offer' || type === 'coupon'){
        data.purchase_type = type
      }
      const resp = await cancelPlan(id, data);

      if (resp !== undefined && resp?.data?.success) {
        dispatch(setIsCurrentPlan({}));
        dispatch(setActivePlanData({}));

        navigation.goBack();
        setIsLoading(false);
      } else {
        console.log(resp?.data);
        setIsLoading(false);
      }
    },
    [isLoadingMemo, activePlanData]
  );

  const renderItem = useCallback(({ item, index }) => {
    return (
      <View style={styles.renderItemView}>
        <CustomIcon
          name="correct-icon"
          size={16}
          color={BaseColors.lightBlack10}
        />
        <View style={{ flex: 1 }}>
          <Text style={styles.descTextStyle}>{item?.title}</Text>
        </View>
      </View>
    );
  }, []);
  return (
    <View style={styles.mainView}>
      {/* Header */}
      <CHeader
        headingTitle={"cancelSubscription"}
        handleBackButton={() => navigation.goBack()}
      />
      {isLoadingMemo ? <Loader /> : null}
      {/* Main Data View */}
      <View style={styles.secondMainViewStyle}>
        <View style={styles.renderDataMainView}>
          <FlatList
            data={route?.params?.currentPlan[0]?.features_list_preview || activePlanData?.features_list_preview || []}
            renderItem={renderItem}
            keyExtractor={(item, index) => index.toString()}
            showsVerticalScrollIndicator={false}
          />
        </View>
        {/* Note Text */}
        <View style={styles.noteViewStyle}>
          <Text style={styles.noteTextStyle}>
            {translate("noteText")}: {translate("subScriptionCancelText")}
          </Text>
        </View>
      </View>
      <View style={styles.btnContainerView}>
        <CButton onBtnClick={() => navigation.goBack()}>
          I will keep my subscription
        </CButton>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.cancelBtnContainerStyle}
          onPress={() => {
            if (Platform.OS === "ios") {
              // For iOS only open store subscription screen for Production app.
              if (is_production === 'true' && activePlanData?.plan_type !== 'special_offer' && activePlanData?.plan_type !== 'coupon') {
                RNIap.deepLinkToSubscriptions({
                  sku: isCurrentPlan[0]?.product_id || activePlanData?.product_id,
                });
              } else {
                cancelApiCall(route?.params?.currentPlan[0]?.plan_id || activePlanData?.plan_id ,activePlanData?.plan_type );
              }
            } else {
              cancelApiCall(route?.params?.currentPlan[0]?.plan_id || activePlanData?.plan_id, activePlanData?.plan_type);
            }
          }}
        >
          <Text style={[styles.noteTextStyle, { fontSize: 14 }]}>
            I want to cancel
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CancelCurrentPlan;
