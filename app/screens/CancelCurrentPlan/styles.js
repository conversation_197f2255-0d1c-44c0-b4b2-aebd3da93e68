import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet } = require("react-native");

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.White,
  },
  secondMainViewStyle: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 25,
  },
  descTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.lightBlack10,
  },
  renderDataMainView: {
    backgroundColor: BaseColors.grayBackgroundColor,
    borderWidth: 1,
    borderColor: BaseColors.borderColor,
    borderRadius: 8,
    padding: 23,
    paddingVertical: 35,
  },
  renderItemView: {
    marginVertical: 10,
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  noteTextStyle: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.gray5,
  },
  noteViewStyle: {
    marginVertical: 12,
  },
  btnContainerView: {
    marginHorizontal: 20,
    marginVertical: 10,
  },
  cancelBtnContainerStyle: {
    marginVertical: 10,
    alignItems: "center",
  },
});
export default styles;
