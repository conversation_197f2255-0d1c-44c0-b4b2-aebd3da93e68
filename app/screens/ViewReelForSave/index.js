import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { RefreshControl, View, SafeAreaView } from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { BaseColors } from "@config/theme";
import ShareListModal from "@components/ShareListModal";
import MoreInfoModal from "@components/MoreInfoModal";
import { RenderItem } from "./component";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import { followFindObject, likeFindObject } from "./function";
import {
  handleAddComment,
  handleToGeCommentList,
  handleToLikeDisLike,
  handleToReportUser,
  handleToSaveUnSaved,
  viewReel,
} from "./apiFunctions";
import {
  getUserFollowList,
  handleFollowToggle,
  onSharePost,
} from "@app/utils/commonFunction";
import CommentList from "@components/CommentList";
import SwiperFlatList from "react-native-swiper-flatlist";
import Toast from "react-native-simple-toast";
import ReportModal from "@components/ReportModal";
import { translate } from "../../lang/Translate";
import { isEmpty } from "lodash-es";
import {
  ClickedCommentLikeButton,
  hideComment,
} from "@components/PostComponent/apiCallFunction";
import { getSavedReelData } from "@screens/SavedScreen/apiCallFunction";
import { handleToGetLikeList } from "@screens/ViewReels/apiFunctions";
import ContactInfoModal from "@components/ContactModal";
import PurChasePlanModal from "@components/PurchasePlanModal";
import {
  getCountryData,
  getCountryStateData,
  natureOfBusinessData,
} from "@screens/SingUpWithCompanyDetail/apiFunctions";

const {
  moreInfo,
  moreInfoUnSaved,
  selfUserForDataForSave,
  selfUserForDataForUnSave,
} = require("@config/staticData");

const {
  setUserFollowList,
  setCommentData,
  setSavedReelList,
  setUserLikeList,
  setModalData,
  setCreatedPostList,
} = authAction;

// ViewReels component responsible for rendering a list of reels
const ViewReelsForSave = ({ navigation, route }) => {
  const type = route?.params?.type || "anotherProfile";
  // Selected Reel Index from Saved Screen
  const selectedReelIndex = route?.params?.selectedReelIndex;

  // props for open comment modal
  const notificationType = route?.params?.notificationType || "";
  const commentPostId = route?.params?.commentPostId || "";

  // Redux Variable
  const dispatch = useDispatch();
  const {
    userFollowList,
    commentData,
    savedReelList,
    isCurrentPlan,
    myReel,
    userLikeList,
    userData,
    userSelfReel,
    createdPostList,
    activePlanData
  } = useSelector((s) => s.auth);
  const videoRef = useRef(null);
  // Ref's
  const textInputRef = useRef(null);
  let data = savedReelList?.data;
  // State
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [shareListModal, setIsShareListModal] = useState(false);
  const [moreInfoModal, setModalInfoModal] = useState(false);
  const [isLikeAnim, setIsLikeAnim] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isContactModal, setIsContactModal] = useState(false);
  const [userDataMain, setUerDataMain] = useState({});
  const [isLikeListLoader, setIsLikeListLoader] = useState(false);
  const [reelsId, setIsReelId] = useState("");
  const [isCommentList, setIsCommentList] = useState(false);
  const [commentLoading, setCommentLoading] = useState(false);
  const [commentModalLoading, setCommentModalLoading] = useState(false);
  const [reelData, setReelData] = useState(false);
  const [commentId, setCommentId] = useState("");
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [isReportModal, setIsReportModal] = useState(false);
  const [reportTextInput, setReportTextinput] = useState("");
  const [reportReason, setReportReason] = useState({});
  const [isComment, setIsComment] = useState("");
  const [reasonErrorMessage, setIsReasonErrMsg] = useState("");
  const [selectedUname, setSelectedUname] = useState("");
  const [isCurrentIndexUser, setIsCurrentIndexUser] = useState("");
  const [likeListModal, setLikeListModal] = useState(false);
  const [isReelsArrList, setIsReelsArrList] = useState([]);
  const [shareReelId, setShareReelId] = useState("");
  const [countryList, setCountryData] = useState([]);
  const [stateList, setSelectedCountryState] = useState([]);
  const [natureOfBusinessList, setNatureOfBusinessData] = useState([]);
  const [searchValue, setOnChangeSearchValue] = useState("");

  // Memo's
  const currentVideoIndexMemo = useMemo(
    () => currentVideoIndex,
    [currentVideoIndex]
  );
  const likeListModalMemo = useMemo(() => likeListModal, [likeListModal]);
  const reelIdMemo = useMemo(() => reelsId, [reelsId]);
  const commentIdMemo = useMemo(() => commentId, [commentId]);
  const isCommentMemo = useMemo(() => isComment, [isComment]);
  const shareListModalMemo = useMemo(() => shareListModal, [shareListModal]);
  const moreInfoModalMemo = useMemo(() => moreInfoModal, [moreInfoModal]);
  const isLikeAnimMemo = useMemo(() => isLikeAnim, [isLikeAnim]);
  const refreshingMemo = useMemo(() => refreshing, [refreshing]);
  const isCommentListMemo = useMemo(() => isCommentList, [isCommentList]);
  const isReelsArrListMemo = useMemo(() => isReelsArrList, [isReelsArrList]);

  const reportReasonMemo = useMemo(() => reportReason, [reportReason]);

  const commentLoadingMemo = useMemo(() => commentLoading, [commentLoading]);
  const reasonErrorMessageMemo = useMemo(
    () => reasonErrorMessage,
    [reasonErrorMessage]
  );
  const commentModalLoadingMemo = useMemo(
    () => commentModalLoading,
    [commentModalLoading]
  );
  const reportTextInputMemo = useMemo(() => reportTextInput, [reportTextInput]);

  const isReportModalMemo = useMemo(() => isReportModal, [isReportModal]);
  const reelDataMemo = useMemo(() => reelData, [reelData]);

  useFocusEffect(
    useCallback(() => {
      setIsReelsArrList(savedReelList);
      fetchCountryData();
      fetchCountryStateData(101);
      fetchNatureOfBusinessData();
      return () => {
        setIsReelsArrList(savedReelList);
      };
    }, [savedReelList])
  );

  const fetchCountryData = async (e) => {
    const resp = await getCountryData(e);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setCountryData(updatedData);
  };
  const fetchCountryStateData = async (id) => {
    const resp = await getCountryStateData(id);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });

    setSelectedCountryState(updatedData);
  };

  // fetch Nature Of Business Data and manipulate response
  const fetchNatureOfBusinessData = async () => {
    const resp = await natureOfBusinessData();

    let updatedData = resp?.data?.data?.map((val) => {
      return {
        ...val,
        value: val.id,
      };
    });

    setNatureOfBusinessData(updatedData);
  };
  // Set Current Video Index Method
  const onChangeIndex = useCallback(
    async ({ index }) => {
      setCurrentVideoIndex(index);
      handleViewReel(index);
      const userId = savedReelList?.data[index]?.user_data[0]?.user_id;
      setIsCurrentIndexUser(userId);

      // if (
      //   index === savedReelList?.data?.length - 3 &&
      //   savedReelList?.next_enable
      // ) {
      //   fetchSavedReelData(savedReelList?.page + 1);
      // }
    },
    [currentVideoIndexMemo]
  );

  const handleViewReel = useCallback(
    (index) => {
      if (index === currentVideoIndexMemo) {
        setTimeout(async () => {
          const reelId = savedReelList?.data[index]?.reel_id;

          const resp = await viewReel(reelId);
        }, 1000);
      }
    },
    [currentVideoIndexMemo]
  );
  // Follow Button Animation
  const handleToFollowAnim = useCallback(
    (userId) => {
      if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
        const data = followFindObject(userId, savedReelList?.data);
        if (data !== -1) {
          for (let i = 0; i < data.length; i++) {
            const object = data[i];

            savedReelList.data[object].is_followed =
              !savedReelList.data[object].is_followed;
          }
        }
        dispatch(setSavedReelList(savedReelList));
      } else {
        Toast.show("Failed to follow,please purchase plan");
      }
    },
    [savedReelList, isCurrentPlan]
  );

  // Handle To Follow
  const handleToFollow = async (userId, type) => {
    if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
      handleToFollowAnim(userId);
      if (type === "add") {
        userData.followings = userData.followings + 1;
      } else {
        userData.followings = userData.followings - 1;
      }
      const resp = await handleFollowToggle(userId);
      if (resp?.data?.success) {
        getUserList();
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        handleToFollowAnim(userId);
        if (type === "add") {
          userData.followings = userData.followings - 1;
        } else {
          userData.followings = userData.followings + 1;
        }
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  // Handle To Like Animation
  const handleToLikeAnim = (reelId, index) => {
    const updatedReelsList = { ...savedReelList };
    updatedReelsList.data[index].is_liked =
      !updatedReelsList.data[index].is_liked;
    setIsLikeAnim(true);
    if (updatedReelsList.data[index].is_liked) {
      updatedReelsList.data[index].like_counts =
        Number(updatedReelsList.data[index].like_counts) + 1;
    } else {
      updatedReelsList.data[index].like_counts =
        Number(updatedReelsList.data[index].like_counts) - 1;
    }

    dispatch(setSavedReelList(updatedReelsList));
  };

  // Handle To Like
  const handleToLike = async (reelId, index) => {
    if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
      handleToLikeAnim(reelId, index);
      const resp = await handleToLikeDisLike(reelId);
      if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      } else {
        handleToLikeAnim(reelId, index);
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  const commentLikeDislikeAnimation = async (c_id, type, mainIndex, index) => {
    if (type !== "nested") {
      commentData.data[mainIndex].is_liked =
        !commentData.data[mainIndex].is_liked;
      if (commentData.data[mainIndex].is_liked) {
        commentData.data[mainIndex].likes_counts =
          commentData?.data[mainIndex].likes_counts + 1;
      } else {
        commentData.data[mainIndex].likes_counts =
          commentData?.data[mainIndex].likes_counts - 1;
      }
    } else {
      commentData.data[mainIndex].replies[index].is_liked =
        !commentData.data[mainIndex].replies[index].is_liked;
      if (commentData.data[mainIndex].replies[index].is_liked) {
        commentData.data[mainIndex].replies[index].likes_counts =
          commentData?.data[mainIndex].replies[index].likes_counts + 1;
      } else {
        commentData.data[mainIndex].replies[index].likes_counts =
          commentData?.data[mainIndex].replies[index].likes_counts - 1;
      }
    }

    dispatch(setCommentData(commentData));
  };

  const handleCommentLike = useCallback(
    async (c_id, type, mainIndex, index) => {
      if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
        await commentLikeDislikeAnimation(c_id, type, mainIndex, index);
        const resp = await ClickedCommentLikeButton(c_id);
        if (resp !== undefined) {
          if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
          } else {
            await commentLikeDislikeAnimation(c_id);
            Toast.show(resp?.data?.message || "Soothing went wrong");
          }
        }
      } else {
        setIsPaymentModal(true);
      }
    },
    [commentData, isCurrentPlan]
  );

  // Handle On Double Tap
  const onDoubleTap = () => {
    console.log("On double tap");
  };

  // For Pull To Refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true); // Set refreshing state to true
    // Fetch updated data here

    fetchSavedReelData(1).then(() => {
      setRefreshing(false);
    });
  }, [refreshingMemo]);

  useEffect(() => {
    if (notificationType === "comment" && !isEmpty(commentPostId)) {
      handleToCommentList(commentPostId, 1);
    }
  }, []);

  const followUnFollowAnimation = (index) => {
    userLikeList.viewData[index].is_followed =
      !userLikeList.viewData[index].is_followed;
  };
  const handleToBtnPressForFollow = async (data) => {
    followUnFollowAnimation(data?.index);
    const resp = handleFollowToggle(data?.item);
    if (resp !== undefined && resp?.data?.success) {
      console.log("Success");
    } else {
      followUnFollowAnimation(data?.index);
    }
  };

  const handleToCommentList = useCallback(async (id, page) => {
    setIsReelId(id);
    const resp = await handleToGeCommentList(id, page);

    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(
        setCommentData({
          page: page,
          next_enable: resp?.data?.hasNextPage,
          data:
            page > 1
              ? [...createdPostList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      setIsComment("");
      setIsCommentList(true);
    } else {
      console.log(resp?.data?.message || "Some thing went wrong");
    }
  }, []);

  // Handle To More Info; Modal
  const handleToMoreInfo = useCallback(
    async (item) => {
      await setReelData(item?.data);
      setModalInfoModal(item?.modalVisible);
    },
    [reelDataMemo, moreInfoModalMemo]
  );

  // For Handle More Info Button's Event's
  const handleToMoreInfoBtnEvent = useCallback(
    async (item) => {
      if (item?.id === 1) {
        if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
          setModalInfoModal(false);
          navigation.navigate("StoryPreview", {
            image: {
              ...reelData?.ReelData,
              thubnails: reelData?.ReelData?.thumbnailData?.thumbUrl,
              reelId: reelData?.reel_id,
            },
            video_duration: 30,
            sendedFile: {
              ...reelData?.ReelData,
              uri: reelData?.ReelData?.fileUrl,
            },
            type: "reels",
          });
        } else {
          setModalInfoModal(false);

          if (Platform.OS === "ios") {
            setTimeout(() => {
              setIsPaymentModal(true);
            }, 200);
          } else {
            setIsPaymentModal(true);
          }
        }
      } else if (item?.id === 2) {
        if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
          handleToSaveReel(reelData?.reel_id); // For Save Reel
        } else {
          setModalInfoModal(false);
          if (Platform.OS === "ios") {
            setTimeout(() => {
              setIsPaymentModal(true);
            }, 200);
          } else {
            setIsPaymentModal(true);
          }
        }
      } else if (item?.id === 3) {
        setModalInfoModal(false);
        if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
          navigation.navigate("MessagesInfo", {
            userInfo: {
              is_blocked: false,
              userData: {
                user_dp: reelData?.user_data[0]?.user_dp,
                user_id: reelData?.user_data[0]?.user_id,
                full_name: reelData?.user_data[0]?.full_name,
              },
            },
          });
        } else {
          if (Platform.OS === "ios") {
            setTimeout(() => {
              setIsPaymentModal(true);
            }, 200);
          } else {
            setIsPaymentModal(true);
          }
        }
      } else if (item?.id === 4) {
        if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
          await handleToFollow(reelDataMemo?.user_data[0]?.user_id, "remove"); // For Remove Following
          setModalInfoModal(false);
        } else {
          setModalInfoModal(false);
          if (Platform.OS === "ios") {
            setTimeout(() => {
              setIsPaymentModal(true);
            }, 200);
          } else {
            setIsPaymentModal(true);
          }
        }
      } else if (item?.id === 5) {
        if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
          setModalInfoModal(false);
          setReportReason({});
          setReportTextinput("");
          setIsReasonErrMsg("");
          setIsReportModal(true); // For Report Account
        } else {
          setModalInfoModal(false);
        }
      } else if (item?.id === 6) {
        setModalInfoModal(false);
        dispatch(
          setModalData({
            type: "deleteReelsOnly",
            title: "deleteReelText",
            buttonTxt: "delete",
            cancelText: "No",
            icon: "Delete",
            visible: true,
            extraData: reelData?.reel_id,
          })
        );
      } else {
        Toast.show("Select any one Option"); // When user not select any option then error message
      }
    },
    [
      reelDataMemo,
      moreInfoModalMemo,
      reportReasonMemo,
      reportTextInputMemo,
      isCurrentPlan,
      savedReelList,
    ]
  );

  const UpdateHomePostListRedux = (post_id) => {
    const updatedPosts = createdPostList?.data?.map((post) => {
      if (post.post_id === post_id || post.reel_id === post_id) {
        return {
          ...post,
          is_saved: post.is_saved ? false : true,
        };
      }
      return post;
    });

    dispatch(
      setCreatedPostList({
        ...createdPostList,
        data: updatedPosts,
      })
    );
  };

  const handleSaveUnSaveAnimation = async (reelId) => {
    const data = await savedReelList?.data?.findIndex(
      (item) => item?.reel_id === reelId
    );
    if (data !== -1) {
      savedReelList.data[data].is_saved = !savedReelList.data[data].is_saved;
    }
    dispatch(setSavedReelList(savedReelList));
    UpdateHomePostListRedux(reelId);
  };
  // For Save Reels
  const handleToSaveReel = useCallback(
    async (reelId) => {
      await handleSaveUnSaveAnimation(reelId);
      const resp = await handleToSaveUnSaved(reelId);
      if (resp !== undefined && resp?.data?.success) {
        Toast.show(resp?.data?.message || "Reel Saved");
        setModalInfoModal(false);
      } else {
        await handleSaveUnSaveAnimation(reelId);
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        setModalInfoModal(false);
      }
    },
    [savedReelList]
  );

  // Get Following list
  const getUserList = useCallback(async () => {
    const resp = await getUserFollowList(userData?.user_id);
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(setUserFollowList(resp?.data));
    } else {
      Toast.show("Something went wrong please try again");
    }
  }, [userData]);

  // handle To Report
  const handleToReport = async () => {
    const data = {
      user_id: reelDataMemo?.user_data[0]?.user_id,
      reason: translate(reportReasonMemo?.options),
      type: "reel",
      reporting_id: reelDataMemo?.reel_id,
    };

    if (reportReasonMemo?.options === "somethingElseText") {
      data.reason = reportTextInputMemo;
    }
    if (
      reportReasonMemo?.options === "somethingElseText" &&
      reportTextInputMemo === ""
    ) {
      setIsReasonErrMsg("Please enter,\nWhy you are report this user!");
    } else {
      const resp = await handleToReportUser(data);
      if (resp !== undefined && resp?.data?.success) {
        Toast.show(resp?.data?.message);
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        setReportReason({});
        setReportTextinput("");

        setIsReportModal(false);
      }
    }
  };

  const handleToSubmitComment = useCallback(
    async (e) => {
      if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
        let updateComment = !isEmpty(selectedUname)
          ? isCommentMemo?.replace(selectedUname, "")
          : isCommentMemo;

        setCommentLoading(true);
        if (!isEmpty(updateComment.trim())) {
          const data = { reel_id: reelIdMemo, comment: updateComment.trim() };
          if (!isEmpty(commentId)) {
            data.reply_id = commentId;
          }
          const comment_count = likeFindObject(reelIdMemo, savedReelList?.data);
          const resp = await handleAddComment(data);
          if (resp !== undefined && resp?.data?.success) {
            setIsComment("");
            setCommentId("");
            if (comment_count !== -1) {
              savedReelList.data[comment_count].comment_counts =
                savedReelList.data[comment_count].comment_counts + 1;
            }
            dispatch(setSavedReelList(savedReelList));
            await handleToCommentList(reelIdMemo, 1);
            setCommentLoading(false);
          } else {
            Toast.show(
              resp?.data?.message || "Something went wrong please try again"
            );
            setCommentLoading(false);
          }
        } else {
          setCommentLoading(false);
          Toast.show("Please enter your comment");
        }
        setCommentLoading(false);
      } else {
        setIsComment(false);
        if (Platform.OS === "ios") {
          setTimeout(() => {
            setIsPaymentModal(true);
          }, 200);
        } else {
          setIsPaymentModal(true);
        }
      }
    },
    [isCommentMemo, reelIdMemo, isCurrentPlan]
  );

  // Handle To Reply
  const handleReply = useCallback((value) => {
    setIsComment(`@${value?.user_data[0]?.username} `);
    setSelectedUname(`@${value?.user_data[0]?.username} `);
    setCommentId(value?.comment_id);
    if (textInputRef.current) {
      textInputRef.current.focus();
    }
  }, []);
  // Handle to Hide Comment
  const handleHideComment = async (value) => {
    if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
      const removeObjectsByCommentId = (dataArray, commentId) => {
        return dataArray.map((obj) => {
          if (obj.comment_id === commentId) {
            // Change the hide or unhide here
            obj.is_hide = obj.is_hide ? 0 : 1;
          }
          if (obj.replies && obj.replies.length > 0) {
            obj.replies = removeObjectsByCommentId(obj.replies, commentId);
          }
          return obj;
        });
      };

      const updatedData = commentData?.data?.map((obj) => {
        if (obj.comment_id === value?.comment_id) {
          // Change the nested hide or unhide here
          obj.is_hide = obj.is_hide ? 0 : 1;
        }
        if (obj.replies && obj.replies.length > 0) {
          obj.replies = removeObjectsByCommentId(
            obj.replies,
            value?.comment_id
          );
        }
        return obj;
      });

      dispatch(
        setCommentData({
          ...commentData,
          data: updatedData,
        })
      );
      const d = {
        comment_id: value?.comment_id,
        hide_id: value?.reel_id,
        post_type: "reel",
      };
      const resp = await hideComment(d);

      if (resp !== undefined && resp?.data?.success) {
        console.log("COmment is ");
      } else {
        Toast.show(resp?.data?.message || "Soothing went wrong");
      }
    } else {
      setIsComment(false);
      if (Platform.OS === "ios") {
        setTimeout(() => {
          setIsPaymentModal(true);
        }, 200);
      } else {
        setIsPaymentModal(true);
      }
    }
  };

  const fetchSavedReelData = async (page = 1) => {
    const resp = await getSavedReelData(page, userData?.user_id);

    if (resp?.data?.success && resp?.data?.data && !isEmpty(resp?.data?.data)) {
      dispatch(
        setSavedReelList({
          page: page,
          next_enable: resp?.data?.hasNextPage || false,
          data:
            page > 1
              ? [...savedReelList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
    } else {
      dispatch(setSavedReelList([]));
    }
  };

  // For get User Story Data
  useFocusEffect(
    useCallback(() => {
      getUserList();
    }, [])
  );

  useFocusEffect(
    React.useCallback(() => {
      // Start video playback when the screen gains focus
      if (videoRef.current) {
        videoRef.current.props.paused = false;
      }

      // Pause video playback when the screen loses focus
      return () => {
        if (videoRef.current) {
          videoRef.current.props.pause = true;
        }
      };
    }, [])
  );
  const handleToGetLikeListOne = async (id, page = 1, search) => {
    setLikeListModal(true);
    setIsLikeListLoader(true);
    const resp = await handleToGetLikeList(id, search);

    if (resp !== undefined && resp?.data?.success) {
      const data = await savedReelList?.data?.filter(
        (item) => item?.reel_id === id
      );
      setIsReelId(id);
      dispatch(
        setUserLikeList({
          page: page,
          next_enable: resp?.data?.data?.hasNextPage,
          viewData:
            page > 1
              ? [...commentData?.viewData, ...resp?.data?.data?.viewData]
              : resp?.data?.data?.viewData,
          totalLike: resp?.data?.data?.totalLikes || 0,
          viewCount: data[0]?.view_count || 0,
        })
      );
      setIsLikeListLoader(false);
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsLikeListLoader(false);
      setLikeListModal(false);
    }
  };

  const handleToAction = (action, data, conversation_id, item) => {
    if (type === "anotherProfile" && data?.user_id !== userData?.user_id) {
      if (action === "visit_my_profile") {
        if (data?.user_id !== userData?.user_id) {
          navigation.navigate("ProfileNew", {
            data: data,
            type: "anotherProfile",
          });
        } else {
          navigation.navigate("ProfileNew", { data: data });
        }
      } else if (action === "contact_now") {
        setUerDataMain(data);
        setIsContactModal(true);
      } else if (action === "send_message") {
        navigation.current.navigate("MessagesInfo", {
          userInfo: {
            is_blocked: conversation_id?.is_blocked,
            conversation_id: conversation_id?.conversation_id || "",
            userData: {
              user_dp: data?.user_dp,
              user_id: data?.user_id,
              full_name: data?.full_name,
            },
          },
        });
      } else {
        Toast.show("Current User Not perform any action");
      }
    } else {
      navigation.navigate("BoostedPostInsights", { data: item });
    }
  };

  const handleToPressBoost = (e) => {
    const data = {
      files: e?.ReelData,
      SelectedCountry: e?.country?.split(",").map(Number),
      SelectedState: e?.state?.split(",").map(Number),
      Audience: e?.audience?.split(",").map(Number),
    };
    navigation.navigate("CreateBoostPost", {
      data,
      countryData: countryList,
      stateData: stateList,
      natureOfBusinessDada: natureOfBusinessList,
      type: "reel",
      apiRes: { reel_id: e.reel_id },
    });
  };
  // Render Reels List
  const renderItem = useCallback(
    ({ item, index }) => {
      return (
        <View style={styles.renderItemMainView}>
          <RenderItem
            item={item}
            index={index}
            isLikeAnim={isLikeAnimMemo}
            setIsLikeAnim={setIsLikeAnim}
            setLoader={setIsLikeListLoader}
            loader={isLikeListLoader}
            currentVideoIndex={currentVideoIndexMemo}
            setIsShareListModal={(e) => {
              setShareReelId(item?.reel_id);
              setIsShareListModal(e);
            }}
            handleToFollow={handleToFollow}
            setModalInfoModal={handleToMoreInfo}
            handleToLike={(e, index) => {
              if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
                handleToLike(e, index);
              } else {
                setIsPaymentModal(true);
              }
            }}
            onDoubleTap={onDoubleTap}
            handleToComment={(e) => {
              handleToCommentList(e, 1);
            }}
            handleToLikeCount={(e) => {
              handleToGetLikeListOne(e, 1);
              // setLikeListModal(true);
            }}
            handleToAction={(action, data, conversation_id, item) => {
              handleToAction(action, data, conversation_id, item);
            }}
            videoRef={videoRef}
            type={type}
            navigation={navigation}
            handleToPressBoost={(e) => handleToPressBoost(e)}
          />
        </View>
      );
    },
    [
      currentVideoIndexMemo,
      isLikeAnimMemo,
      savedReelList,
      shareListModalMemo,
      userFollowList,
    ]
  );

  const handleToPressUser = (e) => {
    if (e?.user_id !== userData?.user_id) {
      navigation.navigate("ProfileNew", {
        data: e,
        type: "anotherProfile",
      });
    } else {
      navigation.navigate("ProfileNew", { data: e });
    }
  };

  return (
    <SafeAreaView style={styles.mainView}>
      {/* Reels Header */}
      <View style={styles.headingView}>
        <CHeader
          camera={false}
          headingTitle={"reelTxt"}
          isBackIcon={true}
          headerTitleStyle={styles.headingTitleTextStyle}
          cameraColor={BaseColors.white}
          backIconColor={BaseColors.white}
          cameraSize={28}
          cameraIcon="camera-outline"
          onPressCamera={() =>
            navigation.navigate("AddReelScreen", { goToPost: true })
          }
          handleBackButton={() => navigation.goBack()}
        />
      </View>

      {/* FlatList component to render list of reels */}
      <View style={styles.listMainView}>
        <SwiperFlatList
          data={data}
          index={selectedReelIndex || 0}
          vertical={true}
          renderItem={renderItem}
          pagingEnabled
          keyExtractor={(item, index) => index.toString()}
          legacyImplementation
          scrollEnabled={data?.length > 1}
          onChangeIndex={onChangeIndex}
          windowSize={5} 
          removeClippedSubviews={false}
          initialNumToRender={3}
          maxToRenderPerBatch={3}
          decelerationRate="fast"
          onViewableItemsChanged={({ changed }) => {
            const index = changed[0]?.index;
            if (index != null) {
              // 👇 Trigger API call when user scrolls past halfway
              const totalItems = isReelsArrListMemo?.data?.length || 0;
              if (index >= Math.floor(totalItems / 2)) {
                if (
                  index === savedReelList?.data?.length - 3 &&
                  savedReelList?.next_enable
                ) {
                  fetchSavedReelData(savedReelList?.page + 1);
                }
              }
            }
            
          }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.primary]} // Customize refresh indicator color
              tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
              style={{ zIndex: 1111111111, paddingTop: 10 }}
            />
          }
        />
      </View>
      {/* Share list modal */}
      <ShareListModal
        visible={shareListModalMemo}
        setModalVisible={() => {
          setIsShareListModal(!shareListModalMemo);
          setShareReelId("");
        }}
        buttonText={"Share"}
        listData={userFollowList?.data || []}
        shareItem={shareReelId}
        handleBtnPress={(s, shareItemId) => {
          onSharePost(s?.item, shareItemId);
        }}
        onPressItemClick={(e) => {
          setIsShareListModal(!shareListModalMemo);
          setShareReelId("");
          if (e?.user_id !== userData?.user_id) {
            navigation.navigate("ProfileNew", {
              data: e,
              type: "anotherProfile",
            });
          } else {
            navigation.navigate("ProfileNew", { data: e });
          }
        }}
      />

      {/* Report Modal */}
      <ReportModal
        visible={isReportModalMemo}
        setModalVisible={() => setIsReportModal(false)}
        selectedOptionForReason={(e) => {
          setReportReason(e);
        }}
        textInputData={reportTextInputMemo}
        setTextInputValue={(e) => {
          setReportTextinput(e);
          setIsReasonErrMsg("");
        }}
        reasonValue={reportReasonMemo}
        isErrMessage={reasonErrorMessageMemo}
        onBtnPress={() => {
          handleToReport();
        }}
      />
      {/* More info Modal */}
      {moreInfoModal ? (
        <MoreInfoModal
          listData={
            userData?.user_id !== reelDataMemo?.user_data[0]?.user_id
              ? !reelDataMemo?.is_saved
                ? reelDataMemo?.is_followed
                  ? moreInfo
                  : moreInfo.filter((item) => item?.id !== 4) || []
                : reelDataMemo?.is_followed
                  ? moreInfoUnSaved
                  : moreInfoUnSaved.filter((item) => item?.id !== 4) || []
              : !reelDataMemo?.is_saved
                ? selfUserForDataForSave
                : selfUserForDataForUnSave
          }
          visible={moreInfoModalMemo}
          onBtnPress={(e) => {
            handleToMoreInfoBtnEvent(e);
          }}
          setModalVisible={() => setModalInfoModal(!moreInfoModalMemo)}
        />
      ) : null}

      {/* Comment List Modal */}
      <CommentList
        visible={isCommentListMemo}
        setModalVisible={setIsCommentList}
        listData={commentData?.data || []}
        commentLodging={commentLoadingMemo}
        setCommentLodging={setCommentLoading}
        comment={isCommentMemo}
        handleHideComment={handleHideComment}
        handleReply={handleReply}
        setComment={(e) => setIsComment(e)}
        commentModalLodging={commentModalLoadingMemo}
        submitCommentText={handleToSubmitComment}
        textInputRef={textInputRef}
        handleCommentLike={handleCommentLike}
        handleBottom={() =>
          handleToCommentList(reelIdMemo, false, commentData?.page + 1, true)
        }
        onPressItemClick={(e) => {
          setIsCommentList(false);
            handleToPressUser(e);
          }}
        UserId={isCurrentIndexUser}
        // bottomLoader={bottomLoader}
      />

      <ShareListModal
        visible={likeListModalMemo}
        setModalVisible={() => {
          setLikeListModal(!likeListModalMemo);
          setOnChangeSearchValue("");
        }}
        buttonText={"Share"}
        type={"reel"}
        title={"playAndLikeText"}
        likeCount={userLikeList?.totalLike}
        likeViewCount={userLikeList?.viewCount}
        listData={userLikeList?.viewData || []}
        onChangeSearch={(e) => {
          setOnChangeSearchValue(e);

          handleToGetLikeListOne(reelIdMemo, 1, e);
        }}
        searchValue={searchValue}
        handleBtnPress={(e) => {
          handleToBtnPressForFollow(e);
        }}
        onPressItemClick={(e) => {
          setLikeListModal(!likeListModalMemo);
          setOnChangeSearchValue("");
          handleToPressUser(e);
        }}
      />

      <ContactInfoModal
        visible={isContactModal}
        setModalVisible={(e) => setIsContactModal(e)}
        listData={userDataMain}
      />
      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};

export default memo(ViewReelsForSave);
