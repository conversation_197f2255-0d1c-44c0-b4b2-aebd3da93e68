import { useCallback, useMemo, useRef, useState } from "react";
import { Dimensions, Text, TouchableOpacity, View } from "react-native";
// import Video from "react-native-video";
import styles from "./styles";
import { CustomIcon } from "@config/LoadIcons";
// import FastImage from "react-native-fast-image";
import { BaseColors } from "@config/theme";
// import LottieView from "lottie-react-native";
// import { images } from "@config/images";
import truncate from "lodash-es/truncate";
import { translate } from "../../lang/Translate";
import Toast from "react-native-simple-toast";
import { navigationRef } from "@navigation/NavigationService";
import { useSelector } from "react-redux";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;
const Video = require("react-native-video").default;
const FastImage = require("react-native-fast-image");
// For Render Reel Item Component
export const RenderItem = ({
  item,
  index,
  currentVideoIndex,
  isLikeAnim,
  type,
  setIsLikeAnim = () => {},
  setIsShareListModal = () => {},
  handleToFollow = () => {},
  setModalInfoModal = () => {},
  handleToLike = () => {},
  onDoubleTap = () => {},
  handleToAction = () => {},
  handleToComment = () => {},
  handleToLikeCount = () => {},
  videoRef,
  navigation,
  handleToPressBoost = () => {},
}) => {
  const currentScreen = navigationRef.current.getCurrentRoute()?.name;
  // Expand State's
  const [expandedIndex, setExpandedIndex] = useState(null);
  const [orientation, setOrientation] = useState("portrait");
  const [isLoadVideo, setIsLoadVideo] = useState(false);
  const [isPause, setIsPause] = useState(true);

  // Expand State's Memo
  const expandedIndexMemo = useMemo(() => expandedIndex, [expandedIndex]);
  const orientationMemo = useMemo(() => orientation, [orientation]);
  const isLoadVideoMemo = useMemo(() => isLoadVideo, [isLoadVideo]);

  const { userData } = useSelector((s) => s.auth);
  // last Ref Reference
  const lastTapRef = useRef(0);
  // For Double Tap Function
  const handleDoubleTap = () => {
    const now = Date.now();
    if (now - lastTapRef.current < 300) {
      onDoubleTap();
    }
    lastTapRef.current = now;
  };

  // Expand Description Function
  const toggleExpand = useCallback(
    (index) => {
      setExpandedIndex(expandedIndexMemo === index ? null : index);
    },
    [expandedIndexMemo]
  );

  return (
    // RenderItem MainView
    <TouchableOpacity
      style={styles.renderDataMainView}
      activeOpacity={1}
      onPress={handleDoubleTap}
    >
      <View style={styles.videoView}>
        {item?.ReelData?.thumbnailData?.thumbUrl ? (
          <View
            style={{
              position: "absolute",
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              opacity: isLoadVideoMemo ? 0 : 1,
            }}
          >
            <FastImage
              source={{ uri: item?.ReelData?.thumbnailData?.thumbUrl }}
              style={{
                width: Dimensions.get("screen").width,
                height: Dimensions.get("screen").height,
              }}
              resizeMode="contain"
            />
          </View>
        ) : null}
        {item?.ReelData?.fileUrl ? (
          <Video
            ref={videoRef}
            source={{
              uri: item?.ReelData?.fileUrl,
            }}
            // poster={item?.ReelData?.thumbnailData?.thumbUrl}
            // posterResizeMode={"contain"}
            minLoadRetryCount={6}
            resizeMode={orientationMemo === "landscape" ? "contain" : "contain"}
            paused={
              currentVideoIndex !== index || currentScreen !== "ViewReelForSave"
            }
            onLoadStart={(r) => setIsLoadVideo(false)}
            playInBackground={false}
            playWhenInactive={false}
            onLoad={(e) => {
              setIsLoadVideo(true);
              setOrientation(e?.naturalSize?.orientation);
            }}
            style={styles.videoStyle}
            preload="auto" // Preload videos automatically
            repeat={true}
            hideShutterView={true}
            mixWithOthers="duck"
            onError={() => Toast.show("Failed to load video")}
          />
        ) : null}
      </View>

      {/* Render User Detail Information */}
      <View style={[styles.bottomView, { bottom: 10 }]}>
        <TouchableOpacity
          style={styles.contentView}
          activeOpacity={0.9}
          onPress={() => {
            if (item?.user_data[0]?.user_id !== userData?.user_id) {
              navigation.navigate("ProfileNew", {
                data: item?.user_data[0],
                type: "anotherProfile",
              });
            } else {
              navigation.navigate("ProfileNew", {
                data: item?.user_data[0],
              });
            }
          }}
        >
          <FastImage
            source={{ uri: item?.user_data[0]?.user_dp }}
            style={styles.profileDPStyle}
          />
          <Text style={styles.userNameStyle}>
            {item?.user_data[0]?.username}
          </Text>
          {type === "profile" &&
          item.whereCome === undefined &&
          item.is_boost === 0 ? (
            <TouchableOpacity
              activeOpacity={0.9}
              style={styles.boostBtnView}
              onPress={() => handleToPressBoost(item, navigation)}
            >
              <Text style={styles.boostBtnText}>
                {translate("boostReelText")}
              </Text>
            </TouchableOpacity>
          ) : null}

          {item?.is_self === false && !item?.is_followed ? (
            <TouchableOpacity
              style={styles.btnViewStyle}
              onPress={() => {
                handleToFollow(item?.user_data[0]?.user_id);
              }}
              activeOpacity={0.8}
            >
              <Text style={styles.btnTextStyle}>{translate("followText")}</Text>
            </TouchableOpacity>
          ) : null}
        </TouchableOpacity>

        {item?.is_boost === 1 ? (
          <TouchableOpacity
            style={styles.boostReelView}
            activeOpacity={0.8}
            onPress={() =>
              handleToAction(
                item?.action,
                item?.user_data[0],
                item?.conversation_id,
                item
              )
            }
          >
            <Text style={styles.boostTextStyle}>
              {type !== "profile"
                ? item?.action === "contact_now"
                  ? "Contact Now"
                  : item?.action === "send_message"
                    ? "Send Message"
                    : item?.action === "visit_my_profile"
                      ? "Visit My Profile"
                      : null
                : "View insights"}
            </Text>

            <CustomIcon
              name="BsChevronRight"
              size={16}
              color={BaseColors.white}
            />
          </TouchableOpacity>
        ) : null}

        {type === "profile" &&
        item.whereCome === "boost" &&
        item.is_boost === 0 ? (
          <TouchableOpacity
            style={styles.boostReelView}
            activeOpacity={0.8}
            onPress={() =>
              handleToAction(
                item?.action,
                item?.user_data[0],
                item?.conversation_id,
                item
              )
            }
          >
            <Text style={styles.boostTextStyle}>
              {type !== "profile"
                ? item?.action === "contact_now"
                  ? "Contact Now"
                  : item?.action === "send_message"
                    ? "Send Message"
                    : item?.action === "visit_my_profile"
                      ? "Visit My Profile"
                      : null
                : "View insights"}
            </Text>

            <TouchableOpacity
              activeOpacity={0.9}
              style={styles.boostBtnView}
              onPress={() => handleToPressBoost(item, navigation)}
            >
              <Text style={styles.boostBtnText}>
                {translate("boostReelText")}
              </Text>
            </TouchableOpacity>
          </TouchableOpacity>
        ) : null}
        {}
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={() => toggleExpand(index)}
          style={styles.descMainViewStyle}
        >
          <Text
            style={[
              styles.descTextStyle,
              { maxWidth: expandedIndex === index ? "90%" : null },
            ]}
          >
            {expandedIndex === index
              ? item?.description
              : truncate(item?.description, { length: 35, omission: "..." })}
            {item?.description?.length > 35 ? (
              <Text style={styles.moreLessTextStyle}>
                {expandedIndex === index ? " Less" : " More"}
              </Text>
            ) : null}
          </Text>
        </TouchableOpacity>
      </View>
      {/* Social Media Method's  */}
      <View style={[styles.socialIconStyle, { bottom: 0 }]}>
        {/* Like Design Render With Animation */}
        <View style={styles.socialIconViewStyle}>
          <TouchableOpacity
            onPress={() => handleToLike(item?.reel_id, index)}
            activeOpacity={0.8}
          >
            {item?.is_liked ? (
              <>
                {isLikeAnim && index === currentVideoIndex ? (
                  <LottieView
                    autoSize={true}
                    source={images.like}
                    autoPlay={true}
                    loop={false}
                    style={styles.likeView}
                    onAnimationFinish={() => setIsLikeAnim(false)}
                  />
                ) : null}
                <CustomIcon
                  name="BsSuitHeartFill"
                  size={28}
                  color={BaseColors.activeTab}
                />
              </>
            ) : (
              <CustomIcon
                name="heart-outline"
                size={28}
                color={BaseColors.white}
              />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleToLikeCount(item?.reel_id)}
            activeOpacity={0.8}
          >
            <Text style={styles.countTextStyle}>{item?.like_counts || 0}</Text>
          </TouchableOpacity>
        </View>
        {/* Render Comment Design */}
        <TouchableOpacity
          style={styles.socialIconViewStyle}
          onPress={() => handleToComment(item?.reel_id)}
          activeOpacity={0.8}
        >
          <CustomIcon name="BsChat" size={28} color={BaseColors.white} />
          <Text style={styles.countTextStyle}>{item?.comment_counts}</Text>
        </TouchableOpacity>
        {/* Render Share Design */}
        <TouchableOpacity
          style={styles.socialIconViewStyle}
          onPress={() => setIsShareListModal(true, item)}
          activeOpacity={0.8}
        >
          <CustomIcon
            name="share-3---Copy"
            size={28}
            color={BaseColors.white}
          />
        </TouchableOpacity>
        {/* Render More info Design */}
        <TouchableOpacity
          style={styles.socialIconViewStyle}
          onPress={() => setModalInfoModal({ modalVisible: true, data: item })}
          activeOpacity={0.8}
        >
          <CustomIcon name="dot" size={28} color={BaseColors.white} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};
