import { FontFamily } from "@config/typography";

const { BaseColors } = require("@config/theme");
const { StyleSheet } = require("react-native");

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  priceCalculationView: {
    borderWidth: 1,
    marginHorizontal: 20,
    backgroundColor: BaseColors.grayBackgroundColor,
    borderColor: BaseColors.borderColor,
    borderRadius: 8,
  },
  priceTextStyle: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontColor,
  },
  priceTextStyle1: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontColor,
    position: "absolute",
    top: -12,
  },
  contentContainer: {
    alignItems: "center",
    marginVertical: 36,
  },
  labelTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.grayFontColor,
    marginTop: 6,
  },
  counterPriceView: {
    marginTop: 26,
    alignItems: "center",
  },
  formMainView: {
    marginHorizontal: 20,
    marginVertical: 24,
  },
  separatorView: {
    borderTopWidth: 5,
    borderColor: BaseColors.gray8,
  },
  inputLabelStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
  },
  budgetContainerStyle: {
    margin: 20,
  },
  track: {
    height: 6,
    borderRadius: 2,
    backgroundColor: "#F0F5FF",
  },
  thumb: {
    width: 25,
    height: 25,
    borderRadius: 30 / 2,
    backgroundColor: BaseColors.activeTab,
    borderColor: BaseColors.gray8,
    borderWidth: 3,
  },

  priceContainerStyle: {
    flexDirection: "row",
    justifyContent: "space-between",

    marginTop: -8,
  },
  sliderContainerStyle: {
    marginVertical: 16,
  },
  razorPayImgStyle: {
    height: 32,
    width: 156,
  },
  bottomView: {
    marginHorizontal: 20,
    marginBottom: 6,
  },
});

export default styles;
