import React, { use<PERSON>allback, useEffect, useMemo, useState } from "react";
import { Dimensions, SafeAreaView, ScrollView, Text, View } from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import { PriceHeader } from "./component";
import CMultiDropdown from "@components/CDropDown/CMultiDropdown";
import Slider from "react-native-slider";
import { BaseColors } from "@config/theme";
import FastImage from "react-native-fast-image";
import CButton from "@components/CButton";
import { useFocusEffect } from "@react-navigation/native";
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";
import CDropdown from "@components/CDropDown";
import { callOfActionArr } from "@config/staticData";
import { estimatePeople } from "./apiFunction";
import Toast from "react-native-simple-toast";
import { isEmpty } from "lodash-es";
import {
  getCountryData,
  getCountryStateData,
  natureOfBusinessData,
} from "@screens/SingUpWithCompanyDetail/apiFunctions";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const FadeInUp = require("react-native-reanimated").FadeInUp;
const isString = require("lodash-es").isString;
const images = require("@config/images").images.cashfreeLogo;

const BoostPostSchema = yup.object().shape({
  SelectedCountry: yup
    .array()
    .required("selectCountry")
    .test("is-empty", "selectCountry", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
  SelectedState: yup
    .array()
    .required("selectState")
    .test("is-empty", "selectState", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
  Audience: yup
    .array()
    .required("AudienceMsg")
    .test("is-empty", "AudienceMsg", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
  callOfAction: yup.string().required("Please select call of action"),
});

const BoostPostSchemaWithoutState = yup.object().shape({
  SelectedCountry: yup
    .array()
    .required("selectCountry")
    .test("is-empty", "selectCountry", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
  Audience: yup
    .array()
    .required("AudienceMsg")
    .test("is-empty", "AudienceMsg", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
  callOfAction: yup.string().required("Please select call of action"),
});

const CreateBoostPost = ({ navigation, route }) => {
  // State's
  const [countryData, setCountryData] = useState([]);
  const [stateData, setStateData] = useState([]);
  const [sliderValue, setSliderValue] = useState(1);
  const [sliderDayValue, setSliderDayValue] = useState(1);
  const [countryIds, setCountryIds] = useState();
  const [natureOfBusinessDada, setNatureOfBusinessData] = useState([]);
  const [minimumValue, setMinimumValue] = useState(0);
  const [maximumValue, setMaximumValue] = useState(0);

  // Memo's
  const countryDataMemo = useMemo(() => countryData, [countryData]);
  const sliderValueMemo = useMemo(() => sliderValue, [sliderValue]);
  const stateDataMemo = useMemo(() => stateData, [stateData]);
  const sliderDayValueMemo = useMemo(() => sliderDayValue, [sliderDayValue]);
  const countryIdsMemo = useMemo(() => countryIds, [countryIds]);
  const natureOfBusinessDadaMemo = useMemo(
    () => natureOfBusinessDada,
    [natureOfBusinessDada]
  );
  const minimumValueMemo = useMemo(() => minimumValue, [minimumValue]);
  const maximumValueMemo = useMemo(() => maximumValue, [maximumValue]);
  // Device Width
  const { width } = Dimensions.get("window");

  // Form Variable
  const {
    control,
    handleSubmit,
    setValue,
    clearErrors,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(
      countryIds?.indexOf(101) !== -1
        ? BoostPostSchema
        : BoostPostSchemaWithoutState
    ),
  });
  const fetchCountryData = async (e) => {
    const resp = await getCountryData(e);
    const data = updatedListData(resp);
    setCountryData(data);
  };
  const fetchCountryStateData = async (id) => {
    const resp = await getCountryStateData(id);
    const data = updatedListData(resp);
    setStateData(data);
  };

  // fetch Nature Of Business Data and manipulate response
  const fetchNatureOfBusinessData = async () => {
    const resp = await natureOfBusinessData();

    let updatedData = resp?.data?.data?.map((val) => {
      return {
        ...val,
        value: val.id,
      };
    });

    setNatureOfBusinessData(updatedData);
  };

  const updatedListData = (arr) => {
    let updatedData = arr?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });
    return updatedData;
  };
  useFocusEffect(
    useCallback(() => {
      if (!isEmpty(route?.params?.countryData)) {
        setCountryData(route?.params?.countryData || []);
      }
      if (!isEmpty(route?.params?.stateData)) {
        setStateData(route?.params?.stateData || []);
      }
      if (!isEmpty(route?.params?.natureOfBusinessDada)) {
        setNatureOfBusinessData(route?.params?.natureOfBusinessDada || []);
      }
      if (route?.params?.data?.SelectedCountry) {
        setValue("SelectedCountry", route?.params?.data?.SelectedCountry);
        setCountryIds(route?.params?.data?.SelectedCountry);
      }
      if (
        route?.params?.data?.SelectedState &&
        route?.params?.data?.SelectedCountry?.indexOf(101) !== -1
      ) {
        setValue("SelectedState", route?.params?.data?.SelectedState);
      }
      if (route?.params?.data?.Audience) {
        setValue("Audience", route?.params?.data?.Audience);
      }

      setSliderValue(100);
      setSliderDayValue(2);
    }, [countryDataMemo, stateDataMemo, natureOfBusinessDadaMemo])
  );

  useFocusEffect(
    useCallback(() => {
      if (isEmpty(route?.params?.countryData)) {
        fetchCountryData();
      }
      if (isEmpty(route?.params?.stateData)) {
        fetchCountryStateData(101);
      }
      if (isEmpty(route?.params?.natureOfBusinessDada)) {
        fetchNatureOfBusinessData();
      }
    }, [])
  );

  const getEstimatePeople = async () => {
    const resp = await estimatePeople(sliderDayValueMemo, sliderValueMemo);

    if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
      setMaximumValue(resp?.data?.data?.expected);
      setMinimumValue(resp?.data?.data?.estimated);
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  };

  useEffect(() => {
    getEstimatePeople();
  }, [sliderDayValueMemo, sliderValueMemo]);
  const onSubmit = async (data) => {
    navigation.navigate("BoostPostReelPreview", {
      postData: { ...route?.params },
      boostData: { data },
      natureOfBusinessDada: natureOfBusinessDadaMemo,
      amount: sliderValueMemo,
      days: sliderDayValueMemo,
    });
  };
  return (
    <SafeAreaView style={styles.mainContainer}>
      {/* For Header */}
      <CHeader
        headingTitle={
          route?.params?.type === "post" ? "boostPostText" : "boostReelText"
        }
        handleBackButton={() => navigation.goBack()}
      />
      <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
        {/* Calculate Price Header */}
        <PriceHeader
          price={sliderValueMemo}
          days={sliderDayValueMemo}
          maximumValue={maximumValueMemo}
          miniumValue={minimumValueMemo}
        />

        {/* Form View */}
        <View style={styles.formMainView}>
          <View>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              name="SelectedCountry"
              render={({ field: { onChange, value } }) => {
                return (
                  <Animated.View
                    entering={FadeInDown}
                    style={styles.cInputStyle}
                  >
                    <CMultiDropdown
                      data={countryDataMemo}
                      required
                      multiple
                      placeholder="Country"
                      value={isString(value) ? value.split(",") : value || []}
                      valueProp="indication_id"
                      listProps="name"
                      setItem={(e) => {
                        onChange(e);
                        setCountryIds(e);

                        if (e?.indexOf(101) !== -1) {
                          clearErrors("SelectedState");
                        }
                      }}
                      listPosition="top"
                      setValue={setValue}
                      dropDownName={"SelectedCountry"}
                      showError={errors?.SelectedCountry}
                      errorMsg={errors?.SelectedCountry?.message}
                      setCountryIds={setCountryIds}
                    />
                  </Animated.View>
                );
              }}
            />
          </View>
          {countryIdsMemo?.indexOf(101) !== -1 ? (
            <Animated.View entering={FadeInDown} exiting={FadeInUp}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="SelectedState"
                render={({ field: { onChange, value } }) => {
                  return (
                    <Animated.View
                      entering={FadeInDown}
                      style={styles.cInputStyle}
                    >
                      <CMultiDropdown
                        data={stateDataMemo}
                        required
                        multiple
                        placeholder="State"
                        value={isString(value) ? value.split(",") : value || []}
                        valueProp="indication_id"
                        listProps="name"
                        setItem={(e) => {
                          onChange(e);
                        }}
                        listPosition="top"
                        setValue={setValue}
                        dropDownName={"SelectedState"}
                        showError={errors?.SelectedState}
                        errorMsg={errors?.SelectedState?.message}
                      />
                    </Animated.View>
                  );
                }}
              />
            </Animated.View>
          ) : null}
          <View>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              name="Audience"
              render={({ field: { onChange, value } }) => {
                return (
                  <Animated.View entering={FadeInDown}>
                    <CMultiDropdown
                      data={natureOfBusinessDadaMemo}
                      required
                      multiple
                      placeholder="Select Target Audience"
                      value={isString(value) ? value.split(",") : value || []}
                      valueProp="indication_id"
                      listProps="name"
                      setItem={(e) => {
                        onChange(e);
                      }}
                      listPosition="top"
                      setValue={setValue}
                      dropDownName={"Audience"}
                      showError={errors?.Audience}
                      errorMsg={errors?.Audience?.message}
                    />
                  </Animated.View>
                );
              }}
            />
          </View>
          <View>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              name="callOfAction"
              render={({ field: { onChange, value } }) => {
                return (
                  <Animated.View
                    entering={FadeInDown}
                    style={styles.cInputStyle}
                  >
                    <CDropdown
                      labelplaceholder={"Call of action"}
                      data={callOfActionArr}
                      setItem={(e) => onChange(e?.value)}
                      value={value}
                      isError={errors?.callOfAction}
                      isErrorMsg={errors?.callOfAction?.message}
                    />
                  </Animated.View>
                );
              }}
            />
          </View>
        </View>
        <View style={styles.separatorView} />
        <Animated.View
          style={styles.budgetContainerStyle}
          entering={FadeInDown}
        >
          <Text style={styles.inputLabelStyle}>Per day budget</Text>
          <View style={styles.sliderContainerStyle}>
            <Text
              style={[
                styles.priceTextStyle1,
                {
                  left: (sliderValue / 999) * (width - 80),
                },
              ]}
            >
              ₹{sliderValue}
            </Text>
            <Slider
              trackStyle={styles.track}
              thumbStyle={styles.thumb}
              minimumTrackTintColor={BaseColors.activeTab}
              minimumValue={1}
              maximumValue={999}
              animateTransitions
              value={sliderValueMemo}
              onValueChange={(e) => {
                setSliderValue(Math?.floor(e));
              }}
            />
            <View style={styles.priceContainerStyle}>
              <Text style={[styles.priceTextStyle, { fontSize: 16 }]}>₹1</Text>
              <Text style={[styles.priceTextStyle, { fontSize: 16 }]}>
                ₹999
              </Text>
            </View>
          </View>
        </Animated.View>
        <View style={styles.separatorView} />
        <Animated.View
          style={styles.budgetContainerStyle}
          entering={FadeInDown}
        >
          <Text style={styles.inputLabelStyle}>Duration</Text>
          <View style={styles.sliderContainerStyle}>
            <Text
              style={[
                styles.priceTextStyle1,
                {
                  left: (sliderDayValue / 30) * (width - 60),
                },
              ]}
            >
              {sliderDayValueMemo} Days
            </Text>
            <Slider
              trackStyle={styles.track}
              thumbStyle={styles.thumb}
              minimumTrackTintColor={BaseColors.activeTab}
              minimumValue={1}
              maximumValue={30}
              animateTransitions
              value={sliderDayValueMemo}
              onValueChange={(e) => {
                setSliderDayValue(Math?.floor(e));
              }}
            />
          </View>
        </Animated.View>
        <View style={styles.separatorView} />
        <Animated.View
          style={styles.budgetContainerStyle}
          entering={FadeInDown}
        >
          <Text style={styles.inputLabelStyle}>Payment Method</Text>
          <View style={styles.sliderContainerStyle}>
            <FastImage
              source={images}
              resizeMode="contain"
              style={styles.razorPayImgStyle}
            />
          </View>
        </Animated.View>
      </ScrollView>
      <View style={styles.bottomView}>
        <CButton onBtnClick={handleSubmit(onSubmit)}>
          Boost {route?.params?.type === "post" ? "Post" : "Reel"}
        </CButton>
      </View>
    </SafeAreaView>
  );
};

export default CreateBoostPost;
