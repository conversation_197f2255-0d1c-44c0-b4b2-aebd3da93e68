import { Text, View } from "react-native";
import styles from "./styles";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const FadeInLeft = require("react-native-reanimated").FadeInLeft;
const FadeInRight = require("react-native-reanimated").FadeInRight;

export const PriceHeader = ({
  price = 0,
  days = 0,
  miniumValue = 0,
  maximumValue = 0,
}) => {
  return (
    <Animated.View style={styles.priceCalculationView} entering={FadeInDown}>
      <View style={styles.contentContainer}>
        <Text
          style={styles.priceTextStyle}
        >{`₹${price} Over ${days} days`}</Text>
        <Text style={styles.labelTextStyle}>Total spend</Text>

        <View style={styles.counterPriceView}>
          <Text
            style={styles.priceTextStyle}
          >{`${miniumValue} - ${maximumValue}`}</Text>
          <Text style={styles.labelTextStyle}>Estimated reach</Text>
        </View>
      </View>
    </Animated.View>
  );
};
