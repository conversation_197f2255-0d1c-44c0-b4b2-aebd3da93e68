import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { Keyboard } from "react-native";

export const handleToForgot = async (navigation, data, type) => {
  console.log(data, type);
  Keyboard.dismiss();
  const finalData = {};
  if (type === "email") {
    finalData["email"] = data?.text;
    finalData["type"] = "email";
  } else {
    finalData["phone_number"] = data?.text;
    finalData["phone_code"] = data?.countryCode;
    finalData["type"] = "phone";
  }

  try {
    const resp = await getApiData(
      BaseSetting.endpoints.forgotPass,
      "POST",
      finalData,
      false,
      false
    );

    return resp;
  } catch (error) {
    console.log("🚀 ~ handleToForgot ~ error:", error);
  }
};
