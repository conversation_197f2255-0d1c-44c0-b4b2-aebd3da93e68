import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainFootLogo: {
    height: 120,
    width: 120,
    marginTop: Platform.OS === "ios" ? 0 : 0,
    alignSelf: "center",
  },
  headingText: {
    fontSize: 34,
    fontFamily: FontFamily.RobotSemiBold,
    color: "#343434",
    textAlign: "center",
  },
  contentView: {
    marginHorizontal: 25,
    marginTop: 49,
  },
  descText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#000000",
    marginTop: 15,
    textAlign: "center",
  },
  cInputStyle: {
    marginTop: 25,
  },
  btnStyle: {
    marginTop: 45,
  },
  helloImg: {
    height: 30,
    width: 30,
  },
  helloImagAndText: {
    flexDirection: "row",
    marginTop: 15,
    justifyContent: "center",
  },
  inputFields: {
    marginBottom: 15,
  },
  descText1: {
    fontSize: 24,
    fontFamily: FontFamily.regular,
    color: "#343434",
    marginBottom: 5,
    textAlign: "center",
  },
  KeyboardAvoidingViewStyle: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
});
export default styles;
