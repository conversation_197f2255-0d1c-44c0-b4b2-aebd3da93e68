import React, { memo, useCallback, useMemo, useState } from "react";
import {
  Image,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from "react-native";
import styles from "./styles";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { images } from "@config/images";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import { handleToForgot } from "./apiFunctions";
import {
  getMobileNumberLength,
  mobileValidation,
} from "@app/utils/commonFunction";

const forgotText = yup.object().shape({
  text: yup.string().required("enterMobileNumber").email("validEmailAddress"),
  singUpType: yup.string().default("email"),
});

const mobileLoginText = yup.object().shape({
  text: yup
    .string()
    .required("enterMobileNumber")
    .matches(/^\d+$/, "Please enter only numeric value"),
  countryCode: yup.string().default("91"),
  singUpType: yup.string().default("phone"),
});

const CForgotPasswordEmail = ({ navigation, route }) => {
  const type = route?.params?.loginType;

  const [mobileNoLength, setMobileLength] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const [countryCode, setCountryCode] = useState("91");
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const mobileNoLengthMemo = useMemo(() => mobileNoLength, [mobileNoLength]);
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
    getValues,
    reset,
  } = useForm({
    resolver: yupResolver(type === "email" ? forgotText : mobileLoginText),
  });

  const onSubmit = useCallback(
    async (data) => {
      let isValid = true;
      const mobileNumberValidation = await mobileValidation(
        data?.text,
        mobileNoLengthMemo
      );

      if (type !== "email") {
        if (!mobileNumberValidation?.isValid) {
          isValid = false;
          setError("text", {
            message: mobileNumberValidation?.validMessage,
          });
        } else {
          isValid = true;
        }
      }
      if (isValid) {
        setIsLoading(true);
        const respData = await handleToForgot(navigation, data, type);

        if (respData?.data?.success) {
          navigation.navigate("ForgotOtpScreen", {
            data: { data, signUpType: type },
            signUpType: type,
          });
          reset();
          setIsLoading(false);
        } else {
          setIsLoading(false);
          setError("text", { message: respData?.data?.message });
        }
      }
      setIsLoading(false);
    },
    [isLoadingMemo]
  );
  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader handleBackButton={() => navigation.goBack()} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : null}
        style={styles.KeyboardAvoidingViewStyle}
      >
        <ScrollView>
          <Image
            source={images.footMainLogo}
            style={styles.mainFootLogo}
            resizeMode={"contain"}
          />
          <View style={styles.contentView}>
            <Text style={styles.headingText}>
              {translate("forgotPasswordText")}?
            </Text>

            <Text style={styles.descText}>
              {type === "email"
                ? translate("enterEmailMessage")
                : translate("forgotMainScreenText")}
            </Text>
            {type === "email" ? (
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="text"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.cInputStyle}>
                    <CInput
                      placeholderText={
                        type === "email"
                          ? translate("EmailText")
                          : translate("MobileNumberPlaceHolder")
                      }
                      returnKeyType="next"
                      // keyBoardType="email-address"
                      value={value}
                      onChange={onChange}
                      onSubmit={handleSubmit((e) => onSubmit(e))}
                      isError={errors?.text}
                      isErrorMsg={errors?.text?.message}
                    />
                  </View>
                )}
              />
            ) : (
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="text"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.cInputStyle}>
                    <CInput
                      placeholderText={translate("MobileNumberPlaceHolder")}
                      returnKeyType="next"
                      inputType="mobile"
                      keyBoardType="number-pad"
                      maxLength={mobileNoLengthMemo}
                      value={value}
                      onChange={onChange}
                      onSubmit={handleSubmit((e) => onSubmit(e))}
                      isError={errors?.text}
                      isErrorMsg={errors?.text?.message}
                      selectedCountryCode={(e) => {
                        const country_length = getMobileNumberLength(
                          e?.country_code
                        );
                        setMobileLength(country_length);
                        setValue("text", undefined);
                        const yourData = e?.dial_code?.split("+");
                        setCountryCode(yourData);
                        setValue("countryCode", yourData[1]);
                      }}
                      countryCodeValue={
                        getValues("countryCode")
                          ? `+${getValues("countryCode")}`
                          : `+${countryCode}`
                      }
                    />
                  </View>
                )}
              />
            )}
            <CButton
              style={styles.btnStyle}
              onBtnClick={handleSubmit((e) => onSubmit(e))}
              loading={isLoadingMemo}
            >
              {translate("GetOtpText")}
            </CButton>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
export default memo(CForgotPasswordEmail);
