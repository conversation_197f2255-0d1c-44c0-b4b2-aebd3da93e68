import {
  View,
  FlatList,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Platform,
} from "react-native";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import styles from "./style";
import { CustomHeader, SwitchComponent } from "./component";
import { useFocusEffect } from "@react-navigation/native";
import CButton from "@components/CButton";
import {
  getPlanList,
  getRemainingPlanData,
  isValidCoupon,
  paySubscription,
} from "./apiFunction";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import Toast from "react-native-simple-toast";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import CHeader from "@components/CHeader";
import { FontFamily } from "@config/typography";
import { isEmpty } from "lodash-es";
import CurrentPlanInfoModal from "@components/CurrentPlanInfoModal";
import Loader from "@components/Loader";
import LottieView from "lottie-react-native";
import { images } from "@config/images";
import FastImage from "react-native-fast-image";
import { useIAP } from "react-native-iap";
import * as RNIap from "react-native-iap";
import VerifyModal from "@components/VerifyModal";
import dayjs from "dayjs";
import { translate } from "../../lang/Translate";
import CInput from "@components/TextInput";
import { getSpecialOffer } from "@screens/Home/apiCallFunction";
import OfferComponent from "@components/OfferComponent";
const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

const {
  setSubScriptionPlan,
  setIsCurrentPlan,
  setActivePlanData,
  setSpecialOfferData,
} = authAction;
const PaymentPlans = ({ navigation, route }) => {
  // Redux Variable
  const dispatch = useDispatch();
  const {
    subscriptionPlan,
    isCurrentPlan,
    activePlanData,
    adminSettingData,
    userData,
    specialOfferData,
  } = useSelector((auth) => auth.auth);
  
  const isDescriptedPrice = adminSettingData?.find(
    (obj) => activePlanData?.plan_type === 'special_offer' ? 
                obj?.slug === "SPECIALOFFERPLANONGOING" :
                activePlanData?.plan_type === 'coupon' ? 
                  obj?.slug === "COUPONOFFERONGOING" :
                  obj?.slug === "DESCREPTEDPLANTEXT"
  );

  const offerText = adminSettingData?.find((obj) => obj?.slug === "OFFERTEXT");
  const { subscriptions, getSubscriptions } = useIAP();

  const flatListRef = useRef(null);

  const [onSwitch, setOnSwitch] = useState(false);
  const [desc, setIsDesc] = useState([]);
  const [clickId, setClickId] = useState();
  const [isAtLastIndex, setIsAtLastIndex] = useState(false);
  const [screenTitle, setScreenTitle] = useState("");
  const [selectedItem, setSelectedItem] = useState([]);
  const [currentActivePlan, setCurrentActivePlan] = useState([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isModal, setIsModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const [isApplyCouponData, setApplyCouponData] = useState({});
  const [applyCouponModal, setApplyCouponModal] = useState(false);
  const [isScreenLoading, setIsScreenLoading] = useState(false);
  const [isCurrentPlanModal, setIsCurrentPlanModal] = useState(false);

  const onSwitchMemo = useMemo(() => onSwitch, [onSwitch]);
  const clickIdMemo = useMemo(() => clickId, [clickId]);
  const descMemo = useMemo(() => desc, [desc]);
  const isScreenLoadingMemo = useMemo(() => isScreenLoading, [isScreenLoading]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);

  const screenTitleMemo = useMemo(() => screenTitle, [screenTitle]);
  const selectedItemMemo = useMemo(() => selectedItem, [selectedItem]);

  const cHeader = useCallback(() => {
    return (
      <CustomHeader
        title={screenTitleMemo || "Basic Plan"}
        onBtnClick={() => navigation.goBack()}
      />
    );
  }, [screenTitleMemo]);

  let data = onSwitchMemo
    ? subscriptionPlan.filter((item) => item.plan_type_label === "Annually" && item?.plan_type !== 'special_offer' && item?.plan_type !== 'coupon')
    : subscriptionPlan.filter((item) => item.plan_type_label !== "Annually" && item?.plan_type !== 'special_offer' && item?.plan_type !== 'coupon');

  useEffect(() => {
    if (data?.length !== 0) {
      if (flatListRef.current && onSwitchMemo) {
        if (isAtLastIndex) {
          flatListRef.current.scrollToIndex({ animated: true, index: 0 });
        }
      }
    }
  }, [onSwitchMemo, isAtLastIndex]);

  const ITEMS_PER_PAGE = 3;
  // Calculate the number of pages based on data length
  const totalPages = Math.ceil(data.length / ITEMS_PER_PAGE);

  // Update active dot based on current page
  let currentPage = Math.floor(activeIndex / ITEMS_PER_PAGE);

  const renderPaginationDots = () => {
    if (totalPages > 1) {
      return Array.from({ length: totalPages }).map((_, index) => {
        return (
          <View key={index}>
            <Animated.View
              style={[
                styles.paginationDot,
                {
                  borderWidth: 2,
                  borderColor:
                    currentPage === index
                      ? BaseColors.activeTab
                      : BaseColors.gray,
                  backgroundColor:
                    currentPage === index
                      ? BaseColors.activeTab
                      : BaseColors.white,
                },
              ]}
            />
          </View>
        );
      });
    }
  };

  useEffect(() => {
    setSelectedItem(data[0]);
  }, [onSwitchMemo]);

  useFocusEffect(
    useCallback(() => {
      getAPiData(true);
      getSpecialOfferData();
    }, [])
  );

  const setCurrentIndex = async (currentPlan, subscriptionPlan1) => {
    if (currentPlan?.length !== 0) {
      if (currentPlan[0]?.plan_type === "annual") {
        setOnSwitch(true);
        const data = subscriptionPlan1?.filter(
          (item) => item?.plan_type === "annual"
        );

        if (data?.length !== 0) {
          const index = data?.findIndex(
            (item) => item?.plan_id === currentPlan[0]?.plan_id
          );

          if (index !== -1) {
            setClickId(index);
            setSelectedItem(data[index]);
          } else {
            setClickId(0);
            setSelectedItem(data[0]);
          }
        }
      } else {
        setOnSwitch(false);
        const data = subscriptionPlan1?.filter(
          (item) => item?.plan_type !== "annual"
        );
        if (data?.length !== 0) {
          const index = data?.findIndex(
            (item) => item?.plan_id === currentPlan[0]?.plan_id
          );
          if (index !== -1) {
            setClickId(index);
            setSelectedItem(data[index]);
          } else {
            setClickId(0);
            setSelectedItem(data[0]);
          }
        }
      }
    } else {
      setOnSwitch(false);
      const data = subscriptionPlan1?.filter(
        (item) => item?.plan_type !== "annual"
      );
      if (data?.length !== 0) {
        const index = data?.findIndex(
          (item) => item?.plan_id === currentPlan[0]?.plan_id
        );
        if (index !== -1) {
          setClickId(index);
          setSelectedItem(data[index]);
        } else {
          setClickId(0);
          setSelectedItem(data[0]);
        }
      }
    }
  };

  const apiFuncForPurchase = async (plan_id, product_id, s) => {
    setIsLoading(true);
    const data = {
      plan_id: plan_id,
      DEVICE: Platform.OS,
      product_id: product_id,
      package_name: "com.footbizz",
    };
    if (Platform.OS === "android" && !isEmpty(s)) {
      data.order_id = s?.transactionId;
      data.purchase_token = s?.purchaseToken;
      data.purchase_time = s?.transactionDate;
    }
    if (Platform.OS === "ios" && !isEmpty(s)) {
      data.order_id = s?.originalTransactionIdentifierIOS || s?.transactionId;
      data.transaction_receipt = s?.transactionReceipt;
      data.transaction_date = s?.transactionDate;
      data.transaction_id =
        s?.originalTransactionIdentifierIOS || s?.transactionId;
    }
    const resp = await paySubscription(data);
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      await getAPiData(false);
      // navigation.goBack();
      setIsModal(true);
      setIsLoading(false);
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsLoading(false);
    }
  };

  // Get new plan offer token for purchase
  const getOfferToken = (subscriptions, productId) => {
    const currentProduct = subscriptions.find((product) => {
      return productId === product.productId;
    });
    let offerToken = "";
    if (currentProduct) {
      offerToken = currentProduct?.subscriptionOfferDetails[0]?.offerToken;
    }

    return offerToken;
  };

  const handleToPayPayment = useCallback(
    async (plan_id, product_id) => {
      if (product_id) {
        const findSubPlan = subscriptions.find(
          (s) => s?.productId === product_id
        );
        setIsLoading(true);
        // Current on going plan details
        const availablePurchases = await RNIap.getAvailablePurchases();
        // check if current plan is ongoing and purchaseToken is available or not
        const purchaseToken =
          availablePurchases.length > 0 &&
          availablePurchases[0]?.autoRenewingAndroid &&
          availablePurchases[0]?.purchaseToken
            ? availablePurchases[0]?.purchaseToken
            : "";

        // New plan offer token needs set, Get this token in subscription list > item object > subscriptionOfferDetails > offerToken
        const offerToken = getOfferToken(subscriptions, findSubPlan?.productId); //findSubPlan?.productId is your new product id which one you try to purchase

        // Use this data when you want to replace your current plan with new plan
        const gradeSubParams =
          Platform.OS === "android" && !isEmpty(purchaseToken)
            ? {
                purchaseTokenAndroid: purchaseToken, // Current on-going plan token
                prorationModeAndroid:
                  RNIap.ProrationModesAndroid.IMMEDIATE_WITHOUT_PRORATION, // Plan change without any time of price carry forward from previous plan
              }
            : {};
        try {
          await RNIap.requestSubscription({
            subscriptionOffers: [
              {
                sku: findSubPlan?.productId,
                offerToken: offerToken,
              },
            ],
            ...gradeSubParams,
          })
            .then(async (s) => {
              // Acknowledge your purchase only for android it's work
              await RNIap.acknowledgePurchaseAndroid({
                token: s[0].purchaseToken,
              })
                .then(async (r) => {
                  console.log("🚀 ~ .then ~ r:", r);
                  // API call for our side
                  apiFuncForPurchase(plan_id, product_id, s[0]);
                  setIsLoading(false);
                })
                .catch((f) => {
                  console.log("🚀 ~ acknowledgePurchase ~ f:", f);
                  setIsLoading(false);
                });
            })
            .catch((s) => {
              setIsLoading(false);
            });
        } catch (error) {
          setIsLoading(false);
          console.log("error-----------", error);
        }
      }
    },
    [selectedItemMemo, isLoadingMemo, subscriptionPlan]
  );

  const handleToPayPaymentForiOS = useCallback(
    async (plan_id, product_id) => {
      console.log("🚀 ~ product_id:", product_id);
      if (product_id) {
        const findSubPlan = subscriptions.find(
          (s) => s?.productId === product_id
        );

        try {
          await RNIap.requestSubscription({
            sku: findSubPlan?.productId,
            appAccountToken: userData?.user_id,
          })
            .then(async (s) => {
              await RNIap.finishTransaction({
                purchase: s,
                isConsumable: false,
                developerPayloadAndroid: "",
              })
                .then(async (r) => {
                  apiFuncForPurchase(plan_id, product_id, s);
                })
                .catch((w) => {
                  console.log("🚀 ~ w:", w);
                });
            })
            .catch((s) => {
              setIsLoading(false);
            });
        } catch (error) {
          setIsLoading(false);
          console.log("error-----------", error);
        }
      }
    },
    [selectedItemMemo, isLoadingMemo, subscriptionPlan]
  );

  const handleToPay = useCallback(
    async (plan_id, price, product_id) => {
      // if (isCurrentPlan.length !== 0) {
      // if (price > isCurrentPlan[0]?.price) {
      if (Platform.OS === "android") {
        handleToPayPayment(plan_id, product_id);
        // apiFuncForPurchase(plan_id, product_id);
      } else {
        // apiFuncForPurchase(plan_id, product_id);
        handleToPayPaymentForiOS(plan_id, product_id);
      }
      // } else {
      //   Toast.show("You are not purchase this subscription");
      // }
      // } else {
      // handleToPayPayment(plan_id, price, product_id);
      // }
    },
    [selectedItemMemo, isLoadingMemo]
  );

  const getAPiData = useCallback(
    async (isLoader) => {
      {
        isLoader ? setIsScreenLoading(true) : null;
      }
      const resp = await getPlanList();

      if (resp?.data?.success && resp?.data?.data) {
        const planListArr = resp?.data?.data;
        dispatch(setSubScriptionPlan(planListArr));
        const getProductIdArr = planListArr.map((s) => {
          if (s?.product_id) {
            return s?.product_id;
          }
        });
        const finalPIdArr = getProductIdArr.filter((s) => s !== undefined);
        if (!isEmpty(finalPIdArr)) {
          await getSubscriptions({ skus: finalPIdArr });
        }
        const currentPlan = await planListArr.filter(
          (item) => item?.active_plan === true
        );
        dispatch(setIsCurrentPlan(currentPlan));
        if (route?.params?.isSpecialOffer) {
          handleToSpecialOffer(planListArr);
        } else {
          await setCurrentIndex(currentPlan, planListArr);
        }
        data = onSwitchMemo
          ? planListArr.filter((item) => item.plan_type_label === "Annually" 
              && item?.plan_type !== 'special_offer' && item?.plan_type !== 'coupon')
          : planListArr.filter((item) => item.plan_type_label !== "Annually" 
              && item?.plan_type !== 'special_offer' && item?.plan_type !== 'coupon');
        setApiData(planListArr);
        setIsDesc(data[0]?.features_list_preview);

        setIsScreenLoading(false);
      } else {
        Toast.show(resp?.data?.message || "No Plan Available");
        setIsScreenLoading(false);
      }
      setIsScreenLoading(false);
    },
    [onSwitchMemo, isScreenLoadingMemo, isCurrentPlan]
  );
        

  const setApiData = (data) => {
    setIsDesc(data[0]?.features_list_preview);
  };

  const renderItem = ({ item, index }) => {
    return (
      <View key={index}>
        {item?.discount_value && item?.discount_value > 0 ? (
          <View
            style={{
              position: "absolute",
              top: -10,
              right: index === data.length - 1 ? 0 : -15,
              zIndex: 11111,
            }}
          >
            <FastImage
              source={images.specialOffer}
              style={{ height: 60, width: 60 }}
              resizeMode="contain"
            />
          </View>
        ) : null}
        <TouchableOpacity
          style={[
            styles.planItemMainView,
            {
              marginLeft: index === 0 ? 20 : 10,
              marginRight: index === data.length - 1 ? 30 : 0,
              marginVertical: 10,
              borderWidth: index === clickIdMemo ? 1.75 : 1.75,
              borderColor:
                index === clickIdMemo ? BaseColors.activeTab : BaseColors.white,
            },
          ]}
          onPress={() => {
            setClickId(index);
            setIsDesc(item?.features_list_preview);
            setScreenTitle(item?.name);
            setActiveIndex(index);
            setSelectedItem(item);
          }}
          activeOpacity={0.8}
        >
          <Text style={[styles.planTypeTextStyle]} numberOfLines={2}>
            {item?.name}
          </Text>
          {/* <Text style={styles.planDuration}>{item?.plan_type_label}</Text> */}
          <Text style={styles.planPriceTextStyle}>
            ₹{Number(item?.final_price)}
          </Text>
          <Text style={styles.planDuration}>
            / {item?.plan_type_label == "Annually" ? "year" : "month"}
          </Text>
          {item?.discount_value && item?.discount_value > 0 ? (
            <Text
              style={[
                styles.planPriceTextStyle,
                {
                  fontSize: 16,
                  textDecorationLine: "line-through",
                  color: BaseColors.gray2,
                },
              ]}
            >
              ₹{Number(item?.price)}
            </Text>
          ) : null}

          <View style={styles.clickedPositionStyle}>
            {index === clickIdMemo ? (
              <View
                style={{
                  height: 23,
                  width: 23,
                  backgroundColor: BaseColors.white,
                  borderRadius: 23,
                }}
              >
                <CustomIcon
                  name="BsCheckCircleFill"
                  size={22}
                  color={BaseColors.activeTab}
                />
              </View>
            ) : null}
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const descRenderItem = useCallback(
    ({ item, index }) => {
      return (
        <Animated.View style={styles.descRenderMainView} entering={FadeInDown}>
          <CustomIcon name="correct-icon" size={14} color={BaseColors.gray11} />
          <View style={{ flex: 1 }}>
            <Text style={styles.descTextStyle}>{item?.title}</Text>
          </View>
        </Animated.View>
      );
    },
    [clickIdMemo]
  );
  const onViewableItemsChanged = useCallback(
    ({ viewableItems }) => {
      // viewableItems is an array containing information about the currently visible items
      if (viewableItems.length > 0) {
        // Assuming you're using horizontal FlatList
        setActiveIndex(viewableItems[0].index);
      }
    },
    [activeIndex]
  );
  const handleToToggleSwitch = () => {
    setOnSwitch(!onSwitchMemo);
    setClickId(0);
    setIsDesc(data[0]?.desc);
    if (data?.length !== 0) {
      if (flatListRef?.current && onSwitchMemo) {
        if (isAtLastIndex) {
          flatListRef?.current?.scrollToIndex({ animated: true, index: 0 });
        }
      }
    }
  };
  const handleScroll = (event) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const contentHeight = event.nativeEvent.contentSize.height;
    const layoutHeight = event.nativeEvent.layoutMeasurement.height;
    setIsAtLastIndex(offsetY + layoutHeight >= contentHeight);
    const { contentOffset } = event.nativeEvent;
    const index = Math.round(contentOffset.x / 110);

    setActiveIndex(index);
  };
  const switchComponent = useCallback(() => {
    return (
      <SwitchComponent
        offSwitchText={"Monthly"}
        onSwitchText={"Annually"}
        switchValue={onSwitchMemo}
        onChangeSwitch={() => {
          handleToToggleSwitch();
        }}
      />
    );
  }, [onSwitchMemo]);
  const handleToRemainingPlan = async () => {
    setIsScreenLoading(true);
    const resp = await getRemainingPlanData();
    if (resp !== undefined && resp?.data?.success) {
      setCurrentActivePlan(resp?.data?.data);
      dispatch(setActivePlanData(resp?.data?.data));
      setIsCurrentPlanModal(true);
      setIsScreenLoading(false);
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
      setIsScreenLoading(false);
    }
    setIsScreenLoading(false);
  };
  const handleToSpecialOffer = (type) => {
    let speData = [];
    if (type === "best") {
      speData = subscriptionPlan?.filter(
        (item) => item?.is_best_offer === true
      );
    } else {
      speData = subscriptionPlan?.filter(
        (item) => item?.is_special_offer === true
      );
    }
    if (speData?.length !== 0) {
      if (speData[0]?.plan_type === "annual") {
        setOnSwitch(true);
        const data = subscriptionPlan?.filter(
          (item) => item?.plan_type === "annual"
        );

        if (data?.length !== 0) {
          const index = data?.findIndex(
            (item) => item?.plan_id === speData[0]?.plan_id
          );

          if (index !== -1) {
            setClickId(index);
            setSelectedItem(data[index]);
          } else {
            setClickId(0);
            setSelectedItem(data[0]);
          }
        }
      } else {
        setOnSwitch(false);
        const data = subscriptionPlan?.filter(
          (item) => item?.plan_type !== "annual"
        );
        if (data?.length !== 0) {
          const index = data?.findIndex(
            (item) => item?.plan_id === speData[0]?.plan_id
          );
          if (index !== -1) {
            setClickId(index);
            setSelectedItem(data[index]);
          } else {
            setClickId(0);
            setSelectedItem(data[0]);
          }
        }
      }
    } else {
      console.log("Something went wrong please try again");
    }
  };

  const onApplyCoupon = async () => {
    setIsBtnLoading(true);
    const resp = await isValidCoupon(couponCode);
    if (resp?.data?.success) {
      // navigation.navigate("AddOnPlans", {
      //   type: "couponCode",
      //   data: { ...resp?.data?.data, couponCode: couponCode },
      // });

      setApplyCouponData({ ...resp?.data?.data, couponCode: couponCode });
      setCouponCode("");
      setApplyCouponModal(true);
      setIsBtnLoading(false);
    } else {
      Toast.show(resp?.data?.message || "Something went wrong");
      setIsBtnLoading(false);
    }
  };

  const navigateToSpecialOffer = () => {
    navigation.navigate("AddOnPlans", {
      type: "couponCode",
      data: { ...specialOfferData, isSpecialOffer: true },
    });
  };
  /**
   * Function to get special offer data
   * @returns {Promise<void>}
   */
  const getSpecialOfferData = async () => {
    /**
     * Call API to get special offer data
     */
    const resp = await getSpecialOffer();
    if (resp?.data?.success) {
      /**
       * On success, set special offer data in reducer
       */
      dispatch(setSpecialOfferData(resp?.data?.data));
    } else {
      /**
       * On failure, set special offer data as empty object in reducer
       */
      dispatch(setSpecialOfferData({}));
    }
  };

  const showCurrentPlan = (currentPlanData) => {
    return (
      <TouchableOpacity
            onPress={() => {
              if(currentPlanData?.plan_type !== 'special_offer' && 
              currentPlanData?.plan_type !== 'coupon')
              {
                setCurrentIndex(isCurrentPlan, subscriptionPlan)}
                }
              }
            activeOpacity={0.9}
          >
            <View style={styles.activePlanMainView}>
              <View style={styles.activePlanHeaderView}>
                <Text style={styles.currentPlanText}>Current Plan</Text>
                <Text style={styles.currentPlanText}>
                  {currentPlanData?.plan_type_label}
                </Text>
              </View>
              <View style={[styles.activePlanHeaderView, { marginTop: 10 }]}>
                <Text
                numberOfLines={2}
                  style={[
                    styles.currentPlanText,
                    { fontSize: 18, color: BaseColors.black, maxWidth: '80%' },
                  ]}
                >
                  {currentPlanData?.name}
                </Text>
                <Text
                  style={[
                    styles.currentPlanText,
                    {
                      fontSize: 18,
                      color: BaseColors.black,
                      fontFamily: FontFamily.RobotSemiBold,
                    },
                  ]}
                >
                  ₹{Number(currentPlanData?.final_price).toFixed(2)}
                </Text>
              </View>
              {activePlanData?.plan_type === 'special_offer' ||
                activePlanData?.plan_type === 'coupon' ? (<View style={[styles.activePlanHeaderView, { marginTop: 5 }]}>
                <Text style={styles.currentPlanText}>Expiry Date</Text>
                <Text style={styles.currentPlanText}>
                  { dayjs(activePlanData?.plan_expire_date).format(
                      "DD/MM/YYYY HH:mmA"
                    )}
                </Text>
              </View>) : 
                (<View style={[styles.activePlanHeaderView, { marginTop: 5 }]}>
                <Text style={styles.currentPlanText}>Renewal Date</Text>
                <Text style={styles.currentPlanText}>
                  {dayjs(activePlanData?.renewal_Date).format(
                    "DD/MM/YYYY HH:mmA"
                  ) ||
                    dayjs(activePlanData?.plan_expire_date).format(
                      "DD/MM/YYYY HH:mmA"
                    )}
                </Text>
              </View>)}
              <TouchableOpacity
                style={styles.moreInfoView}
                activeOpacity={0.8}
                onPress={() => {
                  handleToRemainingPlan();
                }}
              >
                <CustomIcon
                  name="BsChevronDown"
                  size={20}
                  color={BaseColors.activeTab}
                />
              </TouchableOpacity>
            </View>

            <View style={{ alignItems: "flex-start" }}>
              <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => {
                  navigation.navigate("CancelCurrentPlan", {
                    currentPlan: isCurrentPlan,
                  });
                }}
                style={{
                  marginHorizontal: 20,
                  marginBottom: 10,
                }}
              >
                <Text style={styles.cancelSubscriptionTextStyle}>
                  Cancel Subscription
                </Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
    )
  }

  return (
    <SafeAreaView style={styles.mainView}>
      {/* Custom Header */}

      {route?.params?.isHome === "yes" ? (
        cHeader()
      ) : (
        <CHeader
          headingTitle={"mySubscription"}
          handleBackButton={() => navigation.goBack()}
        />
      )}
      {isScreenLoadingMemo ? <Loader /> : null}
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
      {!isEmpty(specialOfferData) && (
          <OfferComponent specialOfferDataObj={specialOfferData} handleToSpecialOffer={() => navigateToSpecialOffer("best")} />
      )}
        {route?.params?.isHome !== "yes" && (!isEmpty(isCurrentPlan)) ? (
          showCurrentPlan(isCurrentPlan[0])
        ) : null}
        {route?.params?.isHome !== "yes" && 
        isEmpty(isCurrentPlan) && 
        !isEmpty(activePlanData) &&
        (activePlanData?.plan_type === 'special_offer' || activePlanData?.plan_type === 'coupon')  ? (
          showCurrentPlan(activePlanData)
        ) : null}
        {/* Render Discount Value */}
        {/* {subscriptionPlan?.filter((item) => item?.is_best_offer === true)[0]
          ?.discount_value && isEmpty(specialOfferData) ? (
          <>
            <TouchableOpacity
              style={styles.discountMainView}
              onPress={() => handleToSpecialOffer("best")}
              activeOpacity={0.8}
            >
              <View style={styles.textView}>
                <View style={{ marginTop: 10, maxWidth: "80%" }}>
                  <Text style={styles.discountTextView} numberOfLines={3}>
                    {
                      subscriptionPlan?.filter(
                        (item) => item?.is_best_offer === true
                      )[0]?.discount_value
                    }
                    % off {offerText?.value}
                  </Text>
                  <Text style={styles.discountTextView}>GET OFFER</Text>
                </View>
                <View>
                  <LottieView
                    source={images.offerLottie}
                    autoPlay={true}
                    loop
                    style={{
                      height: 55,
                      width: 65,
                    }}
                  />
                </View>
              </View>
            </TouchableOpacity>
          </>
        ) : null} */}

        {/* Coupon View */}
       {isEmpty(activePlanData) ||  activePlanData?.plan_status !== 1 ? (
        <View style={{ marginHorizontal: 20, marginVertical: 12 }}>
          <Text style={styles.couponText}>Coupon Code</Text>
          <View style={styles.couponView}>
            <View style={{ flex: 2 }}>
              <CInput
                placeholderText="Enter Code"
                containerStyle={{ height: 40, borderWidth: 0 }}
                fontFamilyStyle={{ minHeight: 40 }}
                value={couponCode}
                onChange={(text) => setCouponCode(text)}
              />
            </View>
            <View style={{ flex: 1 }}>
              <CButton
                containerStyle={{ height: 40, paddingVertical: 8 }}
                onBtnClick={() => onApplyCoupon()}
                loading={isBtnLoading}
                disabled={isEmpty(couponCode)}
              >
                APPLY
              </CButton>
            </View>
          </View>
        </View>) : null}
        {/* Switch For Monthly or Annually */}
        {switchComponent()}

        {/* View For Card Description */}
        {descMemo?.length !== 0 ? (
          <View style={{ marginVertical: 30, marginHorizontal: 20 }}>
            <Animated.FlatList
              data={selectedItemMemo?.features_list_preview || descMemo || []}
              renderItem={descRenderItem}
            />
          </View>
        ) : null}
        {/* View Card List */}
        <View>
          <FlatList
            ref={flatListRef}
            data={data || []}
            renderItem={renderItem}
            keyExtractor={({ item, index }) => index}
            horizontal
            onScroll={handleScroll}
            onViewableItemsChanged={onViewableItemsChanged}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ flexGrow: 1 }}
          />
        </View>
        {/* Button For AddON */}
        <View style={{ marginHorizontal: 20, marginVertical: 31 }}>
          <CButton
            type={"outlined"}
            onBtnClick={() => navigation.navigate("AddOnPlans", { type: "" })}
            disabled={activePlanData?.plan_status !== 1}
          >
            Add on Plans
          </CButton>
        </View>
        {Platform.OS === "ios" ? (
          <View style={{ marginHorizontal: 20 }}>
            <View
              style={{
                marginTop: 20,
                borderWidth: 0.75,
                borderColor: BaseColors.gray,
              }}
            ></View>
            <View style={{ marginTop: 15 }}>
              <Text
                style={[
                  styles.currentPlanText,
                  { textAlign: "justify", marginBottom: 12 },
                ]}
              >
                • Payment will be charged to your iTunes Account at confirmation
                of purchase.
              </Text>
              <Text style={[styles.currentPlanText, { textAlign: "justify" }]}>
                • Subscription will automatically renew every month/year
                depending on the plan you purchased, unless you turn off
                auto-renew at least 24 hours prior to the end of your current
                subscription period.
              </Text>
              <Text
                style={[
                  styles.currentPlanText,
                  { textAlign: "justify", marginTop: 12 },
                ]}
              >
                {`• Subscriptions can be managed by the user and auto-renewal can
                be turned off by going to iTunes Account Settings. ﻿﻿You can
                restore previously purchased subscriptions on IOS by going to
                Settings > Restore.`}
              </Text>
            </View>
          </View>
        ) : null}
      </ScrollView>
      <View style={styles.payNowBtnContainer}>
        <View
          style={[
            styles.paginationDotsContainer,
            {
              marginBottom:
                selectedItemMemo?.plan_id !== isCurrentPlan[0]?.plan_id
                  ? 20
                  : 2,
            },
          ]}
        >
          {renderPaginationDots()}
        </View>
        <Animated.View entering={FadeInDown}>
          {route?.params?.isHome === "yes" || activePlanData?.plan_status === 0 ? (
            <CButton
              onBtnClick={() =>
                handleToPay(
                  selectedItemMemo?.plan_id,
                  selectedItemMemo?.final_price,
                  selectedItemMemo?.product_id
                )
              }
              loading={isLoadingMemo}
            >
              Pay now
            </CButton>
          ) : isCurrentPlan[0]?.plan_id !== selectedItemMemo?.plan_id &&
            isCurrentPlan[0]?.price < selectedItemMemo?.price && 
            activePlanData?.plan_type !== 'special_offer' && 
            activePlanData?.plan_type !== 'coupon' ? (
            <View
              style={{ flexDirection: "row", justifyContent: "space-between" }}
            >
              <View style={{ alignItems: "center", flex: 1 }}>
                <Text style={styles.currentPlanText}>Amount to Pay</Text>
                <Text
                  style={[
                    styles.currentPlanText,
                    {
                      fontSize: 18,
                      color: BaseColors.black,
                      fontFamily: FontFamily.RobotSemiBold,
                    },
                  ]}
                >
                  ₹{Number(selectedItemMemo?.final_price).toFixed(2)}
                </Text>
              </View>
              <View style={{ flex: 1 }}>
                <CButton
                  loading={isLoadingMemo}
                  onBtnClick={() =>
                    handleToPay(
                      selectedItemMemo?.plan_id,
                      selectedItemMemo?.final_price,
                      selectedItemMemo?.product_id
                    )
                  }
                  showRightIcon={true}
                >
                  Upgrade
                </CButton>
              </View>
            </View>
          ) : 
          isDescriptedPrice?.value && activePlanData?.plan_status === 1 ? (
            <TouchableOpacity
              style={styles.discountMainView}
              onPress={() => handleToSpecialOffer("best")}
              activeOpacity={0.8}
            >
              <View style={styles.textView1}>
                <View style={{ alignItems: "center" }}>
                  <Text
                    style={[styles.discountTextView, { textAlign: "center" }]}
                  >
                    {isDescriptedPrice.value}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ) : null}
          <View style={styles.bottomView}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() =>
                navigation.navigate("CMS", {
                  type: "privacy",
                })
              }
            >
              <Text style={styles.privacyPolicyTextStyle}>
                {translate("PrivacyAndPolicyText")}
                {" • "}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() =>
                navigation.navigate("CMS", {
                  type: "terms",
                })
              }
            >
              <Text style={styles.privacyPolicyTextStyle}>
                {"Terms of use"}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
        <CurrentPlanInfoModal
          visible={isCurrentPlanModal}
          setModalVisible={() => setIsCurrentPlanModal(false)}
          listData={currentActivePlan}
          activePlanData={activePlanData}
        />
      </View>
      {isModal ? (
        <VerifyModal
          visible={isModal}
          setModalVisible={(e) => setIsModal(false)}
          setIsModal={(e) => setIsModal(false)}
          type={"boost"}
          text={"Your Plan is Successfully Purchase "}
          navigation={navigation}
        />
      ) : null}
      {applyCouponModal ? (
        <VerifyModal
          visible={applyCouponModal}
          setModalVisible={(e) => setApplyCouponModal(false)}
          setIsModal={(e) => setApplyCouponModal(false)}
          type={"addOns"}
          subTitle1={`'${isApplyCouponData?.couponCode}' Applied`}
          text={`${isApplyCouponData?.discount_value}% Discount`}
          subTitle2={"with this coupon code"}
          navigation={navigation}
          data={isApplyCouponData}
        />
      ) : null}
    </SafeAreaView>
  );
};

export default PaymentPlans;
