import { BaseColors } from "@config/theme";
import { FontFamily, FontWeight } from "@config/typography";

const { StyleSheet } = require("react-native");

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  backIconView: {
    borderWidth: 1,
    padding: 6,
    borderRadius: 23,
    marginRight: 15,
  },
  headingTextTitle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 24,
    color: BaseColors.black,
    textTransform: "capitalize",
  },
  headerMainView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    margin: 10,
    paddingHorizontal: 20,
  },
  skipTextStyle: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 16,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  switchMainView: {
    borderWidth: 0.75,
    backgroundColor: BaseColors.textinputBackGroundColor,
    borderRadius: 5,
    borderColor: BaseColors.borderColor,
    marginHorizontal: 20,
  },
  switchTextStyle: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
  },
  dataComponent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 10,
    padding: 12,
  },
  switchCardView: {
    borderRadius: 24,
    overflow: "hidden",
  },
  planDuration: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 14,
    color: BaseColors.black,
    textTransform: "capitalize",
  },
  planItemMainView: {
    backgroundColor: BaseColors.textinputBackGroundColor,

    padding: 10,
    alignItems: "center",
    borderRadius: 8,
    width: 129,
    paddingVertical: 20,
  },
  planPriceTextStyle: {
    fontFamily: FontFamily.RobotSemiBold,
    fontSize: 19,
    color: BaseColors.black101,
    textAlignVertical: "center",
    textAlign: "center",
    marginTop: 9,
  },
  planTypeTextStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 14,
    color: BaseColors.black,

    textTransform: "capitalize",
  },
  clickedPositionStyle: {
    position: "absolute",
    bottom: -12,
    alignSelf: "center",
    zIndex: 1,
    overflow: "hidden",
  },
  descRenderMainView: {
    flex: 1,
    marginVertical: 10,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  descTextStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 18,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
    flexWrap: "wrap",
  },
  btnContainerStyle: {
    borderWidth: 2,
    borderColor: BaseColors.gray,
  },
  payNowBtnContainer: {
    marginHorizontal: 20,
    paddingBottom: 10,
  },
  paginationDotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 10,
  },
  paginationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  activePlanMainView: {
    borderWidth: 1,
    borderColor: BaseColors.borderColor,
    borderRadius: 8,
    marginHorizontal: 20,
    backgroundColor: BaseColors.textinputBackGroundColor,
    marginVertical: 20,
    padding: 13,
  },
  currentPlanText: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 14,
    color: BaseColors.black100,
  },
  activePlanHeaderView: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  moreInfoView: {
    borderWidth: 1,
    position: "absolute",
    borderRadius: 24,
    alignItems: "center",
    justifyContent: "center",
    borderColor: BaseColors.activeTab,
    backgroundColor: "rgba(214, 0, 46, 0.2)",
    bottom: -12,
    alignSelf: "center",
  },
  cancelSubscriptionTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,

    textDecorationLine: "underline",
  },
  discountMainView: {
    borderWidth: 1,
    marginHorizontal: 20,
    borderRadius: 8,
    backgroundColor: "#f2e2bd",
    borderColor: BaseColors.activeTab,
    overflow: "hidden",
  },
  textView: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 12,
  },
  textView1: {
    marginVertical: 12,
  },
  discountTextView: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.activeTab,
  },
  bottomView: {
    flexDirection: "row",

    alignSelf: "center",
  },
  privacyPolicyTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: "#616161",
    // position: "absolute",
    // bottom: 30,
    alignSelf: "center",
    marginTop: 12,
  },
  noteText: {
    marginHorizontal: 20,
    marginVertical: 12,
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
  },
  couponView: {
    borderWidth: 0.5,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    borderColor: BaseColors.borderColor,
    backgroundColor: BaseColors.white,
  },
  couponText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.black,
    marginBottom: 10,
    opacity: 0.8,
  },
});

export default styles;
