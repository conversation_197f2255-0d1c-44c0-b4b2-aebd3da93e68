import { Switch, Text, TouchableOpacity, View } from "react-native";
import styles from "./style";
import Animated, { FadeIn } from "react-native-reanimated";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";

export const CustomHeader = ({ title, onBtnClick = () => {} }) => {
  return (
    <View style={styles.headerMainView}>
      <TouchableOpacity
        style={styles.backIconView}
        activeOpacity={0.8}
        onPress={onBtnClick}
      >
        <CustomIcon name="back-arrow" size={18} color={BaseColors.black} />
      </TouchableOpacity>
      <View>
        <Animated.Text
          style={styles.headingTextTitle}
          entering={FadeIn.duration(400)}
        >
          {title}
        </Animated.Text>
      </View>
      <View style={{ width: "10%" }} />
      {/* <TouchableOpacity activeOpacity={0.8}>
        <Text style={styles.skipTextStyle}>Skip</Text>
      </TouchableOpacity> */}
    </View>
  );
};

export const SwitchComponent = ({
  offSwitchText,
  onSwitchText,
  onChangeSwitch = () => {},
  switchValue = false,
}) => {
  return (
    <View style={styles.switchMainView}>
      <View style={styles.dataComponent}>
        <Animated.Text
          style={[
            styles.switchTextStyle,
            {
              color: !switchValue ? BaseColors.activeTab : BaseColors.fontColor,
            },
          ]}
          entering={!switchValue ? FadeIn.duration(400) : null}
        >
          {offSwitchText}
        </Animated.Text>
        <View
          style={[
            styles.switchCardView,
            {
              backgroundColor: "#EFEFEF",
            },
          ]}
        >
          <Switch
            trackColor={{
              false: "transparent",
              true: "transparent",
            }}
            thumbColor={BaseColors.activeTab}
            ios_backgroundColor="transparent"
            onChange={onChangeSwitch}
            value={switchValue}
          />
        </View>
        <Animated.Text
          entering={switchValue ? FadeIn.duration(400) : null}
          style={[
            styles.switchTextStyle,
            {
              color: switchValue ? BaseColors.activeTab : BaseColors.fontColor,
            },
          ]}
        >
          {onSwitchText}
        </Animated.Text>
      </View>
    </View>
  );
};
