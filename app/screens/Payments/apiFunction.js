import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const getPlanList = async () => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.subscriptionPlan,
      "GET"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ getPlanList ~ error:", error);
  }
};

export const paySubscription = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.purchasePlan,
      "POST",
      data,
      false
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ paySubscription ~ error:", error);
  }
};

export const getRemainingPlanData = async () => {
  try {
    const resp = await getApiData(BaseSetting.endpoints.getUserPlan, "GET");
    return resp;
  } catch (error) {
    console.log("🚀 ~ getCurrentPlantDetail ~ error:", error);
  }
};

export const isValidCoupon = async (id) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.getCouponDetail,
      "POST",
      { coupon_code: id }
    );
    return resp;
  } catch (e) {
    console.error("🚀 ~ isValidCoupon ~ e:", e);
  }
};
