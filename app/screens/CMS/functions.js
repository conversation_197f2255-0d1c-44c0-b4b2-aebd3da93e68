import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const getCmsData = async (type, cmsData = () => {}) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=cms&type=${
        type === "terms"
          ? `terms-and-condition`
          : type === "privacy"
            ? "privacy-policy"
            : type === "about-us"
              ? "about-us"
              : null
      }`,
      "GET"
    );
    return resp?.data;
  } catch (error) {
    console.error("🚀 ~ getCmsData ~ error:", error);
  }
};
