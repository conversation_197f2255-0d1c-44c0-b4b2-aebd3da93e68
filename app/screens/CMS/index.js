import React, { memo, useCallback, useMemo, useState } from "react";
import { SafeAreaView, View } from "react-native";
import { defaultStyle } from "@app/utils/commonFunction";
import styles from "./styles";
import CHeader from "@components/CHeader";
// import Animated from "react-native-reanimated";
import { useFocusEffect } from "@react-navigation/native";
import { getCmsData } from "./functions";
import Loader from "@components/CLoaderLottie";
const { WebView } = require("react-native-webview");
const Animated = require("react-native-reanimated").default;

const defaultHead = `<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
${defaultStyle}`;
const CMS = ({ navigation, route }) => {
  const ScreenName = route.name;

  const type = route?.params?.type;
  const INJECTED_JAVASCRIPT =
    "const meta = document.createElement('meta'); meta.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=0.99, user-scalable=0'); meta.setAttribute('name', 'viewport'); document.getElementsByTagName('head')[0].appendChild(meta); ";

  const [fetchDataContent, setFetchDataContent] = useState("");
  const [cmsTitle, setCmsTitle] = useState("");
  // Memo's
  const fetchDataContentMemo = useMemo(
    () => fetchDataContent,
    [fetchDataContent]
  );
  const cmsTitleMemo = useMemo(() => cmsTitle, [cmsTitle]);
  useFocusEffect(
    useCallback(() => {
      handleToSetData();
    }, [])
  );

  const handleToSetData = async () => {
    const data = await getCmsData(
      ScreenName === "Terms&Conditions"
        ? "terms"
        : ScreenName === "PrivacyPolicy"
          ? "privacy"
          : ScreenName === "AboutUs"
            ? "about-us"
            : `${type}`
    );
    if (data) {
      setFetchDataContent(data?.data[0]?.content);
      setCmsTitle(data?.data[0]?.title);
    }
  };
  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle={ScreenName === "FAQ" ? "FAQ" : cmsTitleMemo}
      />
      <Animated.View style={{ flex: 1, marginHorizontal: 20 }}>
        {ScreenName === "FAQ" ? (
          <View
            style={{
              flex: 1,
              paddingHorizontal: 14,
            }}
          >
            <Loader />
          </View>
        ) : (
          <WebView
            source={{ html: `${defaultHead}${fetchDataContentMemo}` }}
            style={{ flex: 1 }}
            startInLoadingState
            setBuiltInZoomControls={false}
            scrollEnabled
            scalesPageToFit={false}
            injectedJavaScript={INJECTED_JAVASCRIPT}
            onLoad={() => {}}
          />
        )}
      </Animated.View>
    </SafeAreaView>
  );
};
export default memo(CMS);
