import React, { memo, useCallback, useState } from "react";
import { Safe<PERSON>reaView, ScrollView, View } from "react-native";
import styles from "./styles";
import CHeader from "@components/CHeader";
import Collapse from "@components/Collapse/Collapse";
import BaseSetting from "@config/setting";
import { getApiData } from "@app/utils/apiHelper";
import { useFocusEffect } from "@react-navigation/native";
import Toast from "react-native-simple-toast";
import { Controller, useForm } from "react-hook-form";
import CInput from "@components/TextInput";
import { yupResolver } from "@hookform/resolvers/yup";
// import Animated, { FadeInDown } from "react-native-reanimated";
import { translate } from "../../lang/Translate";
import isEmpty from "lodash-es/isEmpty";
import MiniLoader from "@components/MiniLoader";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

const FAQ = ({ navigation }) => {
  // state
  const [loading, setIsLoading] = useState(false);
  const [FAQList, setFAQList] = useState([]);

  const { control } = useForm({
    resolver: yupResolver({}),
  });

  // fetchFAQData function
  const fetchFAQData = async (searchData = "") => {
    if (isEmpty(searchData)) {
      setIsLoading(true);
    }
    try {
      const resp = await getApiData(
        isEmpty(searchData)
          ? `${BaseSetting.endpoints.commonData}?slug=faqs`
          : `${BaseSetting.endpoints.commonData}?slug=faqs&search=${searchData}`,
        "GET"
      );

      if (resp?.data?.success) {
        setFAQList(resp?.data?.data);
        setIsLoading(false);
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
      Toast.show(resp?.data?.message);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchFAQData();
    }, [])
  );

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="FAQ"
      />
      <View style={{ paddingHorizontal: 20 }}>
        <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
          <Controller
            control={control}
            rules={{
              required: true,
            }}
            name="search"
            render={({ field: { onChange, value } }) => (
              <CInput
                placeholderText={translate("Search")}
                rightIconSize={18}
                returnKeyType="done"
                value={value}
                leftSideIcon="Search-1"
                leftSideIconSize={22}
                onChange={(e) => {
                  onChange(e);
                  fetchFAQData(e);
                }}
                containerStyle={{ borderRadius: 10 }}
              />
            )}
          />
        </Animated.View>
      </View>
      {loading ? (
        <View
          style={{
            flex: 1,
            paddingHorizontal: 14,
          }}
        >
          <MiniLoader />
        </View>
      ) : (
        <ScrollView
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.CollapseView}>
            <Collapse data={FAQList} />
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};
export default memo(FAQ);
