import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { navigationRef } from "@navigation/NavigationService";
import Toast from "react-native-simple-toast";

export const onSubmit = async (data, isSignUpType, otp, type, navigation) => {
  const finalData = {
    type: data?.singUpType,
  };
  if (type === "verify") {
    finalData["otp"] = otp;
  }
  if (type !== "verify") {
    finalData["key"] = true;
  }
  if (data?.singUpType === "phone") {
    (finalData["phone_code"] = data?.countryCode),
      (finalData["phone_number"] = data?.text);
  } else {
    finalData["email"] = data?.text;
  }
  try {
    const resp = await getApiData(
      type === "verify"
        ? BaseSetting.endpoints.verifyOtp
        : BaseSetting.endpoints.resendOtp,
      "POST",
      finalData,
      true,
      false
    );
    const respData = { resp: resp, finalData: finalData };
    return respData;
  } catch (error) {
    console.error("🚀 ~ onSubmit ~ error:", error);
  }
};
