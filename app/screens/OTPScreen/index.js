import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Image,
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./styles";
import { images } from "@config/images";
import CButton from "@components/CButton";
import OTPTextView from "react-native-otp-textinput";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import { CustomIcon } from "@config/LoadIcons";
import isEmpty from "lodash-es/isEmpty";
import { onSubmit } from "./apiFunctions";
import { BaseColors } from "@config/theme";
import Toast from "react-native-simple-toast";
import VerifyModal from "@components/VerifyModal";

const SkipComponent = ({ data, isSignUpType, navigation }) => {
  // States's
  const [seconds, setSeconds] = useState(60);
  const [timer, setTimer] = useState(true);

  // Memo's
  const secondsMemo = useMemo(() => seconds, [seconds]);
  const timerMemo = useMemo(() => timer, [timer]);

  useEffect(() => {
    let interval;
    if (secondsMemo > 0) {
      interval = setInterval(() => {
        setSeconds((prevSeconds) => prevSeconds - 1);
      }, 1000);
    } else {
      clearInterval(interval);
    }
    if (secondsMemo <= 0) {
      setTimer(false);
    } else {
      setTimer(true);
    }

    return () => clearInterval(interval);
  }, [secondsMemo, timerMemo]);
  const resendOtpData = async () => {
    // setSeconds(55);
    const data1 = await onSubmit(
      data,
      isSignUpType,
      null,
      "resend",
      navigation
    );

    if (data1?.resp?.data?.success) {
      setSeconds(60);
    } else {
      Toast.show(data1?.resp?.data?.message);
    }
  };
  return (
    <View style={styles.resendOtpTimerView}>
      {timerMemo ? (
        <View>
          <Text style={styles.backToLoginText}>
            00:{secondsMemo < 10 ? "0" : ""}
            {secondsMemo}
          </Text>
        </View>
      ) : (
        <TouchableOpacity
          onPress={() => {
            !timerMemo ? resendOtpData() : null;
          }}
          activeOpacity={0.8}
          style={styles.resendOTPView}
        >
          <CustomIcon name="resend" size={20} color={"#057FFF"} />
          <Text style={styles.backToLoginText}>{translate("ResendText")}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const ForgotOtpScreen = ({ navigation, route }) => {
  const type = route?.params?.loginType;
  const data = route?.params?.data?.data;

  const isSignUpType = route?.params?.signUpType;

  // Ref
  const otpTextInputRef = useRef(null);
  // State's
  const [otp, setOtp] = useState();
  const [isError, setsError] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [isModal, setIsModal] = useState(false);
  // Memo's
  const otpMemo = useMemo(() => otp, [otp]);
  const isErrorMemo = useMemo(() => isError, [isError]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const isModalMemo = useMemo(() => isModal, [isModal]);

  // useEffect(() => {
  //   // if (data?.singUpType === "phone" || data?.singUpType === "email") {
  //   //   setOtp("1234");
  //   // }
  // }, [data]);

  const handleToSubmit = useCallback(async () => {
    setIsLoading(true);
    const respData = await onSubmit(data, isSignUpType, otpMemo, "verify");
    if (respData?.resp?.data?.success) {
      if (isSignUpType === "yes") {
        setIsModal(true);
        setIsLoading(false);
      } else {
        navigation.navigate("CNewPassWordAfterForgot", {
          data: respData?.finalData,
        });
        setIsLoading(false);
      }
      setIsLoading(false);
      setIsLoading(false);
    } else {
      Toast.show(respData?.resp?.data?.message);
      setIsLoading(false);
      setIsLoading(false);
    }
    setIsLoading(false);
  }, [isLoadingMemo, otpMemo]);

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader handleBackButton={() => navigation.goBack()} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : null}
        style={styles.KeyboardAvoidingViewStyle}
      >
        <ScrollView>
          <Image
            source={images.footMainLogo}
            style={styles.mainFootLogo}
            resizeMode={"contain"}
          />
          <View style={styles.contentView}>
            <Text style={styles.headingText}>
              {isSignUpType !== "yes"
                ? `${translate("forgotPasswordText")}?`
                : translate("OtpVerification")}
            </Text>

            <View>
              <Text style={styles.descText}>
                {isSignUpType !== "yes"
                  ? translate("OtpText")
                  : translate("enterOtpText")}
              </Text>
              <View style={styles.emailView}>
                <Text style={styles.emailText}>
                  {data?.singUpType === "phone"
                    ? `+${data?.countryCode} ${data?.text}`
                    : data?.text}
                </Text>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => navigation.goBack()}
                >
                  <CustomIcon
                    name="edit-circle"
                    size={18}
                    color={BaseColors.gray2}
                  />
                </TouchableOpacity>
              </View>
            </View>

            <OTPTextView
              containerStyle={styles.textInputContainer}
              textInputStyle={[
                styles.roundedTextInput,
                {
                  backgroundColor: BaseColors.white,
                  color: isErrorMemo ? "#D6002E" : "#000000",
                  borderColor: isErrorMemo ? "#D6002E" : "#000000",
                },
              ]}
              // tintColor={"#000000"}
              tintColor={
                otpMemo ? (isErrorMemo ? "#D6002E" : "#000000") : "#000000"
              }
              // offTintColor={"#000000"}
              offTintColor={
                otpMemo ? (isErrorMemo ? "#D6002E" : "#000000") : "#000000"
              }
              defaultValue={
                data?.singUpType === "phone" || data?.singUpType === "email"
                  ? ""
                  : ""
              }
              inputCount={4}
              inputCellLength={1}
              autoFocus={false}
              handleTextChange={setOtp}
              handleCellTextChange={() => {
                setsError("");
              }}
              ref={otpTextInputRef}
            />
            {isErrorMemo ? (
              <View style={styles.errorMsgMainView}>
                <CustomIcon
                  name={"BsExclamationCircle"}
                  size={18}
                  color={"#D6002E"}
                />
                <Text style={styles.errorTxt}>{isErrorMemo}</Text>
              </View>
            ) : null}
            <CButton
              style={styles.btnStyle}
              loading={isLoadingMemo}
              onBtnClick={() => {
                Keyboard.dismiss();
                if (isEmpty(otpMemo)) {
                  setsError("Please enter OTP");
                  otpTextInputRef.current.clear();
                } else {
                  otpMemo.length === 4
                    ? handleToSubmit()
                    : setsError("Please full fill OTP");
                }
              }}
            >
              {translate("submitText")}
            </CButton>
            <SkipComponent
              data={data}
              isSignUpType={isSignUpType}
              navigation={navigation}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      {isModalMemo ? (
        <VerifyModal
          visible={isModalMemo}
          navigation={navigation}
          data={data}
          type={"signUp"}
        />
      ) : null}
    </SafeAreaView>
  );
};

export default memo(ForgotOtpScreen);
