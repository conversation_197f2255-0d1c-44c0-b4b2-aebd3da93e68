import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Platform, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainFootLogo: {
    height: 120,
    width: 120,
    marginTop: Platform.OS === "ios" ? 0 : 0,
    alignSelf: "center",
  },
  headingText: {
    fontSize: 34,
    fontFamily: FontFamily.semibold,
    color: "#343434",
    textAlign: "center",
  },
  contentView: {
    marginHorizontal: 25,
    marginTop: 49,
  },
  descText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#000000",
    marginTop: 15,
    textAlign: "center",
  },
  cInputStyle: {
    marginTop: 25,
  },
  btnStyle: {
    marginTop: 45,
  },
  emailView: {
    flexDirection: "row",
    gap: 4,
    alignItems: "center",
    justifyContent: "center",
  },
  emailText: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: "#424242",
    textAlign: "center",
  },
  textInputContainer: {
    marginTop: 40,
  },
  roundedTextInput: {
    height: 50,
    width: 50,
    borderTopWidth: 0.66,
    borderBottomWidth: 0.86,
    borderLeftWidth: 0.86,
    borderRightWidth: 0.86,
    borderRadius: 5,
    fontSize: 23,
    borderColor: "#8E8383",
    backgroundColor: BaseColors.White,
  },
  resendOtpTimerView: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 9,
  },
  backToLoginText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: "#057FFF",
  },
  helloImg: {
    height: 30,
    width: 30,
  },
  helloImagAndText: {
    flexDirection: "row",
    marginTop: 15,
    justifyContent: "center",
  },
  inputFields: {
    marginBottom: 15,
  },
  descText1: {
    fontSize: 24,
    fontFamily: FontFamily.regular,
    color: "#343434",
    marginBottom: 5,
    textAlign: "center",
  },
  errorTxt: {
    color: "#D6002E",
    textAlign: "left",
  },
  errorMsgMainView: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 8,
    gap: 8,
  },
  resendOTPView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  KeyboardAvoidingViewStyle: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
});
export default styles;
