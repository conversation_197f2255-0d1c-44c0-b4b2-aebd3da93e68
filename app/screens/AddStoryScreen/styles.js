import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  mainView: {
    backgroundColor: BaseColors.white,
    flex: 1,
  },
  container:{
    flex: 1,
  },
  headerMainView: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 18,
  },
  cancelTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  headingTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: "#322F2F",
  },

  mainButton: {
    position: "absolute",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
    bottom: 0,
    height: "10%",
  },
});

export default styles;
