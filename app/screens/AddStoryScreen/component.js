import { Text, TouchableOpacity, View } from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";

export const Header = ({ navigation, onPressCamera = () => {} }) => {
  return (
    <View style={styles.headerMainView}>
      <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()}>
        <Text style={styles.cancelTextStyle}>{translate("cancelText")}</Text>
      </TouchableOpacity>
      <View>
        <Text style={styles.headingTextStyle}>{translate("addStoryText")}</Text>
      </View>
      <TouchableOpacity activeOpacity={0.8} onPress={onPressCamera}>
        <CustomIcon name="Camera" size={23} color={BaseColors.fontColor} />
      </TouchableOpacity>
    </View>
  );
};
