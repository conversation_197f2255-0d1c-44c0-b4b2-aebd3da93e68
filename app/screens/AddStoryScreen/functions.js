import {
  PERMISSIONS,
  RESULTS,
  check,
  request,
  requestMultiple,
} from "react-native-permissions";

const { Platform, PermissionsAndroid } = require("react-native");

export async function hasPermission() {
  if (Platform.OS === "android") {
    return await hasAndroidPermission();
  } else if (Platform.OS === "ios") {
    return await hasIOSPermission();
  }
}

export async function hasAndroidPermission() {
  const getCheckPermissionPromise = async () => {
    if (Platform.Version >= 33 && Platform.OS === "android") {
      const [
        hasReadMediaImagesPermission,
        hasReadMediaVideoPermission,
        hasCameraPermission,
        hasAudioPermission,
      ] = await Promise.all([
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
        ),
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO
        ),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO),
      ]);
      return (
        hasReadMediaImagesPermission &&
        hasReadMediaVideoPermission &&
        hasCameraPermission &&
        hasAudioPermission
      );
    } else {
      const [
        hasReadExternalStoragePermission,
        hasCameraPermission,
        hasAudioPermission,
      ] = await Promise.all([
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
        ),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO),
      ]);
      return (
        hasReadExternalStoragePermission &&
        hasCameraPermission &&
        hasAudioPermission
      );
    }
  };

  const hasPermission = await getCheckPermissionPromise();
  if (hasPermission) {
    return true;
  }

  return await getRequestPermissionPromise();
}

const getRequestPermissionPromise = () => {
  if (Platform.OS === "ios") {
    return new Promise((resolve, reject) => {
      Promise.all([
        check(PERMISSIONS.IOS.PHOTO_LIBRARY),
        check(PERMISSIONS.IOS.CAMERA),
        check(PERMISSIONS.IOS.MICROPHONE),
      ])
        .then(([photoLibraryStatus, cameraStatus, microphoneStatus]) => {
          if (
            photoLibraryStatus === RESULTS.GRANTED &&
            cameraStatus === RESULTS.GRANTED &&
            microphoneStatus === RESULTS.GRANTED
          ) {
            resolve(true);
          } else {
            requestMultiplePermissions().then(resolve).catch(reject);
          }
        })
        .catch(reject);
    });
  } else if (Platform.OS === "android") {
    return requestMultiplePermissions();
  }
};

const requestMultiplePermissions = () => {
  if (Platform.Version >= 33) {
    return PermissionsAndroid.requestMultiple([
      PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
      PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
      PermissionsAndroid.PERMISSIONS.CAMERA,
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    ]).then(
      (statuses) =>
        statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] ===
          PermissionsAndroid.RESULTS.GRANTED &&
        statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] ===
          PermissionsAndroid.RESULTS.GRANTED &&
        statuses[PermissionsAndroid.PERMISSIONS.CAMERA] ===
          PermissionsAndroid.RESULTS.GRANTED &&
        statuses[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] ===
          PermissionsAndroid.RESULTS.GRANTED
    );
  } else {
    return PermissionsAndroid.requestMultiple([
      PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
      PermissionsAndroid.PERMISSIONS.CAMERA,
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    ]).then(
      (statuses) =>
        statuses[PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE] ===
          PermissionsAndroid.RESULTS.GRANTED &&
        statuses[PermissionsAndroid.PERMISSIONS.CAMERA] ===
          PermissionsAndroid.RESULTS.GRANTED &&
        statuses[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] ===
          PermissionsAndroid.RESULTS.GRANTED
    );
  }
};

async function hasIOSPermission() {
  const permissionStatus = await requestMultiple([
    PERMISSIONS.IOS.PHOTO_LIBRARY,
    PERMISSIONS.IOS.CAMERA,
    PERMISSIONS.IOS.MICROPHONE,
  ]);
  return (
    permissionStatus[PERMISSIONS.IOS.PHOTO_LIBRARY] === RESULTS.GRANTED &&
    permissionStatus[PERMISSIONS.IOS.CAMERA] === RESULTS.GRANTED &&
    permissionStatus[PERMISSIONS.IOS.MICROPHONE] === RESULTS.GRANTED
  );
}
