import React, { memo, useCallback, useMemo, useState } from "react";
import {
  Dimensions,
  Image,
  Platform,
  SafeAreaView,
  TouchableOpacity,
  View,
} from "react-native";
// import Animated, { FadeIn } from "react-native-reanimated";
import Toast from "react-native-simple-toast";
import isEmpty from "lodash-es/isEmpty";
import CButton from "@components/CButton";
import CImagePicker from "@components/CImagePicker";
import { Header } from "./component";
import { translate } from "../../lang/Translate";
import styles from "./styles";
import {
  checkImgSize,
  checkVideoSize,
  cropImage,
  trimVideo,
  uploadFile,
} from "@app/utils/commonFunction";
import Loader from "@components/Loader";
// import Video from "react-native-video";
import CCamera from "@components/CameraButton/CCamera";
import { useSelector } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { hasPermission } from "./functions";
import { isArray } from "lodash-es";
import { getStatusBarHeight, isIPhoneX } from "react-native-status-bar-height";

const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;
const Video = require("react-native-video").default;
const RNConvertPhAsset = require("react-native-convert-ph-asset").default;

// Dimension height
const { height, width } = Dimensions.get("window");

const AddStoryScreen = ({ navigation }) => {
  // Redux Variable
  const { userStoryList, adminSettingData } = useSelector((auth) => auth.auth);

  // State's
  const [selectedImage, setSelectedImage] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [imagePath, setImagePath] = useState(null);
  const [videoPath, setVideo] = useState(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [storyCountIs, setIsStoryCount] = useState(0);
  const [adminStoryCount, setAdminStoryCount] = useState(5);
  const [productType, setIsProductType] = useState("");
  const [defaultImageSize, setDefaultImageSize] = useState();
  const [defaultVideoSize, setDefaultVideoSize] = useState();

  // Memo's
  const selectedImageMemo = useMemo(() => selectedImage, [selectedImage]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const storyCountIsMemo = useMemo(() => storyCountIs, [storyCountIs]);
  const adminStoryCountMemo = useMemo(() => adminStoryCount, [adminStoryCount]);
  const productTypeMemo = useMemo(() => productType, [productType]);
  const defaultImageSizeMemo = useMemo(
    () => defaultImageSize,
    [defaultImageSize]
  );
  const defaultVideoSizeMemo = useMemo(
    () => defaultVideoSize,
    [defaultVideoSize]
  );

  const getListCount = () => {
    if (!isEmpty(userStoryList?.data)) {
      const selfAvailableObject = userStoryList?.data.find(
        (obj) => obj.self === true
      );

      const imageCountObject = adminSettingData?.find(
        (obj) => obj?.slug === "STORYCOUNT"
      );

      const imageSize = adminSettingData?.find(
        (obj) => obj?.slug === "IMAGESIZE"
      );

      const videoSize = adminSettingData.find(
        (obj) => obj.slug === "VIDEOSIZE"
      );

      setIsStoryCount(selfAvailableObject?.story_count || 0);

      setAdminStoryCount(imageCountObject?.value);
      setDefaultImageSize(imageSize?.value);
      setDefaultVideoSize(videoSize?.value);
    }
  };

  useFocusEffect(
    useCallback(() => {
      getPermission();
      getListCount();
    }, [userStoryList])
  );
  const getPermission = async () => {
    await hasPermission();
  };
  // Api Function
  const handleToNavigation = useCallback(async () => {
    let fileUrl = imagePath
      ? imagePath
      : videoPath
        ? videoPath
        : selectedImageMemo;
    if (productTypeMemo === "image") {
      const updateImage = await cropImage(
        fileUrl?.uri,
        width - 40,
        height / 1.7
      );
      const temp = {
        fileCopyUri: null,
        name: fileUrl?.name,
        size: updateImage?.size,
        type: updateImage?.mime,
        uri: updateImage?.path,
      };
      fileUrl = temp;
    }

    let valid = {};

    const startTime = "00:00:01"; // Start time in HH:MM:SS format
    const endTime = "00:01:00"; // Duration in HH:MM:SS format

    const data =
      productTypeMemo !== "image" && fileUrl?.durationInSec > 60
        ? await trimVideo(
            fileUrl?.uri,
            fileUrl?.name,
            startTime,
            endTime,
            fileUrl
          )
        : fileUrl;

    if (productTypeMemo === "image") {
      valid = checkImgSize(defaultImageSizeMemo || 3, data);
    } else {
      1024 * 1024 * Number(defaultVideoSizeMemo);
      valid = checkVideoSize(defaultVideoSizeMemo || 20, data);
    }

    if (valid?.isValid) {
      if (Number(storyCountIsMemo) < Number(adminStoryCountMemo)) {
        setIsLoading(true);
        const resp = await uploadFile(data, "story");
        if (resp?.data?.data?.success && resp?.data) {
          setIsCameraOpen(false);
          setImagePath(null);
          setVideo(null);
          setIsLoading(false);
          navigation.navigate("StoryPreview", {
            image:
            productTypeMemo === "image" ? resp?.data?.data : resp?.data?.data,
            video_duration: productTypeMemo !== "image" ? data : null,
            sendedFile: fileUrl,
          });
        } else {
          if(resp?.data?.message === 'Network error'){
            handleToNavigation();
          }else{
            setIsLoading(false);
            Toast.show(resp?.data?.message || "Please check API response");
          }
        }
        setIsLoading(false);
      } else {
        Toast.show("You reach the upload limit");
        setIsLoading(false);
      }
    } else {
      Toast.show(valid?.errMsg);
      setIsLoading(false);
    }
    setIsLoading(false);
  }, [isLoadingMemo, selectedImageMemo, imagePath, videoPath]);

  // click camera button
  const handleCamera = () => {
    setIsCameraOpen(true);
    setImagePath(null);
    setVideo(null);
  };

  const setIsSelectFile = useCallback(async (selectedVideoMemo) => {
    if (
      selectedVideoMemo[0]?.uri &&
      selectedVideoMemo[0]?.uri.includes("ph://")
    ) {
      const tempArr = [...selectedVideoMemo];
      RNConvertPhAsset.convertVideoFromUrl({
        url: selectedVideoMemo[0]?.uri,
        convertTo: "mov",
        quality: "original",
      })
        .then((response) => {
          tempArr[0].uri = response?.path;
          tempArr[0].name = response?.filename;
          tempArr[0].type = "video/mov";
          tempArr[0].size = selectedVideoMemo[0]?.size;

          setSelectedImage(tempArr);
          const data = tempArr[0]?.type?.split("/");
          setIsProductType(data[0]);
        })
        .catch(() => {
          return e;
        });
    } else {
      setSelectedImage(selectedVideoMemo[0]);
      const data = selectedVideoMemo[0]?.type?.split("/");
      setIsProductType(selectedVideoMemo[0]);
    }
  }, []);
  return (
    <View style={{flex: 1, marginTop: isCameraOpen && isIPhoneX() ? getStatusBarHeight() : 0}}>

    {isCameraOpen? null :
    (<SafeAreaView style={styles.mainView}>
      <Loader loading={isLoadingMemo} />
      {/* For the header */}
      {isCameraOpen ? null : (
        <Header navigation={navigation} onPressCamera={() => handleCamera()} />
      )}

      {/* camera clicked Image showing*/}
      {imagePath?.uri && (
        <TouchableOpacity activeOpacity={0.8}>
          <Image
            source={{ uri: `file://${imagePath?.uri}` }}
            style={{ width: "100%", height: "92%" }}
          />
        </TouchableOpacity>
      )}

      {/* camera clicked Video showing*/}
      {videoPath?.uri ? (
        <View style={styles.container}>
          <Video
            source={{
              uri:
                Platform.OS === "android"
                  ? `file://${videoPath?.uri}`
                  : videoPath?.uri,
            }}
            style={{ width: "100%", height: "100%" }}
            controls={false}
            repeat
            onError={() => Toast.show("Failed to load video")}
          />
        </View>
      ) : null}
      {/* Camera open component */}
      {/* <CCamera
        setImagePath={setImagePath}
        setVideo={setVideo}
        videoDuration={30}
        isCameraOpen={isCameraOpen}
        setIsCameraOpen={setIsCameraOpen}
      /> */}

      {/* Open Image Picker */}
      {/* if the camera is open then this  CImagePicker not show */}
      {isCameraOpen || videoPath?.uri || imagePath?.uri ? null : (
        <View style={{
          marginBottom: !isEmpty(selectedImage) ? 140 : 60,
        }}>
          <CImagePicker
            onImageSelect={(e) => {
              const data = e[0]?.type?.split("/");

              if (data[0] === "video") {
                setIsSelectFile(e);
              } else {
                setSelectedImage(e[0]);
                setIsProductType(data[0]);
              }
            }}
          />
        </View>
      )}

      {(!isEmpty(selectedImage) && !isCameraOpen) ||
      imagePath?.uri ||
      videoPath?.uri ? (
        <Animated.View style={styles.mainButton} entering={FadeIn}>
          <CButton
            style={{ width: "90%" }}
            onBtnClick={() => handleToNavigation()}
          >
            {translate("nextText")}
          </CButton>
        </Animated.View>
      ) : null}
    </SafeAreaView>)}

    <CCamera
        setImagePath={setImagePath}
        setVideo={setVideo}
        videoDuration={30}
        isCameraOpen={isCameraOpen}
        setIsCameraOpen={setIsCameraOpen}
      />
    </View>
  );
};

export default memo(AddStoryScreen);
