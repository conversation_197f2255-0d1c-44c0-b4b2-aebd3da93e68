import React, { useMemo } from "react";
import {
  Dimensions,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import FastImage from "react-native-fast-image";
import dayjs from "dayjs";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import { BarChart } from "react-native-chart-kit";
import CDropdown from "@components/CDropDown";
import styles from "./styles";
import { FontFamily } from "@config/typography";

// For The Post Photo and Description Component
export const BoostedPostHeader = ({ data }) => {
  // Rendering CratedAt time With Memo
  const createAt = useMemo(() => {
    if (data?.createdAt) {
      const formattedDate = dayjs(data.createdAt).format("DD-MMMM").split("-");
      return {
        day: formattedDate[0],
        month: formattedDate[1],
      };
    }
    return { day: 0, month: "" };
  }, [data?.createdAt]);

  return (
    <View style={styles.headerMainView}>
      {/* For Post Or Reel Image */}
      <View style={styles.boostedHeaderImageMainView}>
        <FastImage
          source={{
            uri:
              data?.type === "post"
                ? data?.ImageData[0]?.fileUrl
                : data?.ReelData?.thumbnailData?.thumbUrl,
          }}
          style={styles.boostedHeaderImageView}
          resizeMode="cover"
        />

        {/* Rendering Created At Time */}
        <View style={styles.createdAtTimeView}>
          <Text style={styles.createdAtTimeText}>
            {createAt.day}
            {"\n"}
            {createAt.month}
          </Text>
        </View>
      </View>
      {/* Rendering Post Or Reel Description Text  */}
      <View>
        <Text style={styles.headerDescTextStyle}>{data?.description}</Text>
      </View>
    </View>
  );
};

// For The Separator View
export const Separator = () => {
  return <View style={styles.SeparatorView} />;
};

// For the Post Reach Data and it's Graphical View
export const PostReachComponent = ({
  dataArr = {},
  handleToPressIcon = () => {},
}) => {
  return (
    <View>
      {/* Heading View */}
      <View style={styles.headingViewForReach}>
        <Text style={styles.headingTextView}>Post Reach</Text>
        <TouchableOpacity onPress={handleToPressIcon} activeOpacity={0.9}>
          <CustomIcon
            name="BsExclamationCircle"
            size={20}
            color={BaseColors.gray6}
          />
        </TouchableOpacity>
      </View>
      {/* Rendering Graphical View Data */}
      <View style={{ marginVertical: 35, height: 190 }}>
        {dataArr !== undefined ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            bounces={false}
          >
            <BarChart
              data={dataArr}
              width={
                dataArr.datasets[0].data?.length > 7
                  ? Dimensions.get("window").width *
                    (dataArr.datasets[0].data?.length / 7)
                  : Dimensions.get("window").width - 40
              } // from react-native
              height={200}
              showValuesOnTopOfBar={true}
              fromZero={true}
              chartConfig={{
                backgroundColor: BaseColors.black,
                backgroundColor: BaseColors.activeTab,
                fillShadowGradientFromOpacity: 1,
                fillShadowGradientToOpacity: 1,
                fillShadowGradientToOffset: 1,
                useShadowColorFromDataset: false,
                barPercentage: 1,
                barRadius: 8,
                useShadowColorFromDataset: false,
                backgroundGradientFrom: BaseColors.white,
                strokeWidth: 1,
                backgroundGradientTo: BaseColors.white,
                decimalPlaces: 0, // optional, defaults to 2dp
                color: (opacity = 0) => BaseColors.activeTab,
                labelColor: (opacity = 1) => BaseColors.black,
                propsForVerticalLabels: {
                  fontSize: 10,
                  fontFamily: FontFamily.RobotSemiBold,
                },
              }}
              withHorizontalLabels={false}
              showBarTops={false}
              showValuesOnTopOfBars={true}
              bezier
              withInnerLines={false}
              style={{
                paddingRight: -12,
              }}
            />
          </ScrollView>
        ) : null}
      </View>
    </View>
  );
};

// For Render Icon With Label Component
const IconWithLabel = React.memo(({ iconName, text }) => {
  return (
    <View style={styles.IconWithLabelMainView}>
      <CustomIcon name={iconName} size={20} color={BaseColors.black} />
      <Text style={styles.textValueStyle}>{text}</Text>
    </View>
  );
});

// For Rendering Social Media Icon With It's Value
export const RenderSocialIcon = ({ dataArr = [] }) => {
  return (
    <View style={styles.socialIconMainView}>
      {dataArr.map((item, index) => {
        return <IconWithLabel iconName={item?.iconName} text={item?.value} />;
      })}
    </View>
  );
};
// For Render Data with Value
const RenderDataWithValue = React.memo(({ titleText, value }) => {
  return (
    <View style={styles.RenderDataWithValueMainView}>
      <Text style={styles.renderTextStyle}>{titleText}</Text>
      <Text style={styles.renderTextStyle}>{value}</Text>
    </View>
  );
});

// For Rendering Content Data
export const ContentData = ({
  mainTitle,
  iconName,
  dataArr = [],
  isDropdown,
  dropDownData = [],
  value,
  placeHolder = "",
  selectedValue = () => {},
  handleToPressIcon = () => {},
}) => {
  return (
    <View>
      {/* Header */}
      <View style={styles.contentDataHeadingView}>
        {mainTitle && (
          <Text style={styles.mainTitleTextStyle}>{mainTitle}</Text>
        )}
        {iconName && (
          <TouchableOpacity onPress={handleToPressIcon} activeOpacity={0.9}>
            <CustomIcon name={iconName} size={20} color={BaseColors.gray6} />
          </TouchableOpacity>
        )}
      </View>
      {/* Show Dropdown */}
      {isDropdown && (
        <CDropdown
          labelplaceholder={placeHolder}
          data={dropDownData}
          setItem={(e) => selectedValue(e)}
          value={value}
        />
      )}
      {/* List Out Data With Sending in props */}
      {dataArr.length > 0 &&
        dataArr.map((item, index) => (
          <RenderDataWithValue
            key={index}
            titleText={item?.text}
            value={item?.value}
          />
        ))}
    </View>
  );
};
