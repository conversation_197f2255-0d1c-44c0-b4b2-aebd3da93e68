import { FontFamily } from "@config/typography";

const { BaseColors } = require("@config/theme");
const { StyleSheet, Dimensions } = require("react-native");

const screenWidth = Dimensions.get("window").width;
const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  scrollViewMain: {
    flex: 1,
  },
  boostedHeaderImageView: {
    height: 158,
    width: 158,
    overflow: "hidden",
    borderRadius: 5,
  },
  secondMainView: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  boostedHeaderImageMainView: {
    height: 158,
    width: 158,
    borderRadius: 5,
    elevation: 12,
  },
  createdAtTimeView: {
    position: "absolute",
    bottom: 6,
    right: 8,
    alignItems: "center",
    borderRadius: 8,
    backgroundColor: BaseColors.white,
    padding: 6,
  },
  createdAtTimeText: {
    fontSize: 10,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
    textAlign: "center",
  },
  headerMainView: {
    flexDirection: "row",
    gap: 8,
  },
  headerDescTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.black100,
    maxWidth: "70%",
  },
  SeparatorView: {
    borderWidth: 5,
    marginVertical: 20,
    borderColor: BaseColors.textinputBackGroundColor,
    backgroundColor: BaseColors.textinputBackGroundColor,
  },
  headingTextView: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  headingViewForReach: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  container: {
    marginTop: 50,
    alignItems: "center",
  },
  chart: {
    height: 200,
    width: screenWidth - 40,
  },
  textValueStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.black100,
    textAlign: "center",
    marginTop: 12,
  },
  IconWithLabelMainView: {
    justifyContent: "center",
    alignItems: "center",
  },
  socialIconMainView: {
    marginHorizontal: 15,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  RenderDataWithValueMainView: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 12,
  },
  renderTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.placeHolderColor,
  },
  mainTitleTextStyle: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  contentDataHeadingView: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
});

export default styles;
