import React, { memo, useCallback, useMemo, useState } from "react"; // Importing React and specific hooks
import { View, SafeAreaView, ScrollView } from "react-native"; // Importing necessary components from React Native
import { useFocusEffect } from "@react-navigation/native"; // Focus Hooks
import { useSelector } from "react-redux"; // For Fetch Redux Data
import Toast from "react-native-simple-toast"; // For Rendering Toast message
import CHeader from "@components/CHeader"; // Importing a custom header component
import MiniLoader from "@components/MiniLoader"; // For Mini loader
import DetailViewModal from "@components/DetailViewModal"; // For Detail Desc Modal
import {
  BoostedPostHeader,
  ContentData,
  PostReachComponent,
  RenderSocialIcon,
  Separator,
} from "./component"; // Importing various custom components
import styles from "./styles"; // Importing styles
import { getData } from "./apiFunciton"; // Api Call Function

// For Convert simple array To graph data
const convertArrToDataSet = (reachDat) => {
  const filteredArr = reachDat.filter((item) => item.date !== null);

  const labels = filteredArr.map((item) => item.date);
  const datasets = [{ data: filteredArr.map((item) => item.reach_count || 0) }];

  return { labels, datasets };
};

const BoostedPostInsights = ({ navigation, route }) => {
  // Extracting the type and data from the route parameters
  const { data } = route?.params || {};
  const { type, boost_post_id: boostId } = data || {};

  // Redux Data
  const { adminSettingData } = useSelector((s) => s.auth);

  // State's
  const [analyticsData, setAnalyticsData] = useState({}); // For Set  analyticsData
  const [dropdownData, setDropdownData] = useState(); // For Set Dropdown Data
  const [isLoader, setIsLoader] = useState(false); // For Set Loader
  const [reelInteraction, setReelInteraction] = useState([]); // For Set Reel Interaction Data
  const [desModal, setDescModal] = useState(false); // For Set Des Modal
  const [descData, setDescData] = useState([]); // For Set Desc Data

  // Memo's
  const analyticsDataMemo = useMemo(() => analyticsData, [analyticsData]);
  const dropdownDataMemo = useMemo(() => dropdownData, [dropdownData]);
  const isLoaderMemo = useMemo(() => isLoader, [isLoader]);

  // For Get Boost Post Data Function which fetch data using API Call and set array format
  const getBoostData = useCallback(async () => {
    setIsLoader(true); // Set Loader True For Displaying Loader in Screen
    const resp = await getData(boostId); // Api Call For Fetch data

    // If response get Success and in response get data then set data
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      const reachDat = resp?.data?.data?.reach;

      // Set Data in Object of Array Format
      const data = await {
        socialDataArr: [
          {
            id: 1,
            name: "reel",
            iconName: "BsPlayCircleFill",
            value: resp?.data?.data?.view_count || 0,
          },
          {
            id: 2,
            name: "like",
            iconName: "BsSuitHeartFill",
            value: resp?.data?.data?.like_count || 0,
          },
          {
            id: 3,
            name: "message",
            iconName: "BsChatFill-1",
            value: resp?.data?.data?.message_count || 0,
          },
          {
            id: 4,
            name: "share",
            iconName: "BsShareFill",
            value: resp?.data?.data?.shares_count || 0,
          },
          {
            id: 5,
            name: "save",
            iconName: "BsBookmarkFill",
            value: resp?.data?.data?.saves_count || 0,
          },
        ],
        overViews: [
          {
            id: 1,
            text: "Accounts reached",
            value: resp?.data?.data?.overview?.accounts_reached || 0,
          },
          {
            id: 2,
            text: "Interactions",
            value: resp?.data?.data?.overview?.interactions || 0,
          },
          {
            id: 3,
            text: "Profile activity",
            value: resp?.data?.data?.overView?.profile_activity || 0,
          },
        ],
        watch: [
          {
            id: 1,
            text: "Play",
            value: Number(resp?.data?.data?.watch?.play).toFixed(0) || 0,
          },
          {
            id: 2,
            text: "Watch Time",
            value: Number(resp?.data?.data?.watch?.watch_time).toFixed(0) || 0,
          },
          {
            id: 3,
            text: "Average Watch Time",
            value:
              Number(resp?.data?.data?.watch?.average_watch_time).toFixed(0) ||
              0,
          },
        ],
        audience: resp?.data?.data?.audience,
        reachData: convertArrToDataSet(reachDat),
      };
      setAnalyticsData(data);
      setReelInteraction([
        {
          id: 1,
          text: "Like",
          value: resp?.data?.data?.like_count || 0,
        },
        {
          id: 2,
          text: "Share",
          value: resp?.data?.data?.shares_count || 0,
        },
        {
          id: 3,
          text: "Comment",
          value: resp?.data?.data?.message_count || 0,
        },
        {
          id: 4,
          text: "Saved",
          value: resp?.data?.data?.saves_count || 0,
        },
      ]);
      setIsLoader(false);
    } else {
      Toast.show(resp?.data?.message || "Something went wrong please try again");
      setIsLoader(false);
    }
  }, [analyticsDataMemo, boostId, isLoaderMemo]);

  // Get Dropdown data which is display in Dropdown
  const getNatureData = useCallback(
    async (boostId, natureId) => {
      const resp = await getData(boostId, natureId); // Set Api Call For Fetch Dropdown data

      // When get Response success true and available response data then set data in array format
      if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
        setReelInteraction([
          {
            id: 1,
            text: "Like",
            value: resp?.data?.data?.like_count || 0,
          },
          {
            id: 2,
            text: "Share",
            value: resp?.data?.data?.shares_count || 0,
          },
          {
            id: 3,
            text: "Comment",
            value: resp?.data?.data?.message_count || 0,
          },
          {
            id: 4,
            text: "Saved",
            value: resp?.data?.data?.saves_count || 0,
          },
        ]);
      } else {
        // Show Toast Message when come to API Response success false
        Toast.show(resp?.data?.message || "Something went wrong please try again");
      }
    },
    [analyticsDataMemo] // Set Dependency
  );

  // When user come to screen then set API Call for Fetch data
  useFocusEffect(
    useCallback(() => {
      getBoostData();
    }, [])
  );

  // When user Press Header Information Button then call this Function it's depend of type and set data in array format
  const handleToPressHeader = (type) => {
    let mainData = [];
    const findDesc = (slug) =>
      adminSettingData.find((item) => item.slug === slug)?.value;

    switch (type) {
      case "Overview":
        mainData = [
          {
            id: 1,
            title: "Accounts reached",
            desc: findDesc("ACCOUNTREACHED"),
          },
          { id: 2, title: "Interactions", desc: findDesc("INTERACTIONS") },
          {
            id: 3,
            title: "Profile Activity",
            desc: findDesc("PROFILEACTIVITY"),
          },
        ];
        break;
      case "Post Interactions":
      case "Reel Interactions":
        mainData = [
          { id: 1, title: "Like", desc: findDesc("LIKE") },
          { id: 2, title: "Share", desc: findDesc("SHARE") },
          { id: 3, title: "Comments", desc: findDesc("COMMENTS") },
          { id: 4, title: "Saved", desc: findDesc("SAVED") },
        ];
        break;
      case "Watch":
        mainData = [
          { id: 1, title: "Play", desc: findDesc("PLAY") },
          { id: 2, title: "Watch Time", desc: findDesc("WATCHTIME") },
          {
            id: 3,
            title: "Average Watch Time",
            desc: findDesc("AVERAGEWATCHTIME"),
          },
        ];
        break;
      case "reach":
        mainData = [{ id: 1, title: "Post Reach", desc: findDesc("REACH") }];
        break;
      default:
        break;
    }

    setDescData(mainData);
    setDescModal(true);
  };

  // Render Content data Common Function
  const renderContentData = useCallback(
    (
      mainTitle,
      dataArr,
      isDropdown = false,
      placeHolder,
      dropDownData = []
    ) => (
      <>
        <Separator />
        <View style={styles.secondMainView}>
          <ContentData
            mainTitle={mainTitle}
            iconName="BsExclamationCircle"
            dataArr={dataArr}
            isDropdown={isDropdown}
            placeHolder={placeHolder}
            dropDownData={dropDownData}
            value={dropdownDataMemo}
            selectedValue={(e) => {
              setDropdownData(e?.value);
              getNatureData(boostId, e?.value);
            }}
            handleToPressIcon={() => {
              handleToPressHeader(mainTitle);
            }}
          />
        </View>
      </>
    ),
    [dropdownDataMemo] // Set dependency
  );

  // For Rendering Heading Title
  const headingTitle = () => {
    switch (type) {
      case "post":
        return "boostedPost";
      case "reel":
        return "boostedReel";
      default:
        return "boostedContent";
    }
  };

  return (
    <SafeAreaView style={styles.mainView}>
      {/* Header */}
      <CHeader
        headingTitle={headingTitle()}
        handleBackButton={() => navigation.goBack()} // Function to handle back button press
      />
      {isLoaderMemo ? (
        <MiniLoader />
      ) : (
        <>
          <ScrollView
            showsVerticalScrollIndicator={false} // Hide vertical scroll indicator
            style={styles.scrollViewMain}
            bounces={false} // Disable bounce effect
          >
            <View style={styles.secondMainView}>
              {/* Custom component to render the boosted post header */}
              <BoostedPostHeader data={data} />
            </View>
            {/*Rendering graph when analstics Data Memo available data */}
            {analyticsDataMemo?.reachData?.datasets[0]?.data?.length !== 0 &&
              analyticsDataMemo?.reachData !== undefined && (
                <>
                  <Separator />
                  <View style={styles.secondMainView}>
                    {/* Custom component to render post reach information */}
                    <PostReachComponent
                      dataArr={analyticsDataMemo?.reachData}
                      handleToPressIcon={() => {
                        handleToPressHeader("reach");
                      }}
                    />
                  </View>
                </>
              )}
            {/* Separator component */}
            <Separator />
            <View style={styles.secondMainView}>
              {/* Custom component to render social icons */}
              <RenderSocialIcon dataArr={analyticsDataMemo?.socialDataArr} />
            </View>
            {/* Rendering overview section */}
            {renderContentData("Overview", analyticsDataMemo?.overViews)}
            {/* Conditionally rendering the watch section */}
            {type === "reel" &&
              renderContentData("Watch", analyticsDataMemo.watch)}

            {/* For Render Reel Interactions */}
            {renderContentData(
              type === "reel" ? "Reel Interactions" : "Post Interactions",
              reelInteraction,
              true,
              type === "reel" ? "Reel Interactions" : "Post Interactions",
              analyticsDataMemo?.audience
            )}
            {/* Separator component */}
            <Separator />
          </ScrollView>

          {/* Rendering Detail Modal when desModal is true */}
          {desModal ? (
            <DetailViewModal
              visible={desModal}
              listData={descData} // Set Data using variable
              setModalVisible={(e) => setDescModal(e)}
            />
          ) : null}
        </>
      )}
    </SafeAreaView>
  );
};

export default memo(BoostedPostInsights);
