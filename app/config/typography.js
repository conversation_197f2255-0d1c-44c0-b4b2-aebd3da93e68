import { Platform } from "react-native";

/**
 * Common font family setting
 * - This font name will be used for all template
 * - Check more how to add more font family with url below
 * @url http://passionui.com/docs/felix-travel/theming
 */
export const FontFamily = {
  InterRegular: "Inter-Regular",
  InterMedium: "Inter-Medium",
  InterSemiBold: "Inter-SemiBold",
  InterBold: "Inter-Bold",
  RobotoRegular:
    Platform.OS === "android" ? "Roboto-Regular" : "Roboto-Regular",
  RobotoMedium: Platform.OS === "android" ? "Roboto-Medium" : "Roboto-Medium",
  RobotoBold: Platform.OS === "android" ? "Roboto-Black" : "Roboto-Black",
  RobotSemiBold: Platform.OS === "android" ? "Roboto-Bold" : "Roboto-Bold",
  OutFit: "Outfit-Regular",
  OutFitMedium: "Outfit-Medium",
  OutFitSemiBold: "Outfit-SemiBold",
};

/**
 * Fontweight setting
 * FontsFree-Net-SF-Pro-Rounded-Medium
 * - This font weight will be used for style of screens where needed
 * - Check more how to use font weight with url below
 * @url http://passionui.com/docs/felix-travel/theming
 */
export const FontWeight = {
  thin: "100",
  ultraLight: "200",
  light: "300",
  regular: "400",
  medium: "500",
  semibold: "600",
  bold: "700",
  heavy: "800",
  black: "900",
};
