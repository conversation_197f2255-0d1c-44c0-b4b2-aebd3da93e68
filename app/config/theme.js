export const BaseColors = {
  primary: "#F74D74",
  secondary: "#DD5EAA",
  activeTab: "#D6002E",
  inactiveTab: "#9DB2CE",
  White: "#FFFFFF",
  primary1: "#24D1BA",
  primary2: "#FD0EA0",
  primary3: "#1A2C68",

  red: "#FF5555",
  inactive: "#DADADA",
  lightBlack: "#4D4D4D",
  textSecondary: "#858585",
  textInputLabel: "#1a1a1a",
  textInputLabel1: "#4f596744",
  backgroundColor: "#F6F6F6",
  borderColor: "#E4E4E7",
  white: "#ffffff",
  whiteSmoke: "#F1F1F1",
  lightBlack10: "#322F2F",
  blueTik: "#057FFF",
  lightblue: "#E6F2FF",

  black: "#000000",
  white10: "#ffffff10",
  white20: "#ffffff20",
  white30: "#ffffff30",
  white40: "#ffffff40",
  white50: "#ffffff50",
  white60: "#ffffff60",
  white70: "#ffffff70",
  white80: "#ffffff80",
  white90: "#ffffff90",

  black10: "#00000010",
  black20: "#00000020",
  black30: "#00000030",
  black40: "#00000040",
  black50: "#00000050",
  black60: "#00000060",
  black70: "#00000070",
  black80: "#00000080",
  black90: "#00000090",
  black101: "#101010",
  black100: "#252525",
  black103: "#544C4C",
  black102: "#212121",
  textinputBackGroundColor: "#F5F5F5",
  gray10: "#BDBDBD",
  gray: "#DEDEDE",
  gray4: "#424242",
  gray5: "#484848",
  gray3: "#666666",
  gray2: "#969696",
  gray6: "#616161",
  gray7: "#8E8383",
  gray8: "#F5F5F5",
  gray9: "#9C9C9C",
  gray11: "#5A5A5A",
  lightGray: "#F1F5F9",
  fontColor: "#343434",
  textLightGray: "#94A3B8",
  headerIcon: "#313B5D",
  placeHolderColor: "#555454",
  grayBackgroundColor: "#FBFBFB",
  borderColor: "#EBEAEA",
  fontBlueColor: "#235DFF",
  fontDarkGrayColor: "#525252",
  grayFontColor: "#B5B5B5",

  green: "#10C10C",
};

export const DarkBaseColor = {
  Info: "#FF9040",
  Success: "#6FCBFF",
  Warning: "#031FB4",
  Error: "#FF5555",
  whiteColor: "#000000",
  blackColor: "#ffffff",
  transparentWhite: "#00000045",
  placeHolderColor: "#00000099",

  white10: "#00000010",
  white20: "#00000020",
  white30: "#00000030",
  white40: "#00000040",
  white50: "#00000050",
  white60: "#00000060",
  white70: "#00000070",
  white80: "#00000080",
  white90: "#00000090",
  black10: "#ffffff10",
  black20: "#ffffff20",
  black30: "#ffffff30",
  black40: "#ffffff40",
  black50: "#ffffff50",
  black60: "#ffffff60",
  black70: "#ffffff70",
  black80: "#ffffff80",
  black90: "#ffffff90",
  inactive: "#F1F1F1",
  lightBlack: "#F6F6F6",
  textSecondary: "#ffffff",
  backgroundColor: "#000000",
  borderColor: "#D8D8D8",
  white: "#000000",
  whiteSmoke: "#F1F1F1",
  black: "#ffffff",
};

export const BaseStyles = {
  shadow: {
    shadowColor: BaseColors.black40,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  roundCorner: {
    borderTopStartRadius: 10,
    borderTopEndRadius: 10,
    borderBottomStartRadius: 10,
    borderBottomEndRadius: 10,
  },
};
