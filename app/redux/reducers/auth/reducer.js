import { stat } from "react-native-fs";
import types from "./actions";
import { chatList } from "@config/staticData";
import { userStoryHistory } from "@screens/Profile/apiFunction";

const initialState = {
  userData: {},
  user_id: null,
  accessToken: "",
  companyId: null,
  walkthrough: true,
  darkmode: false,
  uuid: "",
  isIntro: true,
  userStoryList: [],
  storyCount: [],
  modalData: {},
  adminSettingData: [],
  createdPostList: [],
  commentData: [],
  reelsList: [],
  userFollowList: [],
  userFollowingList: [],
  savedPostList: [],
  savedReelList: [],
  subscriptionPlan: [],
  addOnPlan: [],
  isReferralCode: false,
  isCurrentPlan: {},
  draftPostList: [],
  draftReelList: [],
  userPreStoryHistory: {},
  userChatList: chatList,
  myHighLights: {},
  myIntro: {},
  myPost: {},
  myReel: {},
  userLikeList: {},
  friendUserData: [],
  userSelfPost: {},
  userSelfReel: {},
  userSelfHighlight: {},
  userSelfIntro: {},
  currentPlanDetail: [],
  searchHistory: [],
  myBoostedPostList: {},
  fromScreenName: "",
  notificationData: [],
  activePlanData: {},
  notificationActivity: "",
  isBestOffer: true,
  notificationCount: 0,
  paymentHistoryList: [],
  appReviewCount: 0,
  feedbackModal: false,
  purchaseModal: {
    isShow: false,
  },
  specialOfferData: {},
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_DATA:
      console.log(`${types.SET_DATA} => `);
      return {
        ...state,
        userData: action.userData,
      };
    case types.SET_WALKTHROUGH:
      return {
        ...state,
        walkthrough: action.walkthrough,
      };
    case types.SET_DARKMODE:
      return {
        ...state,
        darkmode: action.darkmode,
      };
    case types.SET_ACCESSSTOKEN:
      return {
        ...state,
        accessToken: action.accessToken,
      };
    case types.SET_USERID:
      return {
        ...state,
        user_id: action.user_id,
      };
    case types.SET_UUID:
      return {
        ...state,
        uuid: action.uuid,
      };
    case types.SET_MODAL_DATA:
      return {
        ...state,
        modalData: action.modalData,
      };
    case types.SET_SAVED_POST_DATA:
      return {
        ...state,
        savedPostList: action.savedPostList,
      };
    case types.SET_DRAFT_POST_DATA:
      return {
        ...state,
        draftPostList: action.draftPostList,
      };
    case types.SET_DRAFT_REEL_DATA:
      return {
        ...state,
        draftReelList: action.draftReelList,
      };
    case types.SET_USERS_CHAT_DATA:
      return {
        ...state,
        userChatList: action.userChatList,
      };
    case types.SET_SAVED_REELS_DATA:
      return {
        ...state,
        savedReelList: action.savedReelList,
      };
    case types.SET_ADMIN_SETTING_DATA:
      return {
        ...state,
        adminSettingData: action.adminSettingData,
      };
    case types.SET_POST_DATA:
      return {
        ...state,
        createdPostList: action.createdPostList,
      };
    case types.SET_COMMENT_DATA:
      return {
        ...state,
        commentData: action.commentData,
      };
    case types.SET_COMPANY_ID:
      return {
        ...state,
        companyId: action.companyId,
      };
    case types.SET_IS_INTRO:
      return {
        ...state,
        isIntro: action.isIntro,
      };
    case types.LOGOUT:
      return {
        ...initialState,
        isIntro: false,
        purchaseModal: {
          isShow: false,
        },
        specialOfferData: {},
      };
    case types.SET_VIEW_STORY_LIST:
      return {
        ...state,
        userStoryList: action.userStoryList,
      };
    case types.SET_STORY_COUNT:
      return {
        ...state,
        storyCount: action.storyCount,
      };
    case types.SET_REELS_LIST:
      return {
        ...state,
        reelsList: action.reelsList,
      };
    case types.SET_SUBSCRIPTION_PLAN:
      return {
        ...state,
        subscriptionPlan: action.subscriptionPlan,
      };
    case types.SET_ADD_ON_PLAN:
      return {
        ...state,
        addOnPlan: action.addOnPlan,
      };
    case types.SET_IS_REFERRAL_CODE:
      return {
        ...state,
        isReferralCode: action.isReferralCode,
      };
    case types.SET_USER_FOLLOW_LIST:
      return {
        ...state,
        userFollowList: action.userFollowList,
      };
    case types.SET_IS_CURRENT_PLAN:
      return {
        ...state,
        isCurrentPlan: action.isCurrentPlan,
      };
    case types.SET_USER_STORY_HISTORY:
      return {
        ...state,
        userPreStoryHistory: action.userStoryHistory,
      };
    case types.SET_USER_MY_HIGHLIGHTS:
      return {
        ...state,
        myHighLights: action.myHighLights,
      };
    case types.SET_MY_INTRO:
      return {
        ...state,
        myIntro: action.myIntro,
      };
    case types.SET_MY_POST_LIST:
      return {
        ...state,
        myPost: action.myPost,
      };
    case types.SET_MY_REEL_LIST:
      return {
        ...state,
        myReel: action.myReel,
      };
    case types.SET_USER_LIKE_LIST:
      return {
        ...state,
        userLikeList: action.userLikeList,
      };
    case types.SET_USER_FOLLOWING_LIST:
      return {
        ...state,
        userFollowingList: action.userFollowingList,
      };
    case types.SET_FRIEND_USER_DATA:
      return {
        ...state,
        friendUserData: action.friendUserData,
      };
    case types.SET_SEARCH_HISTORY:
      return {
        ...state,
        searchHistory: action.searchHistory,
      };
    case types.SET_USER_SELF_POST:
      return {
        ...state,
        userSelfPost: action.userSelfPost,
      };
    case types.SET_USER_SELF_REEL:
      return {
        ...state,
        userSelfReel: action.userSelfReel,
      };
    case types.SET_USER_SELF_HIGHLIGHT:
      return {
        ...state,
        userSelfHighlight: action.userSelfHighlight,
      };
    case types.SET_USER_SELF_INTRO:
      return {
        ...state,
        userSelfIntro: action.userSelfIntro,
      };
    case types.SET_CURRENT_ITEM_DETAIL:
      return {
        ...state,
        currentItemDetail: action.currentItemDetail,
      };
    case types.SET_MY_BOOSTED_POST_LIST:
      return {
        ...state,
        myBoostedPostList: action.myBoostedPostList,
      };
    case types.SET_ACTIVE_PLAN_DATA:
      return {
        ...state,
        activePlanData: action.activePlanData,
      };
    case types.SET_FROM_SCREEN_NAME:
      return {
        ...state,
        fromScreenName: action.fromScreenName,
      };
    case types.SET_NOTIFICATION_DATA:
      return {
        ...state,
        notificationData: action.notificationData,
      };
    case types.SET_NOTIFICATION_COUNT:
      return {
        ...state,
        notificationCount: action.notificationCount,
      };
    case types.SET_NOTIFICATION_ACTIVITY:
      return {
        ...state,
        notificationActivity: action.notificationActivity,
      };
    case types.SET_IS_SHOW_BEST_OFFER:
      return {
        ...state,
        isBestOffer: action.isBestOffer,
      };
    case types.SET_PAYMENT_HISTORY_LIST:
      return {
        ...state,
        paymentHistoryList: action.paymentHistoryList,
      };
    case types.SET_APP_REVIEW_COUNT:
      return {
        ...state,
        appReviewCount: action.appReviewCount,
      };
    case types.SET_FEEDBACK_MODAL:
      return {
        ...state,
        feedbackModal: action.feedbackModal,
      };

    case types.SET_PURCHASE_MODAL:
      return {
        ...state,
        purchaseModal: action.purchaseModal,
      };
    case types.SET_SPECIAL_OFFER_DATA:
      return {
        ...state,
        specialOfferData: action.specialOfferData,
      };
    default:
      return state;
  }
}
