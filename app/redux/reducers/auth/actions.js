const actions = {
  SET_DATA: "auth/SET_DATA",
  LOGOUT: "auth/LOGOUT",
  SET_WALKTHROUGH: "auth/SET_WALKTHROUGH",
  SET_DARKMODE: "auth/SET_DARKMODE",
  SET_ACCESSSTOKEN: "auth/SET_ACCESSSTOKEN",
  SET_USERID: "auth/SET_USERID",
  SET_COMPANY_ID: "auth/SET_COMPANY_ID",
  SET_UUID: "auth/SET_UUID",
  SET_IS_INTRO: "auth/SET_IS_INTRO",
  SET_VIEW_STORY_LIST: "auth/SET_VIEW_STORY_LIST",
  SET_STORY_COUNT: "auth/SET_STORY_COUNT",
  SET_MODAL_DATA: "auth/SET_MODAL_DATA",
  SET_ADMIN_SETTING_DATA: "SET_ADMIN_SETTING_DATA",
  SET_POST_DATA: "SET_POST_DATA",
  SET_COMMENT_DATA: "SET_COMMENT_DATA",
  SET_REELS_LIST: "SET_REELS_LIST",
  SET_USER_FOLLOW_LIST: "SET_USER_FOLLOW_LIST",
  SET_USER_FOLLOWING_LIST: "SET_USER_FOLLOWING_LIST",
  SET_SAVED_POST_DATA: "SET_SAVED_POST_DATA",
  SET_SAVED_REELS_DATA: "SET_SAVED_REELS_DATA",
  SET_SUBSCRIPTION_PLAN: "SET_SUBSCRIPTION_PLAN",
  SET_ADD_ON_PLAN: "SET_ADD_ON_PLAN",
  SET_IS_REFERRAL_CODE: "SET_IS_REFERRAL_CODE",
  SET_IS_CURRENT_PLAN: "SET_IS_CURRENT_PLAN",
  SET_DRAFT_POST_DATA: "SET_DRAFT_POST_DATA",
  SET_DRAFT_REEL_DATA: "SET_DRAFT_REEL_DATA",
  SET_USERS_CHAT_DATA: "SET_USERS_CHAT_DATA",
  SET_USER_STORY_HISTORY: "SET_USER_STORY_HISTORY",
  SET_USER_MY_HIGHLIGHTS: "SET_USER_MY_HIGHLIGHTS",
  SET_MY_INTRO: "SET_MY_INTRO",
  SET_MY_POST_LIST: "SET_MY_POST_LIST",
  SET_MY_REEL_LIST: "SET_MY_REEL_LIST",
  SET_USER_LIKE_LIST: "SET_USER_LIKE_LIST",
  SET_FRIEND_USER_DATA: "SET_FRIEND_USER_DATA",
  SET_USER_SELF_POST: "SET_USER_SELF_POST",
  SET_USER_SELF_REEL: "SET_USER_SELF_REEL",
  SET_USER_SELF_HIGHLIGHT: "SET_USER_SELF_HIGHLIGHT",
  SET_USER_SELF_INTRO: "SET_USER_SELF_INTRO",
  SET_CURRENT_ITEM_DETAIL: "SET_CURRENT_PLAN_ITEM_DETAIL",
  SET_SEARCH_HISTORY: "SET_SEARCH_HISTORY",
  SET_MY_BOOSTED_POST_LIST: "SET_MY_BOOSTED_POST_LIST",
  SET_FROM_SCREEN_NAME: "SET_FROM_SCREEN_NAME",
  SET_NOTIFICATION_DATA: "SET_NOTIFICATION_DATA",
  SET_ACTIVE_PLAN_DATA: "SET_ACTIVE_PLAN_DATA",
  SET_NOTIFICATION_ACTIVITY: "SET_NOTIFICATION_ACTIVITY",
  SET_IS_SHOW_BEST_OFFER: "SET_IS_SHOW_BEST_OFFER",
  SET_NOTIFICATION_COUNT: "SET_NOTIFICATION_COUNT",
  SET_APP_REVIEW_COUNT: "SET_APP_REVIEW_COUNT",
  SET_PAYMENT_HISTORY_LIST: "SET_PAYMENT_HISTORY_LIST",
  SET_FEEDBACK_MODAL: "SET_FEEDBACK_MODAL",
  SET_PURCHASE_MODAL: "SET_PURCHASE_MODAL",
  SET_SPECIAL_OFFER_DATA: "SET_SPECIAL_OFFER_DATA",

  setPaymentHistoryList: (paymentHistoryList) => (dispatch) =>
    dispatch({
      type: actions.SET_PAYMENT_HISTORY_LIST,
      paymentHistoryList,
    }),

  setNotificationCount: (notificationCount) => (dispatch) =>
    dispatch({
      type: actions.SET_NOTIFICATION_COUNT,
      notificationCount,
    }),

  setNotificationActivity: (notificationActivity) => (dispatch) =>
    dispatch({
      type: actions.SET_NOTIFICATION_ACTIVITY,
      notificationActivity,
    }),

  setNotificationData: (notificationData) => (dispatch) =>
    dispatch({
      type: actions.SET_NOTIFICATION_DATA,
      notificationData,
    }),

  setFromScreenName: (fromScreenName) => (dispatch) =>
    dispatch({
      type: actions.SET_FROM_SCREEN_NAME,
      fromScreenName,
    }),

  setSearchHistory: (searchHistory) => (dispatch) =>
    dispatch({
      type: actions.SET_SEARCH_HISTORY,
      searchHistory,
    }),

  setUserChatList: (userChatList) => (dispatch) =>
    dispatch({
      type: actions.SET_USERS_CHAT_DATA,
      userChatList,
    }),

  setDraftReelList: (draftReelList) => (dispatch) =>
    dispatch({
      type: actions.SET_DRAFT_REEL_DATA,
      draftReelList,
    }),

  setDraftPostList: (draftPostList) => (dispatch) =>
    dispatch({
      type: actions.SET_DRAFT_POST_DATA,
      draftPostList,
    }),
  setSavedReelList: (savedReelList) => (dispatch) =>
    dispatch({
      type: actions.SET_SAVED_REELS_DATA,
      savedReelList,
    }),

  setSavedPostList: (savedPostList) => (dispatch) =>
    dispatch({
      type: actions.SET_SAVED_POST_DATA,
      savedPostList,
    }),

  setCommentData: (commentData) => (dispatch) =>
    dispatch({
      type: actions.SET_COMMENT_DATA,
      commentData,
    }),
  setWalkthrough: (walkthrough) => (dispatch) =>
    dispatch({
      type: actions.SET_WALKTHROUGH,
      walkthrough,
    }),
  setCreatedPostList: (createdPostList) => (dispatch) =>
    dispatch({
      type: actions.SET_POST_DATA,
      createdPostList,
    }),
  setAdminSettingData: (adminSettingData) => (dispatch) =>
    dispatch({
      type: actions.SET_ADMIN_SETTING_DATA,
      adminSettingData,
    }),
  setIsIntro: (isIntro) => (dispatch) =>
    dispatch({
      type: actions.SET_IS_INTRO,
      isIntro,
    }),
  setDarkmode: (darkmode) => (dispatch) =>
    dispatch({
      type: actions.SET_DARKMODE,
      darkmode,
    }),
  setAccessToken: (accessToken) => (dispatch) =>
    dispatch({
      type: actions.SET_ACCESSSTOKEN,
      accessToken,
    }),

  setUserId: (user_id) => (dispatch) =>
    dispatch({
      type: actions.SET_USERID,
      user_id,
    }),

  setUUid: (uuid) => (dispatch) =>
    dispatch({
      type: actions.SET_UUID,
      uuid,
    }),
  setModalData: (modalData) => (dispatch) =>
    dispatch({
      type: actions.SET_MODAL_DATA,
      modalData,
    }),
  setCompanyId: (companyId) => (dispatch) =>
    dispatch({
      type: actions.SET_COMPANY_ID,
      companyId,
    }),
  setUserData: (data) => {
    let uData = {};
    if (data !== undefined && data !== null && Object.keys(data).length > 0) {
      uData = data;
    }

    return (dispatch) =>
      dispatch({
        type: actions.SET_DATA,
        userData: uData,
      });
  },
  setUserStoryList: (userStoryList) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_VIEW_STORY_LIST,
        userStoryList: userStoryList,
      });
  },
  setStoryCount: (storyCount) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_STORY_COUNT,
        storyCount: storyCount,
      });
  },
  setReelsList: (reelsList) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_REELS_LIST,
        reelsList: reelsList,
      });
  },
  setUserFollowList: (userFollowList) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_FOLLOW_LIST,
        userFollowList: userFollowList,
      });
  },
  logOut: () => (dispatch) =>
    dispatch({
      type: actions.LOGOUT,
    }),
  setSubScriptionPlan: (subscriptionPlan) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_SUBSCRIPTION_PLAN,
        subscriptionPlan: subscriptionPlan,
      });
  },
  setAddOnPlan: (addOnPlan) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_ADD_ON_PLAN,
        addOnPlan: addOnPlan,
      });
  },
  setIsReferralCode: (isReferralCode) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_IS_REFERRAL_CODE,
        isReferralCode: isReferralCode,
      });
  },
  setIsCurrentPlan: (isCurrentPlan) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_IS_CURRENT_PLAN,
        isCurrentPlan: isCurrentPlan,
      });
  },
  setUserStoryHistory: (userStoryHistory) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_STORY_HISTORY,
        userStoryHistory: userStoryHistory,
      });
  },
  setMyHighlights: (myHighLights) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_MY_HIGHLIGHTS,
        myHighLights: myHighLights,
      });
  },
  setMyIntro: (myIntro) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_MY_INTRO,
        myIntro: myIntro,
      });
  },
  setMyPost: (myPost) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_MY_POST_LIST,
        myPost: myPost,
      });
  },
  setMyReel: (myReel) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_MY_REEL_LIST,
        myReel: myReel,
      });
  },
  setUserLikeList: (userLikeList) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_LIKE_LIST,
        userLikeList: userLikeList,
      });
  },
  setUserFollowingList: (userFollowingList) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_FOLLOWING_LIST,
        userFollowingList: userFollowingList,
      });
  },
  setFriendUserData: (friendUserData) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_FRIEND_USER_DATA,
        friendUserData: friendUserData,
      });
  },
  setUserSelfPost: (userSelfPost) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_SELF_POST,
        userSelfPost: userSelfPost,
      });
  },
  setUserSelfReel: (userSelfReel) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_SELF_REEL,
        userSelfReel: userSelfReel,
      });
  },
  setUserSelfHighlight: (userSelfHighlight) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_SELF_HIGHLIGHT,
        userSelfHighlight: userSelfHighlight,
      });
  },
  setUserSelfIntro: (userSelfIntro) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_USER_SELF_INTRO,
        userSelfIntro: userSelfIntro,
      });
  },
  setCurrentPlanDetail: (currentPlanDetail) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_CURRENT_ITEM_DETAIL,
        currentPlanDetail: currentPlanDetail,
      });
  },
  setActivePlanData: (activePlanData) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_ACTIVE_PLAN_DATA,
        activePlanData: activePlanData,
      });
  },
  setMyBoostedPostList: (myBoostedPostList) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_MY_BOOSTED_POST_LIST,
        myBoostedPostList: myBoostedPostList,
      });
  },
  setIsShowBestOffer: (isBestOffer) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_IS_SHOW_BEST_OFFER,
        isBestOffer: isBestOffer,
      });
  },
  setAppReviewCount: (appReviewCount) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_APP_REVIEW_COUNT,
        appReviewCount: appReviewCount,
      });
  },
  setFeedBackModal: (feedbackModal) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_FEEDBACK_MODAL,
        feedbackModal: feedbackModal,
      });
  },
  setPurchaseModal: (purchaseModal) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_PURCHASE_MODAL,
        purchaseModal: purchaseModal,
      });
  },
  setSpecialOfferData: (specialOfferData) => {
    return (dispatch) =>
      dispatch({
        type: actions.SET_SPECIAL_OFFER_DATA,
        specialOfferData: specialOfferData,
      });
  },
};

export default actions;
