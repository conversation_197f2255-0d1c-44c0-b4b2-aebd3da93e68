import types from "./actions";

const initialState = {
  socketObj: null,
  chatData: {},
  typing: false,
  typingData: {},
  totalMsgCount: 0,
  selectedChatRoom: null,
  chatRooms: [],
  selectedRoom: {},
  chatLoader: false,
  chatListNextEnablePage: { page: 1, next_enable: false },
  uploadPercentage: 0,
  isReadTic: {},
  bottomLoader: false,
  isPlanCancelData: {},
  isBoostPaymentStatus: {}
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_SOCKET:
      return {
        ...state,
        socketObj: action.socketObj,
      };

    case types.CLEAR_SOCKET:
      console.log(`${types.CLEAR_SOCKET} => `);
      return {
        ...state,
        socketObj: null,
      };

    case types.SET_RECEIVED_CHAT_DATA:
      console.log(`${types.SET_RECEIVED_CHAT_DATA} ==>`);
      // CAlert(JSON.stringify(action.chatData));
      console.log("action.chatData =>");
      console.log(action.chatData);
      return {
        ...state,
        chatData: action.chatData,
      };

    case types.CLEAR_CHAT_DATA:
      console.log(`${types.CLEAR_CHAT_DATA} => `);
      return {
        ...state,
        chatData: {},
      };

    case types.SET_TYPING:
      console.log(`${types.SET_TYPING} => `);
      return {
        ...state,
        typing: action.typing,
        typingData: action.typingData,
      };

    case types.SET_TOTAL_MSG_COUNT:
      console.log(`${types.SET_TOTAL_MSG_COUNT} => `);
      return {
        ...state,
        totalMsgCount: action.data,
      };

    case types.SET_CHAT_ROOM:
      return {
        ...state,
        chatRooms: action.chatRooms,
      };
    case types.SET_SELECTED_CHAT_ROOM:
      return {
        ...state,
        selectedRoom: action.selectedRoom,
      };
    case types.SET_LOADER:
      return {
        ...state,
        chatLoader: action.chatLoader,
      };
    case types.SET_BOTTOM_LOADER:
      return {
        ...state,
        bottomLoader: action.bottomLoader,
      };
    case types.SET_CHATROOM_PAGINATION:
      return {
        ...state,
        chatListNextEnablePage: action.chatListNextEnablePage,
      };

    case types.SET_UPLOAD_PROGRESS_PERCENTAGE:
      return {
        ...state,
        uploadPercentage: action.uploadPercentage,
      };
    case types.SET_IS_READ_TIC:
      return {
        ...state,
        isReadTic: action.isReadTic,
      };

      case types.SET_IS_BOOST_PAYMENT_STATUS:
      return {
        ...state,
        isBoostPaymentStatus: action.isBoostPaymentStatus,
      };

      case types.SET_CANCEL_PLAN:
      return {
        ...state,
        isPlanCancelData: action.data,
      };

    default:
      return state;
  }
}
