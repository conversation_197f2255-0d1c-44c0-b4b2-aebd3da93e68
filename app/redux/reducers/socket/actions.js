import io from "socket.io-client";
import settings from "@config/setting";
import isEmpty from "lodash-es/isEmpty";
import isObject from "lodash-es/isObject";
import isUndefined from "lodash-es/isUndefined";
import isArray from "lodash-es/isArray";
import authAction from "../auth/actions";

let SOCKET = null;

const actions = {
  INIT_SOCKET: "socket/INIT_SOCKET",
  EMIT_SOCKET: "socket/EMIT_SOCKET",
  SET_SOCKET: "socket/SET_SOCKET",
  CLEAR_SOCKET: "socket/CLEAR_SOCKET",
  SET_RECEIVED_CHAT_DATA: "socket/SET_RECEIVED_CHAT_DATA",
  SET_CHAT_ROOM: "SET_CHAT_ROOM",
  SET_SELECTED_CHAT_ROOM: "SET_SELECTED_CHAT_ROOM",
  SET_LOADER: "SET_LOADER",
  SET_CHATROOM_PAGINATION: "SET_CHATROOM_PAGINATION",
  SET_UPLOAD_PROGRESS_PERCENTAGE: "SET_UPLOAD_PROGRESS_PERCENTAGE",
  SET_MAIN_CHAT_LOADER: "SET_MAIN_CHAT_LOADER",
  SET_IS_READ_TIC: "SET_IS_READ_TIC",
  SET_BOTTOM_LOADER: "SET_BOTTOM_LOADER",

  CLEAR_CHAT_DATA: "socket/CLEAR_CHAT_DATA",
  SET_TYPING: "socket/SET_TYPING",
  SET_TOTAL_MSG_COUNT: "socket/SET_TOTAL_MSG_COUNT",
  SET_CANCEL_PLAN: "socket/SET_CANCEL_PLAN",
  SET_IS_BOOST_PAYMENT_STATUS: "socket/SET_IS_BOOST_PAYMENT_STATUS",

  setSocket: (socketObj) => (dispatch) =>
    dispatch({
      type: actions.SET_SOCKET,
      socketObj,
    }),

  onDisConnect: (bool) => (dispatch) => {
    if (bool) {
      dispatch({
        type: actions.CLEAR_SOCKET,
        socketObj: null,
      });
    }
  },

  onReceive: (chatData) => (dispatch) => {
    if (isObject(chatData) && !isEmpty(chatData)) {
      const cdata = chatData;
      dispatch({
        type: actions.SET_RECEIVED_CHAT_DATA,
        chatData: cdata,
      });
    }
  },

  initialization: () => (dispatch, getState) => {
    // console.log('getState()----', getState().socket);
    const { socketObj } = getState().socket;
    if (socketObj === null && isUndefined(socketObj?.emit)) {
      const connectionConfig = {
        transports: ["websocket"], /// you need to explicitly tell it to use websockets
      };
      SOCKET = io(settings.socketURL, connectionConfig);

      SOCKET.on("connect", () => {
        console.log("🚀 ~ SOCKET ~ connected successfully");
        const { userData } = getState().auth;
        const options = {
          user_id: userData?.user_id,
        };
        // console.log(token);
        dispatch(actions.setSocket(SOCKET));
        if (socketObj === null) {
          dispatch(actions.emit("userconnect", options));
        }
      });

      SOCKET.on("connect_error", (error) => {
        console.log("🚀 ~ SOCKET ~ connection error:", error);
        dispatch({
          type: actions.SET_LOADER,
          chatLoader: false,
        });
      });

      SOCKET.on("upload_progress", (data) => {
        dispatch({
          type: actions.SET_UPLOAD_PROGRESS_PERCENTAGE,
          uploadPercentage: data,
        });
      });

      SOCKET.on("receive_message", (data) => {
        dispatch(actions.onReceive(data));
      });
      SOCKET.on("message_edited", (data) => {
        dispatch(actions.onReceive(data));
      });

      SOCKET.on("receiveTyping", (data) => {
        dispatch(actions.onReceiveTyping(data));
      });

      SOCKET.on("receiveGroupTyping", (data) => {
        dispatch(actions.onReceiveTyping(data));
      });

      SOCKET.on("group_receive_message", (data) => {
        console.log("🚀 ~ datan recived message-----:", data);
        dispatch(actions.onReceive(data));
      });

      SOCKET.on("group_message_edited", (data) => {
        console.log("🚀 ~ data edited-----:", data);
        dispatch(actions.onReceive(data));
      });

      SOCKET.on("group_receiveTyping", (data) => {
        console.log("🚀 ~ data:", data);
        dispatch(actions.onReceiveTyping(data));
      });

      SOCKET.on("group_message_read", (data) => {
        dispatch({
          type: actions.SET_IS_READ_TIC,
          isReadTic: data,
        });
      });

      SOCKET.on("receiveCount", (data) => {
        dispatch({
          type: actions.SET_TOTAL_MSG_COUNT,
          data: data?.message_count,
        });
      });

      SOCKET.on("message_read", (data) => {
        console.log("🚀 ~ SOCKET.on ~ data:", data);
        dispatch({
          type: actions.SET_IS_READ_TIC,
          isReadTic: data,
        });
      });

      SOCKET.on("plan_cancel", (data) => {
        console.log("🚀 ~ SOCKET.on ~ data:", data);
        if (data?.notificationType === "plan_cancel") {
          dispatch(authAction.setIsCurrentPlan({}));
          dispatch(
            authAction.setActivePlanData({
              is_plan_active: false,
              is_prime_user: false,
              is_free_user: false,
            })
          );
        }
      });

      SOCKET.on("payment_status", (data) => {
        dispatch({
          type: actions.SET_IS_BOOST_PAYMENT_STATUS,
          isBoostPaymentStatus: data,
        });
      });

      SOCKET.on("error", (data) => {
        console.log("SOCKET error =======>", data);
        // dispatch(actions.onReceiveTyping(data, 'stop'));
      });

      SOCKET.on("disconnect", () => {
        dispatch({
          type: actions.SET_SOCKET,
          socketObj: null,
        });
        dispatch(actions.onDisConnect(true));
      });
    } else {
      console.log("Socket Already connected ==>");
    }
  },

  emit:
    (event, data, callback = () => {}) =>
    (dispatch, getState) => {
      const { socketObj } = getState().socket;
      console.log("🚀 ~ emit ~ event:", event, "socketObj:", !!socketObj);

      if (socketObj !== null && !isUndefined(socketObj?.emit)) {
        try {
          socketObj.emit(event, data, (callBackData) => {
            console.log(
              "🚀 ~ emit ~ callback received for event:",
              event,
              callBackData
            );
            callback(callBackData);
          });
        } catch (error) {
          console.log("🚀 ~ emit ~ error:", error);
          // Call callback with error to handle it
          callback({ success: false, message: "Socket error" });
        }
      } else {
        console.log(
          "🚀 ~ emit ~ socket not connected, calling callback with error"
        );
        // Call callback with error if socket is not connected
        callback({ success: false, message: "Socket not connected" });
      }
    },

  onReceiveTyping: (data) => (dispatch, getState) => {
    dispatch({
      type: actions.SET_TYPING,
      typing: "",
      typingData: isObject(data) && isObject(data) ? data : {},
    });
  },

  disconnect: () => (dispatch, getState) => {
    const { socketObj } = getState().socket;
    if (socketObj !== null) {
      socketObj.disconnect();
      dispatch(actions.setSocket(null));
    }
  },

  setTotalMsgCount: (data) => (dispatch) => {
    dispatch({
      type: actions.SET_TOTAL_MSG_COUNT,
      data,
    });
  },

  setCancelPlan: (data) => (dispatch) => {
    dispatch({
      type: actions.SET_CANCEL_PLAN,
      data,
    });
  },

  getChatList:
    (type = "", pages = 1, flt = "", loader = true, chatBottomLoader = false) =>
    (dispatch, getState) => {
      // const { view } = getState().app;
      const { chatRooms } = getState().socket;
      const { userData } = getState().auth;
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

      // const loader = true;

      const params = {
        user_id: userData?.user_id,
        page: pages,
        timezone: timezone,
      };
      if (type === "init" && loader && isEmpty(chatRooms)) {
        dispatch({
          type: actions.SET_LOADER,
          chatLoader: true,
        });
      }

      if (chatBottomLoader) {
        dispatch({
          type: actions.SET_BOTTOM_LOADER,
          bottomLoader: true,
        });
      }

      // Add timeout to ensure loader stops even if socket fails
      const timeoutId = setTimeout(() => {
        console.log("🚀 ~ getChatList ~ timeout reached, stopping loader");
        dispatch({
          type: actions.SET_LOADER,
          chatLoader: false,
        });
        dispatch({
          type: actions.SET_BOTTOM_LOADER,
          bottomLoader: false,
        });
      }, 10000); // 10 second timeout

      dispatch(
        actions.emit("list_Chats", params, (data) => {
          // Clear the timeout since we got a response
          clearTimeout(timeoutId);

          console.log("🚀 ~ getChatList ~ response received:", data);

          const chatLists =
            isObject(data) && isArray(data?.data?.data) ? data.data?.data : [];
          dispatch({
            type: actions.SET_CHAT_ROOM,
            chatRooms: pages > 1 ? [...chatRooms, ...chatLists] : chatLists,
          });
          dispatch({
            type: actions.SET_BOTTOM_LOADER,
            bottomLoader: false,
          });
          dispatch({
            type: actions.SET_LOADER,
            chatLoader: false,
          });
          dispatch({
            type: actions.SET_MAIN_CHAT_LOADER,
            data: false,
          });
          dispatch({
            type: actions.SET_CHATROOM_PAGINATION,
            chatListNextEnablePage: {
              page: data?.data?.pagination.currentPage,
              next_enable: data?.data?.pagination?.hasNextPage || false,
            },
          });
          dispatch({
            type: actions.SET_RECEIVED_CHAT_DATA,
            chatData: {},
          });
        })
      );
    },

  clearChatData: () => (dispatch, getState) => {
    dispatch({
      type: actions.CLEAR_CHAT_DATA,
    });
  },
  setChatList: (chatRooms) => (dispatch) =>
    dispatch({
      type: actions.SET_CHAT_ROOM,
      chatRooms,
    }),
  setChatListLoader: (chatLoader) => (dispatch) =>
    dispatch({
      type: actions.SET_LOADER,
      chatLoader,
    }),
  setChatListBottomLoader: (bottomLoader) => (dispatch) =>
    dispatch({
      type: actions.SET_BOTTOM_LOADER,
      bottomLoader,
    }),
  setChatListPagination: (chatListNextEnablePage) => (dispatch) =>
    dispatch({
      type: actions.SET_CHATROOM_PAGINATION,
      chatListNextEnablePage,
    }),
  setSelectedRoom: (selectedRoom) => (dispatch) =>
    dispatch({
      type: actions.SET_SELECTED_CHAT_ROOM,
      selectedRoom,
    }),
  setUploadPercentage: (uploadPercentage) => (dispatch) =>
    dispatch({
      type: actions.SET_UPLOAD_PROGRESS_PERCENTAGE,
      uploadPercentage,
    }),
  setIsReadTic: (isReadTic) => (dispatch) =>
    dispatch({
      type: actions.SET_IS_READ_TIC,
      isReadTic,
    }),
  setIsBoostPaymentStatus: (isBoostPaymentStatus) => (dispatch) =>
    dispatch({
      type: actions.SET_IS_BOOST_PAYMENT_STATUS,
      isBoostPaymentStatus,
    }),
};

export default actions;
