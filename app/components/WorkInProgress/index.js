// Import React and Component
// import { images } from '@config/images';
// import LottieView from 'lottie-react-native';
import React from 'react';

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;

const WorkInProgress = () => {
  return (
    <LottieView
      autoSize={true}
      source={images.screenRemain}
      autoPlay={true}
      // loop={true}
      style={{
        height: 175,
        width: '100%',
        flex: 1,
        // justifyContent: 'center',
        // alignItems: 'center',
        borderRadius: 200,
      }}
    />
  );
};

export default WorkInProgress;
