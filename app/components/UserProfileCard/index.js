import React, { useState } from "react";
import { Text, View, TouchableOpacity, Modal, Image } from "react-native";
import styles from "./style";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import CButton from "@components/CButton";

import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import authAction from "@redux/reducers/auth/actions";
import { isEmpty } from "lodash-es";
import { handleFollowToggle } from "./apiFunction";

const { setUserData } = authAction;
const UserProfileCard = ({
  visible,
  setModalVisible,
  listData = {},
  title = "",
}) => {
  const dispatch = useDispatch();
  const { userData } = useSelector((s) => s.auth);
  const [isLoading, setIsLoading] = useState(false);

  const handleFollowAnimation = (id) => {
    if (!isEmpty(userData)) {
      if (listData?.type === "Remove") {
        userData.followers = userData.followers - 1;
      } else if (listData?.type === "type") {
        userData.followings = userData.followings + 1;
      } else {
        userData.followings = userData.followings - 1;
      }
    }
  };
  const handleToFollow = async () => {
    setIsLoading(true);
    const resp = await handleFollowToggle(
      listData?.item?.user_id,
      listData?.type === "Remove" ? "remove" : null
    );
    if (resp?.data?.success) {
      handleFollowAnimation(listData?.item?.user_id);
      setModalVisible(false);
      setIsLoading(false);
      Toast.show(resp?.data?.message);
    } else {
      setModalVisible(false);
      setIsLoading(false);
      Toast.show(resp?.data?.message);
    }
    setIsLoading(false);
  };
  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <View style={styles.ovarlayStyle}>
        <View style={styles.modalView}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Text style={styles.modalTitleText}>{translate(title)}</Text>

            <TouchableOpacity
              style={{
                borderWidth: 1,
                height: 24,
                width: 24,
                borderRadius: 5,
                alignItems: "center",
                justifyContent: "center",
              }}
              activeOpacity={0.8}
              onPress={() => setModalVisible(false)}
            >
              <CustomIcon name="BsX" size={20} color={BaseColors.black} />
            </TouchableOpacity>
          </View>
          <View style={{ alignItems: "center" }}>
            <Image
              source={{ uri: listData?.item?.user_dp }}
              style={{ height: 74, width: 75, borderRadius: 5 }}
              resizeMode="cover"
            />
          </View>
          <View>
            <Text style={styles.questionTextStyle}>
              {listData?.type === "Remove" ? "Remove follower?" : null}
            </Text>
          </View>
          <View>
            <Text style={styles.descriptionTextStyle}>
              {listData?.type === "Remove"
                ? `We won’t tell `
                : `If you change your mind, you'll have to request to follow `}
              <Text
                style={[
                  styles.questionTextStyle,
                  { textTransform: "capitalize" },
                ]}
              >
                {listData?.item?.full_name}
              </Text>
              {listData?.type === "Remove"
                ? ` that they were removed from your followers.`
                : ` again.`}
            </Text>
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: 30,
            }}
          >
            <View style={{ width: "48%" }}>
              <CButton loading={isLoading} onBtnClick={() => handleToFollow()}>
                {listData?.type === "Remove" ? "Remove" : "Unfollow"}
              </CButton>
            </View>
            <View style={{ width: "48%" }}>
              <CButton
                type="outlined"
                onBtnClick={() => setModalVisible(false)}
              >
                Cancel
              </CButton>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default UserProfileCard;
