import React, { useState } from "react";
import { Text, TouchableOpacity, View, FlatList } from "react-native";
import { CustomIcon } from "@config/LoadIcons";
import styles from "./styles";
import NoRecord from "@components/NoRecord";
// import Animated, { FadeInDown } from "react-native-reanimated";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

const Collapse = ({ data }) => {
  const [activeIndex, setActiveIndex] = useState(null);

  const toggleCollapse = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const renderItem = ({ item, index }) => (
    <Animated.View entering={FadeInDown} key={index} style={styles.mainBox}>
      <TouchableOpacity
        onPress={() => toggleCollapse(index)}
        activeOpacity={0.8}
      >
        <View style={styles.titleBox}>
          <View style={{ width: "95%" }}>
            <Text style={styles.titleText}>{item.title}</Text>
          </View>
          <View style={{ width: "10%" }}>
            <CustomIcon
              name={activeIndex === index ? "BsChevronUp" : "BsChevronDown"}
              size={20}
              color={"#424242"}
            />
          </View>
        </View>
      </TouchableOpacity>
      {activeIndex === index && (
        <View style={styles.descriptionBox}>
          <Text style={styles.descriptionText}>{item.description}</Text>
        </View>
      )}
    </Animated.View>
  );

  return (
    <View>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
        ListEmptyComponent={
          <View style={styles.centerMain}>
            <NoRecord title={"noRecordFound"} />
          </View>
        }
      />
    </View>
  );
};
export default Collapse;
