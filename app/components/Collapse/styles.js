import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

export default StyleSheet.create({
  mainView: {
    gap: 10,
  },
  CollapseView: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: "#fff",
  },
  mainBox: {
    borderWidth: 1.5,
    borderRadius: 10,
    borderColor: "#F5F5F5",
    marginBottom: 10,
  },
  titleBox: {
    padding: 20,
    justifyContent: "center",
    borderBottomWidth: 1,
    borderColor: "#F5F5F5",
    borderRadius: 25,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  descriptionBox: {
    padding: 10,
    borderRadius: 10,
    color: "#424242",
    fontFamily: FontFamily.InterRegular,
  },
  descriptionText: {
    fontSize: 16,
    color: BaseColors.black,
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  titleText: {
    fontSize: 18,
    color: BaseColors.black,
    fontFamily: FontFamily.InterSemiBold,
  },
  centerMain: {
    flex: 1,
    height: Dimensions.get("window").height / 1.5 - 85,
  },
});
