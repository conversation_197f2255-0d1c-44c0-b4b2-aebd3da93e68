import React from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";

const AlreadyHaveStoryModal = ({
  visible,
  setModalVisible,
  onPressTitle1,
  onPressTitle2,
  headingText = "",
  title1 = "",
  title2 = "",
}) => {
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <View />
                <Text style={styles.modalTitleText}>
                  {translate(headingText)}
                </Text>

                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    height: 24,
                    width: 24,
                    borderRadius: 5,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  activeOpacity={0.8}
                  onPress={() => setModalVisible(false)}
                >
                  <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                style={[styles.textContainer, { borderBottomWidth: 0.5 }]}
                activeOpacity={0.8}
                onPress={onPressTitle1}
              >
                <Text style={styles.textStyle}>{translate(title1)}</Text>
                <CustomIcon
                  name="BsChevronRight"
                  size={14}
                  color={BaseColors.black}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.textContainer}
                activeOpacity={0.8}
                onPress={onPressTitle2}
              >
                <Text style={styles.textStyle}>{translate(title2)}</Text>
                <CustomIcon
                  name="BsChevronRight"
                  size={14}
                  color={BaseColors.black}
                />
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default AlreadyHaveStoryModal;
