import React, { useCallback } from "react";
import {
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
  FlatList,
  Modal,
  ScrollView,
} from "react-native";
import styles from "./style";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import { followListData } from "@config/staticData";
// import FastImage from "react-native-fast-image";
import NoRecord from "@components/NoRecord";
import { useSelector } from "react-redux";

const FastImage = require("react-native-fast-image");

const FollowListModal = ({
  visible,
  setModalVisible,
  listData = [],
  title = "",
  buttonText,
  user_id,
  onChangeSearch = () => {},
  handleBtnPress = () => {},
  onPressItemClick = () => {},
}) => {
  const { userData, userFollowList, userFollowingList } = useSelector(
    (s) => s.auth
  );

  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
      onChangeSearch("");
    }
    // setModalVisible(false);
    Keyboard.dismiss();
  };

  const renderItem = useCallback(
    (item, index) => {
      const data = item?.item;

      return (
        // Item Profile data
        <TouchableOpacity
          style={[
            styles.renderItemMainView,
            {
              borderBottomWidth:
                item?.index !== listData?.length - 1 ? 0.75 : null,
            },
          ]}
          activeOpacity={0.9}
          onPress={() => onPressItemClick(data)}
        >
          {/* Header Image */}
          <View style={styles.userDataComponent}>
            <View>
              <FastImage
                source={{ uri: data?.user_dp }}
                style={styles.profileDPStyle}
              />
            </View>
            <Text style={styles.textComponentStyle}>{data?.username}</Text>
          </View>
          {userData?.user_id !== data?.user_id ? (
            <TouchableOpacity
              style={[
                styles.btnViewStyle,
                {
                  backgroundColor: !data?.is_followed
                    ? BaseColors.activeTab
                    : BaseColors.white,
                },
              ]}
              onPress={() =>
                handleBtnPress({
                  item: data,
                  index: item?.index,
                  type:
                    user_id === userData?.user_id
                      ? !data?.is_followed
                        ? "Follow"
                        : buttonText
                      : "Follow",
                })
              }
            >
              <Text
                style={[
                  styles.btnTextStyle,
                  {
                    color: !data?.is_followed
                      ? BaseColors.white
                      : BaseColors.fontColor,
                  },
                ]}
              >
                {user_id === userData?.user_id
                  ? !data?.is_followed
                    ? "Follow"
                    : buttonText
                  : !data?.is_followed
                    ? "Follow"
                    : "Following"}
              </Text>
            </TouchableOpacity>
          ) : null}
        </TouchableOpacity>
      );
    },
    [listData, userFollowList, userFollowingList]
  );

  const noRecordView = () => {
    return <NoRecord title="noShareList" />;
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <View style={styles.ovarlayStyle}>
        <View style={styles.modalView}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Text style={styles.modalTitleText}>{translate(title)}</Text>

            <TouchableOpacity
              style={{
                borderWidth: 1,
                height: 24,
                width: 24,
                borderRadius: 5,
                alignItems: "center",
                justifyContent: "center",
              }}
              activeOpacity={0.8}
              onPress={() => setModalVisible(false)}
            >
              <CustomIcon name="BsX" size={20} color={BaseColors.black} />
            </TouchableOpacity>
          </View>

          <View style={{ marginBottom: 20 }}>
            <FlatList
              data={listData}
              renderItem={renderItem}
              ListEmptyComponent={noRecordView}
              scrollEnabled
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default FollowListModal;
