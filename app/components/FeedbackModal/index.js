import React, { useMemo, useState } from "react";
import { Text, View, TouchableOpacity, Modal } from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import { AirbnbRating } from "react-native-ratings";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import { sendFeedBack } from "@screens/AddReview/apiFunction";
import Toast from "react-native-simple-toast";
import authAction from "@redux/reducers/auth/actions";
import { useDispatch, useSelector } from "react-redux";

const FastImage = require("react-native-fast-image");

const FeedBackModal = ({ title = "" }) => {
  const dispatch = useDispatch();
  const { setFeedBackModal } = authAction;
  const { feedbackModal } = useSelector((s) => s.auth);

  const [rating, setRating] = useState(null);
  const [isShareFeedbackText, setIsShareFeedbackText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const isShareFeedbackTextMemo = useMemo(
    () => isShareFeedbackText,
    [isShareFeedbackText]
  );
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const ratingMemo = useMemo(() => rating, [rating]);

  const handleToPressBtn = async () => {
    setIsLoading(true);
    const finalData = {
      rates: ratingMemo,
      messages: isShareFeedbackTextMemo,
    };

    const resp = await sendFeedBack(finalData);
    if (resp?.data?.success) {
      setRating(null);
      setIsShareFeedbackText("");
      dispatch(setFeedBackModal(false));
      Toast.show("Feedback submitted successfully");
    } else {
      setIsLoading(false);
      setRating(null);
      setIsShareFeedbackText("");
      dispatch(setFeedBackModal(false));
      Toast.show(resp?.data?.message || "Something went wrong");
    }
    setIsLoading(false);
  };
  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={feedbackModal}
      onRequestClose={() => {
        dispatch(setFeedBackModal(false));
      }}
    >
      <View style={styles.ovarlayStyle}>
        <View style={styles.modalView}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Text style={styles.modalTitleText}>{translate(title)}</Text>

            <TouchableOpacity
              style={{
                borderWidth: 1,
                height: 24,
                width: 24,
                borderRadius: 5,
                alignItems: "center",
                justifyContent: "center",
              }}
              activeOpacity={0.8}
              onPress={() => dispatch(setFeedBackModal(false))}
            >
              <CustomIcon name="BsX" size={20} color={BaseColors.black} />
            </TouchableOpacity>
          </View>
          <View style={styles.ratingView}>
            {/* For Render Rating Component */}
            <AirbnbRating
              count={5}
              defaultRating={0}
              size={40}
              showRating={false}
              selectedColor={BaseColors.activeTab}
              onFinishRating={setRating}
              ratingContainerStyle={styles.ratingReviewView}
            />
          </View>
          <View style={styles.ratingView}>
            <Text style={styles.normalTextStyle}>
              {translate("ratingMoreAboutQueText")}
            </Text>
            {/* For Render Rating Component */}
            <View style={styles.moreAboutTextInputStyle}>
              <CInput
                returnKeyType="done"
                value={isShareFeedbackTextMemo}
                onChange={setIsShareFeedbackText}
                onSubmit={() => {
                  Keyboard.dismiss();
                }}
                multiline
                numberOfLines={5}
              />
            </View>
            <View style={styles.moreAboutTextInputStyle}>
              <CButton
                disabled={ratingMemo === null}
                onBtnClick={() => handleToPressBtn()}
                loading={isLoadingMemo}
              >
                SUBMIT
              </CButton>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default FeedBackModal;
