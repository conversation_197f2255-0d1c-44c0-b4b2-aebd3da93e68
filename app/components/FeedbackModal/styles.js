import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";
import { isIPhoneX } from "react-native-status-bar-height";

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalView: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
    paddingBottom: isIPhoneX() ? 40 : 20
  },

  modalTitleText: {
    fontSize: 24,
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginBottom: 15,
    textAlignVertical: "center",
  },
  dataMainView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },

  headingText: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  normalTextStyle: {
    fontSize: 20,
    color: BaseColors.black,
    fontFamily: FontFamily.OutFitRegular,
  },
  ratingView: {
    marginVertical: 20,
  },
  ratingReviewView: {
    marginTop: 18,
  },
  moreAboutTextInputStyle: {
    marginTop: 20,
  },
  btnContainerStyle: {
    marginTop: 20,
  },
});

export default styles;
