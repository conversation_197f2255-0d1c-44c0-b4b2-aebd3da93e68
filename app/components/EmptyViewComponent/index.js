import React from "react";
// import Animated, { FadeInDown } from 'react-native-reanimated';
import { Text, View } from "react-native";
// import { images } from "@config/images";
import styles from "./styles";
import Button from "@components/Button";
import { translate } from "../../lang/Translate";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const { images } = require("@config/images");

const EmptyViewComponent = ({
  TitleText,
  modalDescription,
  imageStyle,
  buttonText,
  button,
  onBtnClick,
  imgSource = "",
}) => {
  return (
    <View style={styles.modalView}>
      <Animated.Image
        entering={FadeInDown}
        duration={7000}
        source={imgSource || images.leaveIcon}
        style={{ ...styles.centerImg, ...imageStyle }}
      />
      <Text style={styles.modalTitleText}>{translate(TitleText)}</Text>
      {modalDescription && (
        <Text style={styles.modalText}>{translate(modalDescription)}</Text>
      )}
      {button && (
        <Button style={{ width: "100%" }} onBtnClick={onBtnClick}>
          {translate(buttonText)}
        </Button>
      )}
    </View>
  );
};

export default EmptyViewComponent;
