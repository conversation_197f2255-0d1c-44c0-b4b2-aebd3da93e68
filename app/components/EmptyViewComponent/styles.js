import { Dimensions, StatusBar, StyleSheet } from 'react-native';
import { BaseColors } from '../../config/theme';

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  modalView: {
    margin: 20,
    backgroundColor: BaseColors.white,
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  centerImg: {
    width: '100%',
    height: 280,
    borderRadius: 10,
    // marginVertical: 24,
  },
  modalTitleText: {
    fontSize: 20,
    color: BaseColors.secondary,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    marginBottom: 15,
    marginTop: 12,
  },

  modalText: {
    marginBottom: 15,
    textAlign: 'center',
    color: BaseColors.gray10,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
});

export default styles;
