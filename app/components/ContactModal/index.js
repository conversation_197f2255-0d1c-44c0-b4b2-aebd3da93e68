import React from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";

const FastImage = require("react-native-fast-image");

const ContactInfoModal = ({
  visible,
  setModalVisible,

  listData = [],
}) => {
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };

  const DataDetail = ({ title, value }) => {
    return (
      <View style={{ marginBottom: 10 }}>
        <Text style={styles.titleTextStyle}>{title}</Text>
        <Text style={styles.valueTextStyle}>{value}</Text>
      </View>
    );
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Text style={styles.modalTitleText}>
                  {translate("Contact")}
                </Text>
                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    height: 24,
                    width: 24,
                    borderRadius: 5,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  activeOpacity={0.8}
                  onPress={() => setModalVisible(false)}
                >
                  <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                </TouchableOpacity>
              </View>
              <View style={{ marginBottom: 20 }}>
                <DataDetail
                  title={"Mobile No"}
                  value={`+${listData?.company_phone_code} ${listData?.company_phone_number}`}
                />
                <DataDetail title={"Email"} value={listData?.company_email} />
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default ContactInfoModal;
