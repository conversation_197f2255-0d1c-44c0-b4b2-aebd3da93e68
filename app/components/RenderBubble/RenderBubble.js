import { FlatList, Platform, Text, TouchableOpacity, View } from "react-native";
import React, { useRef, useState } from "react";
import dayjs from "dayjs";
import { BaseColors } from "@config/theme";
import styles from "./styles";
import FastImage from "react-native-fast-image";
import { CustomIcon } from "@config/LoadIcons";
import isEmpty from "lodash-es/isEmpty";
import truncate from "lodash-es/truncate";
import { Swipeable } from "react-native-gesture-handler";
import { FontFamily } from "@config/typography";
import { GiphyMediaView } from "@giphy/react-native-sdk";
import { isArray, startCase } from "lodash-es";
import { useDispatch, useSelector } from "react-redux";
import AuthActions from "@redux/reducers/auth/actions";
import CountdownTimer from "@components/CountdownTimer/CountdownTimer";
import Popover from "react-native-popover-view";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;

const timeStringToSeconds = (timeString) => {
  // Split the timeString into minutes and seconds
  const [minutes, seconds] = timeString.split(":").map(Number);

  // Calculate total seconds
  const totalSeconds = minutes * 60 + seconds;

  return totalSeconds;
};

const RenderBubble = ({
  props,
  onSwipe,
  onPlayAudio = () => {},
  isPlay,
  messagesArray,
  soundObj,
  setSoundObj,
  isCurrentPlaySound,
  navigation,
  onActionPress = () => {},
}) => {
  const { setSavedPostList, setSavedReelList } = AuthActions;

  const dispatch = useDispatch();

  // state
  const swipeableRef = useRef(null);
  const [showPopover, setShowPopover] = useState(false);
  const optionData = [
    { key: "edit", text: "Edit" },
    { key: "delete", text: "Delete" },
  ];

  const { userData } = useSelector((a) => a.auth);

  return (
    <View>
      {props?.currentMessage?.text &&
      props.currentMessage?.type !== "message_day" ? (
        <Swipeable
          ref={swipeableRef}
          renderRightActions={() => (
            <View
              style={{
                width:
                  props?.currentMessage?.user?._id === 0
                    ? props?.currentMessage?.messageType === "message" ||
                      props?.currentMessage?.messageType === "reply"
                      ? 0.5
                      : 0
                    : 0,
              }}
            ></View>
          )}
          renderLeftActions={() => (
            <View
              style={{
                width:
                  props?.currentMessage?.user?._id === 0
                    ? 0
                    : props?.currentMessage?.messageType === "message" ||
                        props?.currentMessage?.messageType === "reply"
                      ? 0.5
                      : 0,
              }}
            ></View>
          )}
          onSwipeableWillOpen={() => onSwipe(props)}
        >
          <View
            key={props?.currentMessage?._id}
            style={{
              ...styles.mainMsgView,
              justifyContent:
                props?.currentMessage?.user?._id === 0
                  ? "flex-end"
                  : "flex-start",
              alignItems:
                props?.currentMessage?.user?._id === 0
                  ? "flex-end"
                  : "flex-end",
            }}
          >
            {/* Design post message */}
            {props.currentMessage.messageType === "reel" ? (
              !isEmpty(props?.currentMessage.reelData) ? (
                <TouchableOpacity
                  style={styles.postMain}
                  onPress={() => {
                    const updatedReelList = {
                      page: 1,
                      next_enable: false,
                      data: [props?.currentMessage?.reelData],
                    };
                    dispatch(setSavedReelList(updatedReelList));
                    navigation.navigate("ViewReelForSave");
                  }}
                  activeOpacity={0.8}
                >
                  <View style={styles.imgAndText}>
                    <FastImage
                      source={{
                        uri: props?.currentMessage?.reelData?.user_data[0]
                          ?.user_dp,
                      }}
                      style={styles.userPostImg}
                    />
                    <Text style={styles.postText}>
                      {truncate(
                        props?.currentMessage?.reelData?.user_data[0]
                          ?.full_name,
                        {
                          length: 15,
                          omission: "...",
                        }
                      )}
                    </Text>
                  </View>
                  <View>
                    <FastImage
                      source={{
                        uri: props?.currentMessage?.reelData?.ReelData
                          ?.thumbnailData?.thumbUrl,
                      }}
                      style={styles.postMainImg}
                    />
                    <View style={styles.addReelIcon}>
                      <LottieView
                        autoSize={true}
                        source={images.reelAnim}
                        autoPlay={true}
                        loop
                        style={styles.reelAnimView}
                      />
                    </View>
                  </View>
                  <View>
                    <Text style={styles.postDescriptionText}>
                      {truncate(props?.currentMessage?.reelData?.description, {
                        length: 20,
                        omission: "...",
                      })}
                    </Text>
                  </View>
                </TouchableOpacity>
              ) : (
                <View>
                  <Text style={styles.storyUnavailableTextStyle}>
                    Reel not available
                  </Text>
                </View>
              )
            ) : props?.currentMessage?.messageType === "post" ? (
              !isEmpty(props?.currentMessage?.postData) ? (
                <TouchableOpacity
                  style={styles.postMain}
                  activeOpacity={0.8}
                  onPress={() => {
                    const updatedPostList = {
                      page: 1,
                      next_enable: false,
                      data: [props?.currentMessage?.postData],
                    };
                    dispatch(setSavedPostList(updatedPostList));
                    navigation.navigate("SavedPostList", {
                      data: [props?.currentMessage?.postData],
                    });
                  }}
                >
                  <View style={styles.imgAndText}>
                    <FastImage
                      source={{
                        uri: props?.currentMessage?.postData?.user_data[0]
                          ?.user_dp,
                      }}
                      style={styles.userPostImg}
                    />
                    <Text style={styles.postText}>
                      {truncate(
                        props?.currentMessage?.postData?.user_data[0]
                          ?.full_name,
                        {
                          length: 15,
                          omission: "...",
                        }
                      )}
                    </Text>
                  </View>
                  <View>
                    <FastImage
                      source={{
                        uri: props?.currentMessage?.postData?.ImageData[0]
                          ?.fileUrl,
                      }}
                      style={styles.postMainImg}
                    />
                  </View>
                  <View>
                    <Text style={styles.postDescriptionText}>
                      {truncate(props?.currentMessage?.postData?.description, {
                        length: 20,
                        omission: "...",
                      })}
                    </Text>
                  </View>
                </TouchableOpacity>
              ) : (
                <View>
                  <Text style={styles.storyUnavailableTextStyle}>
                    Post not available
                  </Text>
                </View>
              )
            ) : props.currentMessage.messageType === "profile" ? (
              !isEmpty(props?.currentMessage?.userData[0]) ? (
                <TouchableOpacity
                  style={styles.postMain}
                  activeOpacity={0.8}
                  onPress={() => {
                    if (
                      props?.currentMessage?.userData[0]?.user_id !==
                      userData?.user_id
                    ) {
                      navigation.navigate("ProfileNew", {
                        data: props?.currentMessage?.userData[0],
                        type: "anotherProfile",
                        screenName: "chatScreen",
                      });
                    } else {
                      navigation.navigate("ProfileNew", {
                        data: props?.currentMessage?.userData[0],

                        screenName: "chatScreen",
                      });
                    }
                  }}
                >
                  <View style={styles.imgAndText}>
                    <FastImage
                      source={{
                        uri: props?.currentMessage?.userData[0]?.user_dp,
                      }}
                      style={styles.userPostImg}
                    />
                    <Text style={styles.postText}>
                      {truncate(props?.currentMessage?.userData[0]?.full_name, {
                        length: 15,
                        omission: "...",
                      })}
                    </Text>
                  </View>
                  {props?.currentMessage?.userData[0]?.feedData?.length !==
                  0 ? (
                    <View style={{ flexDirection: "row", marginBottom: 20 }}>
                      {props?.currentMessage?.userData[0]?.feedData?.map(
                        (item, index) => {
                          return (
                            <View
                              style={{
                                marginLeft: 6,
                                marginRight:
                                  index ===
                                  props?.currentMessage?.userData[0]?.feedData
                                    ?.length -
                                    1
                                    ? 6
                                    : 0,
                              }}
                            >
                              <FastImage
                                source={{ uri: item?.posts }}
                                style={{
                                  height: 60,
                                  width:
                                    160 /
                                    props?.currentMessage?.userData[0]?.feedData
                                      ?.length,
                                  borderRadius: 8,
                                }}
                              />
                            </View>
                          );
                        }
                      )}
                    </View>
                  ) : null}
                </TouchableOpacity>
              ) : (
                <View>
                  <Text style={styles.storyUnavailableTextStyle}>
                    Profile not available
                  </Text>
                </View>
              )
            ) : props.currentMessage.messageType === "audioReply" ? (
              <View
                style={[
                  styles.mainReplayView,
                  {
                    backgroundColor:
                      props?.currentMessage?.user?._id === 0
                        ? BaseColors.lightblue
                        : BaseColors.gray8,
                  },
                ]}
              >
                {isArray(props?.currentMessage?.replies) ? (
                  <Text
                    style={[
                      styles.replayTitle,
                      {
                        textAlign:
                          props?.currentMessage?.user?._id === 0
                            ? "right"
                            : "left",
                      },
                    ]}
                  >
                    {props?.currentMessage?.replies[0]?.is_me
                      ? "You"
                      : props?.currentMessage?.replies[0]?.userData[0]
                            ?.full_name
                        ? startCase(
                            String(
                              props?.currentMessage?.replies[0]?.userData[0]
                                ?.full_name
                            )
                          )
                        : ""}
                  </Text>
                ) : null}
                {isArray(props?.currentMessage?.replies) ? (
                  <Text
                    style={[
                      styles.previousReplayText,
                      {
                        textAlign:
                          props?.currentMessage?.user?._id === 0
                            ? "right"
                            : "left",
                      },
                    ]}
                  >
                    {props?.currentMessage?.replies[0]?.message_content || ""}
                  </Text>
                ) : null}

                <View
                  style={[
                    styles.audioMainView,
                    {
                      flexDirection:
                        props?.currentMessage?.user?._id === 0
                          ? "row"
                          : "row-reverse",
                      backgroundColor:
                        props?.currentMessage?.user?._id === 0
                          ? BaseColors.lightblue
                          : BaseColors.gray8,
                    },
                  ]}
                >
                  <TouchableOpacity
                    style={styles.audioPlayButton}
                    activeOpacity={0.8}
                    onPress={() => {
                      onPlayAudio(props?.currentMessage?.text);
                    }}
                  >
                    <CustomIcon
                      name={
                        isCurrentPlaySound.isPlay &&
                        isCurrentPlaySound?.isCurrentUrl ===
                          props?.currentMessage?.text
                          ? "pause1"
                          : "BsPlayFill"
                      }
                      size={18}
                      color={BaseColors.white}
                    />
                  </TouchableOpacity>
                  <View
                    style={{
                      height: 50,
                      justifyContent: "center",
                    }}
                  >
                    <LottieView
                      source={images.voiceWave}
                      autoPlay={
                        isCurrentPlaySound.isPlay &&
                        isCurrentPlaySound?.isCurrentUrl ===
                          props?.currentMessage?.text
                      }
                      loop={
                        isCurrentPlaySound.isPlay &&
                        isCurrentPlaySound?.isCurrentUrl ===
                          props?.currentMessage?.text
                      }
                      style={styles.loader}
                    />
                  </View>
                  <View>
                    <Text
                      style={{
                        fontFamily: FontFamily.InterMedium,
                        color: BaseColors.black,
                      }}
                    >
                      {/* {audioDuration(props.currentMessage.audio)} */}
                      {props?.currentMessage?.duration}
                    </Text>
                  </View>
                </View>
              </View>
            ) : props?.currentMessage?.messageType === "audio" ? (
              <View
                style={[
                  styles.audioMainView,
                  {
                    flexDirection:
                      props?.currentMessage?.user?._id === 0
                        ? "row"
                        : "row-reverse",
                    backgroundColor:
                      props?.currentMessage?.user?._id === 0
                        ? BaseColors.lightblue
                        : BaseColors.gray8,
                  },
                ]}
              >
                <TouchableOpacity
                  style={styles.audioPlayButton}
                  activeOpacity={0.8}
                  onPress={() => {
                    onPlayAudio(props?.currentMessage?.text);
                  }}
                >
                  <CustomIcon
                    name={
                      isCurrentPlaySound.isPlay &&
                      isCurrentPlaySound?.isCurrentUrl ===
                        props?.currentMessage?.text
                        ? "pause1"
                        : "BsPlayFill"
                    }
                    size={18}
                    color={BaseColors.white}
                  />
                </TouchableOpacity>
                <View
                  style={{
                    height: 50,
                    justifyContent: "center",
                  }}
                >
                  <LottieView
                    source={images.voiceWave}
                    autoPlay={
                      isCurrentPlaySound.isPlay &&
                      isCurrentPlaySound?.isCurrentUrl ===
                        props?.currentMessage?.text
                    }
                    loop={
                      isCurrentPlaySound.isPlay &&
                      isCurrentPlaySound?.isCurrentUrl ===
                        props?.currentMessage?.text
                    }
                    style={styles.loader}
                  />
                </View>
                <View>
                  <Text
                    style={{
                      fontFamily: FontFamily.InterMedium,
                      color: BaseColors.black,
                    }}
                  >
                    {/* {audioDuration(props.currentMessage.audio)} */}

                    {/* {props?.currentMessage?.duration} */}
                    <CountdownTimer
                      duration={timeStringToSeconds(
                        props?.currentMessage?.duration
                      )}
                      startTimer={
                        isCurrentPlaySound.isPlay &&
                        isCurrentPlaySound?.isCurrentUrl ===
                          props?.currentMessage?.text
                          ? true
                          : false
                      }
                      selectedMsgId={isCurrentPlaySound?.isCurrentUrl}
                      renderingMsgId={props?.currentMessage?.text}
                    />
                  </Text>
                </View>
              </View>
            ) : props.currentMessage.messageType === "reply" &&
              isArray(props?.currentMessage?.replies) ? (
              <View
                style={[
                  styles.mainReplayView,
                  {
                    backgroundColor:
                      props?.currentMessage?.user?._id === 0
                        ? BaseColors.lightblue
                        : BaseColors.gray8,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.replayTitle,
                    {
                      textAlign:
                        props?.currentMessage?.user?._id === 0
                          ? "right"
                          : "left",
                    },
                  ]}
                >
                  {props?.currentMessage?.replies[0]?.is_me
                    ? "You"
                    : props?.currentMessage?.replies[0]?.userData[0]?.full_name
                      ? startCase(
                          String(
                            props?.currentMessage?.replies[0]?.userData[0]
                              ?.full_name
                          )
                        )
                      : ""}
                </Text>
                <View
                  style={{
                    borderBottomWidth: 0.5,
                    borderColor: BaseColors.gray6,
                  }}
                >
                  <Text
                    style={[
                      styles.previousReplayText,
                      {
                        textAlign:
                          props?.currentMessage?.user?._id === 0
                            ? "right"
                            : "left",
                      },
                    ]}
                  >
                    {props?.currentMessage?.replies[0]?.message_content}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.replayText,
                    {
                      textAlign:
                        props?.currentMessage?.user?._id === 0
                          ? "right"
                          : "left",
                    },
                  ]}
                >
                  {props?.currentMessage?.text}
                </Text>
              </View>
            ) : props.currentMessage.messageType === "stickerReply" ? (
              <View
                style={[
                  styles.mainReplayView,
                  {
                    backgroundColor:
                      props?.currentMessage?.user?._id === 0
                        ? BaseColors.lightblue
                        : BaseColors.gray8,
                  },
                ]}
              >
                {isArray(props?.currentMessage?.replies) ? (
                  <Text
                    style={[
                      styles.replayTitle,
                      {
                        textAlign:
                          props?.currentMessage?.user?._id === 0
                            ? "right"
                            : "left",
                      },
                    ]}
                  >
                    {props?.currentMessage?.replies[0]?.is_me
                      ? "You"
                      : props?.currentMessage?.replies[0]?.userData[0]
                            ?.full_name
                        ? startCase(
                            String(
                              props?.currentMessage?.replies[0]?.userData[0]
                                ?.full_name
                            )
                          )
                        : ""}
                  </Text>
                ) : null}
                {isArray(props?.currentMessage?.replies) ? (
                  <Text
                    style={[
                      styles.previousReplayText,
                      {
                        textAlign:
                          props?.currentMessage?.user?._id === 0
                            ? "right"
                            : "left",
                      },
                    ]}
                  >
                    {props?.currentMessage?.replies[0]?.message_content || ""}
                  </Text>
                ) : null}
                <Text
                  style={[
                    styles.replayText,
                    {
                      textAlign:
                        props?.currentMessage?.user?._id === 0
                          ? "right"
                          : "left",
                    },
                  ]}
                >
                  <GiphyMediaView
                    mediaId={props?.currentMessage?.text}
                    renditionType={
                      Platform.OS === "ios"
                        ? "downsized_still"
                        : "downsized_small"
                    }
                    resizeMode="contain"
                    style={{ width: 150, height: 150 }}
                  />
                </Text>
              </View>
            ) : props.currentMessage.messageType === "sticker" ? (
              <GiphyMediaView
                mediaId={props?.currentMessage?.text}
                renditionType={
                  Platform.OS === "ios" ? "downsized_still" : "downsized_small"
                }
                resizeMode="contain"
                style={{ width: 150, height: 150 }}
              />
            ) : props.currentMessage.messageType === "story" ? (
              !isEmpty(props?.currentMessage?.storyData) ? (
                <TouchableOpacity
                  style={[
                    styles.postMain,
                    {
                      padding: 12,
                      paddingHorizontal: 10,
                      backgroundColor: BaseColors.white,
                      shadowColor: BaseColors.gray,
                      shadowColor: "#000000",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.3,
                      shadowRadius: 1.41,
                      elevation: 12,
                    },
                  ]}
                  activeOpacity={0.8}
                  onPress={() =>
                    navigation.navigate("HighLightPreView", {
                      item: props?.currentMessage?.storyData,
                      userProfileName:
                        props?.currentMessage?.storyData?.userData[0]
                          ?.full_name,
                      userProfileImage:
                        props?.currentMessage?.storyData?.userData[0]?.user_dp,
                      type: "chatScreen",
                    })
                  }
                >
                  <View>
                    <FastImage
                      source={{
                        uri:
                          props?.currentMessage?.storyData?.type === "image"
                            ? props?.currentMessage?.storyData?.content_url
                            : props?.currentMessage?.storyData?.thubnails,
                      }}
                      style={[
                        styles.postMainImg,
                        { height: HEIGHT / 4, borderRadius: 10 },
                      ]}
                    />
                  </View>
                </TouchableOpacity>
              ) : (
                <View>
                  <Text style={styles.storyUnavailableTextStyle}>
                    Story unavailable
                  </Text>
                </View>
              )
            ) : props?.currentMessage?.messageType === "story_reply" ? (
              <>
                <View
                  style={{
                    position: "absolute",
                    top: 0,
                    left: props?.currentMessage?.user?._id === 0 ? null : 0,
                    right: props?.currentMessage?.user?._id === 0 ? 0 : null,
                  }}
                >
                  <View
                    style={{
                      alignItems:
                        props?.currentMessage?.user?._id === 0
                          ? "flex-end"
                          : "flex-start",
                      marginVertical: 10,
                    }}
                  >
                    <Text style={styles.storyUnavailableTextStyle}>
                      You replied to their story
                    </Text>
                  </View>
                  {!isEmpty(props?.currentMessage?.storyData) ? (
                    <View>
                      <TouchableOpacity
                        style={[
                          styles.postMain,
                          {
                            padding: 12,
                            paddingHorizontal: 10,
                            backgroundColor: BaseColors.white,
                            shadowColor: BaseColors.gray,
                            shadowColor: "#000000",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.3,
                            shadowRadius: 1.41,
                            elevation: 12,
                          },
                        ]}
                        activeOpacity={0.8}
                        onPress={() =>
                          navigation.navigate("HighLightPreView", {
                            item: props?.currentMessage?.storyData,
                            userProfileName:
                              props?.currentMessage?.storyData?.userData[0]
                                ?.full_name,
                            userProfileImage:
                              props?.currentMessage?.storyData?.userData[0]
                                ?.user_dp,
                            type: "chatScreen",
                          })
                        }
                      >
                        <View>
                          <FastImage
                            source={{
                              uri:
                                props?.currentMessage?.storyData?.type ===
                                "image"
                                  ? props?.currentMessage?.storyData
                                      ?.content_url
                                  : props?.currentMessage?.storyData?.thubnails,
                            }}
                            style={[
                              styles.postMainImg,
                              { height: HEIGHT / 4, borderRadius: 10 },
                            ]}
                          />
                        </View>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <View>
                      <Text style={styles.storyUnavailableTextStyle}>
                        Story unavailable
                      </Text>
                    </View>
                  )}
                </View>
                <View
                  style={[
                    styles.mainTextMsgView,
                    {
                      backgroundColor:
                        props?.currentMessage?.user?._id === 0
                          ? BaseColors.lightblue
                          : BaseColors.gray8,
                      marginTop: !isEmpty(props?.currentMessage?.storyData)
                        ? HEIGHT / 3
                        : HEIGHT / 13,
                    },
                  ]}
                >
                  <Text
                    style={{
                      ...styles.msgTxt,
                    }}
                  >
                    {props?.currentMessage?.text}
                  </Text>
                </View>
              </>
            ) : (
              <Popover
                from={
                  <TouchableOpacity
                    disabled={
                      userData?.user_id !== props?.currentMessage?.sender_id
                    }
                    style={[
                      styles.mainTextMsgView,
                      {
                        backgroundColor:
                          props?.currentMessage?.user?._id === 0
                            ? BaseColors.lightblue
                            : BaseColors.gray8,
                      },
                    ]}
                    activeOpacity={0.7}
                    onLongPress={() => setShowPopover(true)}
                  >
                    <Text
                      style={{
                        ...styles.msgTxt,
                      }}
                    >
                      {props?.currentMessage?.text}
                    </Text>
                    {props?.currentMessage?.is_edited === 1 && (
                      <Text
                        style={{
                          color: BaseColors.gray6,
                          marginTop: 5,
                          textAlign:
                            props?.currentMessage?.user?._id === 0
                              ? "right"
                              : "left",
                        }}
                      >
                        edited
                      </Text>
                    )}
                  </TouchableOpacity>
                }
                popoverStyle={styles.popoverStyle}
                isVisible={showPopover}
                onRequestClose={() => setShowPopover(false)}
                popoverArrow={false}
                children={
                  <FlatList
                    data={optionData || []}
                    renderItem={({ item, index }) => (
                      <TouchableOpacity
                        style={[
                          styles.mainPopData,
                          {
                            borderBottomWidth:
                              index === optionData?.length - 1 ? 0 : 1,
                          },
                        ]}
                        activeOpacity={0.8}
                        onPress={() => {
                          setShowPopover(false);
                          onActionPress(item.key, props?.currentMessage);
                        }}
                      >
                        <Text
                          style={{
                            color:
                              item.key === "delete"
                                ? BaseColors.activeTab
                                : BaseColors.black,
                            fontSize: 16,
                            fontFamily: FontFamily.InterMedium,
                          }}
                        >
                          {item.text}
                        </Text>
                      </TouchableOpacity>
                    )}
                  />
                }
              ></Popover>
            )}

            {/* render Timer and tick */}
            <View style={styles.timerMain}>
              <Text style={styles.timerText}>
                {/* {dayjs(props.currentMessage.createdAt).format("hh:mm A")} */}
                {props.currentMessage.createdAt}
              </Text>

              {props?.currentMessage?.user?._id === 0 ? (
                <View style={{ marginLeft: 5 }}>
                  <View style={{ flexDirection: "row" }}>
                    <CustomIcon
                      name={
                        props?.currentMessage?.is_read === "1" ||
                        props?.currentMessage?.is_read === "0"
                          ? "two-tic"
                          : props?.currentMessage?.is_read === "1" ||
                              props?.currentMessage?.is_read === "0"
                            ? "two-tic"
                            : "tick"
                      }
                      size={
                        props?.currentMessage?.is_read === "1" ||
                        props?.currentMessage?.is_read === "0"
                          ? 16
                          : 11
                      }
                      color={
                        props?.currentMessage?.is_read === "1"
                          ? BaseColors.blueTik
                          : BaseColors.gray6
                      }
                    />
                  </View>
                </View>
              ) : null}
            </View>
          </View>
        </Swipeable>
      ) : props.currentMessage?.type === "message_day" ? (
        <View style={{ width: "100%" }}>
          <Text style={styles.messageTime}>
            {startCase(String(props?.currentMessage?.message_day))}
          </Text>
        </View>
      ) : null}
    </View>
  );
};

export default RenderBubble;
