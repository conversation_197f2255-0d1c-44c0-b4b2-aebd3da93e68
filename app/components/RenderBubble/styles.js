import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

const WIDTH = Dimensions.get("window").width;
const HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  mainMsgView: {
    marginHorizontal: 16,
    paddingTop: 5,
    paddingBottom: 5,
    flex: 1,
    flexDirection: "row",
    marginBottom: 20,
  },
  messageTime: {
    color: BaseColors.black,
    textAlign: "center",
    fontFamily: FontFamily.InterSemiBold,
    fontSize: 14,
  },
  replayTitle: {
    paddingHorizontal: 15,
    paddingTop: 15,
    color: BaseColors.black,
    fontSize: 14,
    fontFamily: FontFamily.InterBold,
    paddingBottom: 5,
  },
  mainTextMsgView: {
    borderRadius: 10,
    padding: 10,
    maxWidth: "85%",
  },
  replayText: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    color: BaseColors.black,
    fontSize: 15,
    fontFamily: FontFamily.InterMedium,
  },
  previousReplayText: {
    paddingBottom: 10,
    color: BaseColors.gray6,
    fontSize: 14,

    marginLeft: 15,
    paddingRight: 15,
  },
  mainReplayView: {
    maxWidth: "75%",
    borderRadius: 10,
    minWidth: WIDTH / 2.4,
  },
  timerMain: {
    position: "absolute",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    bottom: -12,
  },
  postText: {
    color: BaseColors.black,
    fontSize: 15,
    fontFamily: FontFamily.InterBold,
    marginLeft: 3,
  },
  timerText: {
    color: BaseColors.black,
    fontSize: 10.5,
  },
  msgTxt: {
    fontSize: 15,
    fontFamily: FontFamily.InterMedium,
    color: BaseColors.black,
  },
  postMain: {
    borderRadius: 10,
    minWidth: WIDTH / 2.4,

    gap: 5,
    justifyContent: "center",
    // alignItems: "center",

    backgroundColor: BaseColors.lightblue,
  },
  imgAndText: {
    flexDirection: "row",
    paddingHorizontal: 10,
    paddingVertical: 5,

    gap: 5,
    alignItems: "center",
  },
  userPostImg: {
    height: HEIGHT / 22,
    width: HEIGHT / 22,
    borderRadius: 25,
  },
  postMainImg: {
    height: HEIGHT / 5,
  },
  postDescriptionText: {
    color: BaseColors.black,
    fontSize: 13,
    fontFamily: FontFamily.InterBold,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  loader: {
    height: 100,
    width: WIDTH / 4,
  },
  main: {
    flex: 1,
  },
  audioPlayButton: {
    justifyContent: "center",
    backgroundColor: BaseColors.activeTab,
    borderRadius: 25,
    alignItems: "center",
    height: 31,
    width: 31,
  },
  addReelIcon: {
    paddingRight: 5,
    paddingTop: 5,
    position: "absolute",
    right: -15,
    top: -15,
  },
  reelAnimView: {
    height: 60,
    width: 60,
  },
  storyUnavailableTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoRegular,
    color: "#898989",
  },
  audioMainView: {
    borderRadius: 10,
    minWidth: WIDTH / 2.4,
    paddingHorizontal: 15,
    gap: 5,
    alignItems: "center",
    justifyContent: "flex-end",
  },
  mainPopData: {
    borderColor: BaseColors.gray,
    flexDirection: "row",
    gap: 8,
    paddingVertical: 6,
  },
  popoverStyle: {
    borderWidth: 1,
    width: 150,
    borderRadius: 8,
    marginRight: 12,
    padding: 15,
    borderColor: BaseColors.gray,
  },
});

export default styles;
