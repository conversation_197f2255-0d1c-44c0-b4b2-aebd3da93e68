import { TouchableOpacity, View } from "react-native";
import React, { useMemo } from "react";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import styles from "./style";
// import FastImage from "react-native-fast-image";
// import { images } from "@config/images";
// import Animated, {
//   useAnimatedStyle,
//   withSpring,
// } from "react-native-reanimated";

const Animated = require("react-native-reanimated").default;
const useAnimatedStyle = require("react-native-reanimated").useAnimatedStyle;
const withSpring = require("react-native-reanimated").withSpring;
const { images } = require("@config/images");
const FastImage = require("react-native-fast-image");

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const CameraButton = ({
  onPress,
  onLongPress,
  onPressOut,
  swapCameraButton = () => {},
  cameraTouch = false,
  setCameraTouch = () => {},
  setIsCameraOpen = () => {},
  setFlash = () => {},
  flash = "off",
  scaleValue,
  recordVideo,
}) => {
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scaleValue.value }],
    };
  });

  const handleLongPressIn = () => {
    onLongPress();
    scaleValue.value = withSpring(1.2);
  };

  const handleLongPressOut = () => {
    scaleValue.value = withSpring(1);
    onPressOut();
  };

  return (
    <>
      <View style={styles.mainUpView}>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.buttonView}
          onPress={() => setIsCameraOpen(false)}
        >
          <CustomIcon name={"BsX"} size={40} color={"#fff"} />
        </TouchableOpacity>

        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.cameraChangeBtn}
          onPress={() => {
            if (flash === "off") {
              setCameraTouch(false);
              setFlash("on");
            } else {
              setCameraTouch(true);
              setFlash("off");
            }
          }}
        >
          <FastImage
            source={cameraTouch ? images.noFlash : images.flash}
            style={{
              width: 25,
              height: 25,
            }}
            tintColor={BaseColors.white}
          />
        </TouchableOpacity>
      </View>
      {/* Bottom Design */}
      <View style={styles.mainBotView}>
        <View style={styles.buttonView} />
        <View style={styles.mainRoundButton}>
          <AnimatedTouchableOpacity
            activeOpacity={0.8}
            onLongPress={recordVideo ? () => handleLongPressIn() : null}
            onPressOut={recordVideo ? () => handleLongPressOut() : null}
            onPress={onPress}
            style={[styles.roundBtn, animatedStyle]}
          >
            <View style={styles.underRoundBtn} />
          </AnimatedTouchableOpacity>
        </View>

        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.cameraChangeBtn}
          onPress={() => swapCameraButton()}
        >
          <FastImage
            source={images.swap2}
            style={{
              width: 24,
              height: 24,
            }}
          />
        </TouchableOpacity>
      </View>
    </>
  );
};

export default React.memo(CameraButton);
