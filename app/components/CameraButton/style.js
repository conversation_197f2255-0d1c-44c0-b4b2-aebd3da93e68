import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";
import { isIPhoneX } from "react-native-status-bar-height";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  mainBotView: {
    alignItems: "center",
    position: "absolute",
    bottom: 30,
    justifyContent: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    zIndex: 999,
  },
  mainUpView: {
    alignItems: "center",
    position: "absolute",
    top: 10,
    justifyContent: "center",
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  buttonView: {
    width: "20%",
    justifyContent: "center",
    alignItems: "center",
  },
  mainRoundButton: {
    width: "60%",
    justifyContent: "center",
    alignItems: "center",
  },
  timerMainRoundButton: {
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    top: isIPhoneX() ? 20 : 45,
    zIndex: 999,
    alignSelf: "center",
    backgroundColor: BaseColors.activeTab,
    borderRadius: 20,
  },
  timerText: {
    fontSize: 15,
    color: BaseColors.white,
    fontFamily: FontFamily.RobotoRegular,
    fontWeight: "600",
    minWidth: 70,
    paddingVertical: 5,
    textAlign: "center",
  },
  roundBtn: {
    height: 80,
    width: 80,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BaseColors.White,
  },
  underRoundBtn: {
    height: 70,
    width: 70,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: BaseColors.black,
  },
  cameraChangeBtn: {
    width: "20%",
    justifyContent: "center",
    alignItems: "center",
  },
});
export default styles;
