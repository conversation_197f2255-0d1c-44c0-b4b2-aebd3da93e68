import React, { useRef, useState, useEffect, memo, useCallback } from "react";
import CameraButton from "@components/CameraButton/CameraButton";
import styles from "./style";
import { Platform, Text, View } from "react-native";
// import { useSharedValue, withSpring } from "react-native-reanimated";
import Toast from "react-native-simple-toast";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { runOnJS } from "react-native-reanimated";

const { Camera, useCameraDevice, useCameraFormat } = require("react-native-vision-camera");
const useSharedValue = require("react-native-reanimated").useSharedValue;
const withSpring = require("react-native-reanimated").withSpring;
const RNFS = require("react-native-fs");

const formatDuration = (durationInSeconds) => {
  const minutes = Math.floor(durationInSeconds / 60)
    .toString()
    .padStart(2, "0");
  const seconds = (durationInSeconds % 60).toString().padStart(2, "0");
  return `${minutes}:${seconds}`;
};

const RenderTimer = ({
  isRecording,
  stopRecording = () => {},
  videoDuration,
  isCameraOpen,
}) => {
  const [timer, setTimer] = useState(-1);

  useEffect(() => {
    let interval;
    if (isRecording) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRecording]);

  useEffect(() => {
    if (timer === videoDuration) {
      stopRecording();
    }
  }, [timer]);
  useEffect(() => {
    if (isCameraOpen && timer > -1) {
      setTimer(-1);
    }
  }, [isCameraOpen]);

  return (
    <>
      {timer >= 0 ? (
        <View style={styles.timerMainRoundButton}>
          <Text style={styles.timerText}>{`${formatDuration(timer)}`}</Text>
        </View>
      ) : null}
    </>
  );
};

const RenderTimeMemo = memo(RenderTimer);

const CCamera = ({
  setImagePath = () => {},
  setVideo = () => {},
  videoDuration = 30,
  isCameraOpen,
  setIsCameraOpen = () => {},
  recordVideo = true,
  ClickPhoto = true,
}) => {
  const cameraRef = useRef(null);
  const [cameraDevice, setCameraDevice] = useState("back");
  const device = useCameraDevice(cameraDevice);
  const formatForAndroid = useCameraFormat(device, [
      { 
        photoResolution: 'max'
      },
      {
        fps: 'max',
      },
      {
        iso: 'max'
      },
  ])
  const formatForIOS = useCameraFormat(device, [
    {
      fps: 20,
    },
    {
      iso: 'max'
    },
])
  const [isRecording, setIsRecording] = useState(false);
  const [cameraTouch, setCameraTouch] = useState(true);
  const [flash, setFlash] = useState("off");

  const handleOpenCamera = async () => {
    const hasCameraPermission = await Camera.requestCameraPermission();
    const hasMicrophonePermission = await Camera.requestMicrophonePermission();
    if (hasCameraPermission || hasMicrophonePermission) {
      setImagePath(null);
      setVideo(null);
    }
  };

  useEffect(() => {
    handleOpenCamera();
  }, []);
  

  const startRecording = async () => {
    if (cameraRef.current && !isRecording) {
      // Ensure no ongoing recording
      setIsRecording(true);

      try {
        await cameraRef.current.startRecording({
          flash: flash,
          onRecordingFinished: async (video) => {
            const { path } = video;
            try {
              const stats = await RNFS.stat(path);
              const fileSizeInBytes = stats?.size;
              const lastIndex = path.lastIndexOf("/");
              const lastName = path.substring(lastIndex + 1);

              setVideo({
                name: lastName,
                size: stats?.size,
                type: "video/mov",
                uri: `file://${path}`,
              });
              setIsCameraOpen(false);
            } catch (error) {
              console.error("Error getting image size:", error);
            }
            setIsRecording(false); // Stop recording state after finishing
            setIsCameraOpen(false);
          },
          onRecordingError: (error) => {
            console.error("Error recording video:", error);
            setIsRecording(false); // Stop recording state on error
          },
        });
      } catch (error) {
        console.error("Error starting recording:", error);
        setIsRecording(false); // Stop recording state on error
      }
    }
  };

  const stopRecording = async () => {
    if (cameraRef.current && isRecording) {
      try {
        await cameraRef.current.stopRecording();
      } catch (error) {
        console.error("Error stopping recording:", error);
      }
      setIsRecording(false);
    }
  };

  const takePicture = async () => {
    if (cameraRef.current && !isRecording) {
      const photo = await cameraRef.current.takePhoto({
        flash: flash,
      });
      const { path } = photo;

      try {
        const stats = await RNFS.stat(path);
        const lastIndex = path.lastIndexOf("/");
        const lastName = path.substring(lastIndex + 1);

        setImagePath({
          name: lastName,
          size: stats?.size,
          type: "image/jpeg",
          uri: `file://${path}`,
        });
        setIsCameraOpen(false);
        // Log the size of the image file in bytes
      } catch (error) {
        console.error("Error getting image size:", error);
      }
    }
  };

  const scaleValue = useSharedValue(1);

  const handleLongPress = () => {
    scaleValue.value = withSpring(1.2);
    if (!isRecording) {
      startRecording();
    }
  };

  const handlePressOut = () => {
    if (isRecording) {
      stopRecording();
    }
  };

  const handlePress = () => {
    if (ClickPhoto) {
      takePicture();
    } else {
      Toast.show("Hold button to record a video");
    }
  };

  const focus = useCallback((point) => {
    const c = cameraRef.current
    if (c == null) return
    c.focus(point)
  }, [])

  const gesture = Gesture.Tap()
    .onEnd(({ x, y }) => {
      runOnJS(focus)({ x, y })
    })

  return (
    <>
      {isCameraOpen ? (
        <RenderTimeMemo
          stopRecording={stopRecording}
          videoDuration={videoDuration}
          isRecording={isRecording}
          isCameraOpen={isCameraOpen}
        />
      ) : null}

      {isCameraOpen && (
          <GestureDetector gesture={gesture}>
            <Camera
              ref={cameraRef}
              style={{ 
                flex: 1,
              }}
              isActive={isCameraOpen}
              photo={true}
              video={true}
              device={device}
              enableZoomGesture={true}
              audio={true}
              enableHighQualityPhotos
              format={Platform.OS === 'ios' ? formatForIOS : formatForAndroid}
            />
          </GestureDetector>
      )}

      {isCameraOpen ? (
        <CameraButton
          onPress={() => handlePress()}
          onLongPress={recordVideo ? handleLongPress : null}
          onPressOut={recordVideo ? handlePressOut : null}
          swapCameraButton={
            cameraDevice === "back"
              ? () => setCameraDevice("front")
              : () => setCameraDevice("back")
          }
          setCameraTouch={setCameraTouch}
          cameraTouch={cameraTouch}
          setIsCameraOpen={setIsCameraOpen}
          setFlash={setFlash}
          flash={flash}
          videoDuration={videoDuration}
          scaleValue={scaleValue}
          recordVideo={recordVideo}
        />
      ) : null}
    </>
  );
};

export default memo(CCamera);
