/* eslint-disable react-native/no-inline-styles */
// Import React and Component
import { translate } from "../lang/Translate";
import React from "react";
import { View, StyleSheet, TouchableOpacity } from "react-native";
// import Animated, {
//   FadeInDown,
//   FadeInLeft,
//   FadeInRight,
// } from "react-native-reanimated";
import { isIPhoneX } from "react-native-status-bar-height";
// import FastImage from "react-native-fast-image";
// import { images } from "@config/images";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const FadeInLeft = require("react-native-reanimated").FadeInLeft;
const FadeInRight = require("react-native-reanimated").FadeInRight;
const { images } = require("@config/images");
const FastImage = require("react-native-fast-image");

const isIphoneCurve = isIPhoneX();

const NavigationDrawerHeader = (props) => {
  const {
    title,
    navigationProps,
    logo = true,
    showSearch = false,
    showCam = false,
    showNotification = false,
    handleSearch = () => {},
    handleNotification = () => {},
    notificationCount = 0,
  } = props;
  return (
    <Animated.View
      entering={FadeInDown}
      exiting={FadeInLeft}
      style={styles.mainView}
    >
      <TouchableOpacity
        onPress={() => navigationProps.toggleDrawer()}
        activeOpacity={0.8}
        style={styles.menuView}
      >
        <FastImage
          source={images.menu}
          style={{
            width: 24,
            height: 24,
          }}
        />
      </TouchableOpacity>
      <View
        style={{
          ...styles.textContainer,
        }}
      >
        {logo && (
          <FastImage
            source={images.footMainLogo}
            style={{
              width: 75,
              height: 34,
            }}
            resizeMode="contain"
          />
        )}
        {!logo && (
          <Animated.Text
            entering={FadeInRight}
            exiting={FadeInLeft}
            style={{ ...styles.titleText }}
          >
            {translate(title)}
          </Animated.Text>
        )}
      </View>
      <View style={styles.rightIconView}>
        {showSearch && (
          <TouchableOpacity activeOpacity={0.8} onPress={handleSearch}>
            <CustomIcon
              name={"Search-1"}
              size={24}
              color={BaseColors.headerIcon}
            />
          </TouchableOpacity>
        )}
        {showCam && (
          <TouchableOpacity activeOpacity={0.8}>
            <CustomIcon
              name={"BsCamera"}
              size={24}
              color={BaseColors.headerIcon}
              style={styles.bellIcon}
            />
          </TouchableOpacity>
        )}
        {showNotification && (
          <TouchableOpacity activeOpacity={0.8} onPress={handleNotification}>
            <CustomIcon
              name={"Notification"}
              size={24}
              color={BaseColors.headerIcon}
              style={styles.bellIcon}
            />
            {notificationCount > 0 ? (
              <View
                style={{
                  backgroundColor: BaseColors.activeTab,
                  height: 9,
                  width: 9,
                  position: "absolute",
                  borderRadius: 25,
                  right: 1,
                  top: -3,
                }}
              />
            ) : null}
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  mainView: {
    justifyContent: "center",
  },
  textContainer: {
    alignItems: "center",
    flexDirection: "row",
    gap: 10,
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: "#fff",
    marginTop: 0,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(26,26,26,0.1)",
    justifyContent: "center",
  },
  titleText: {
    fontSize: 16,
    fontFamily: "Inter-SemiBold",
    color: "#333",
  },
  menuView: {
    position: "absolute",
    left: 20,
    top: 0,
    bottom: 0,
    zIndex: 1,
    justifyContent: "center",
  },
  rightIconView: {
    position: "absolute",
    right: 20,
    top: 0,
    bottom: 0,
    zIndex: 1,
    justifyContent: "center",
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  menuImg: {
    width: 30,
    height: 30,
  },
  bellIcon: {
    marginLeft: 15,
  },
});

export default React.memo(NavigationDrawerHeader);
