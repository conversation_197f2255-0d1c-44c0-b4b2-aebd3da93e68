/* eslint-disable react-native/no-inline-styles */
import React from "react";
import { Platform, SafeAreaView } from "react-native";
import styles from "./styles";
import { GiphyGridView, GiphyRendition } from "@giphy/react-native-sdk";

export default function CSticker({ onSelect = () => {} }) {
  return (
    <SafeAreaView style={styles.main}>
      <GiphyGridView
        cellPadding={6}
        content={{
          mediaType: "sticker",
          requestType: "trending",
          rating: "g",
          searchQuery: "",
        }}
        style={{ height: 500 }}
        renditionType={
          Platform.OS === "ios" ? "downsized_still" : "downsized_small"
        }
        spanCount={4}
        onMediaSelect={(e) => {
          onSelect(e?.nativeEvent?.media?.data?.id);
        }}
      />
    </SafeAreaView>
  );
}
