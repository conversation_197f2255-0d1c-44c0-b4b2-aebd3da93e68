import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";
import { isIPhoneX } from "react-native-status-bar-height";

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalView: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
    maxHeight: Dimensions.get("window").height / 2,
    paddingBottom: isIPhoneX() ? 40 : 20
  },
  modalTitleText: {
    fontSize: 24,
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginBottom: 15,
    textAlignVertical: "center",
  },
  getReferralCodeTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.gray4,
  },
  dataMainViewStyle: {
    marginTop: 47,
    alignItems: "center",
  },
  roundedTextInput: {
    height: 50,
    width: 50,
    borderTopWidth: 0.66,
    borderBottomWidth: 0.86,
    borderLeftWidth: 0.86,
    borderRightWidth: 0.86,
    borderRadius: 5,
    fontSize: 23,
    borderColor: "#8E8383",
    backgroundColor: BaseColors.White,
  },
  btnView: {
    // flex: 1,
    marginTop: 33,
    width: "99%",
  },
  dontHaveTextStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 16,
    color: BaseColors.fontBlueColor,
    textAlign: "center",
  },
  dontHaveViewStyle: {
    marginVertical: 10,
  },
  errorTxt: {
    color: "#D6002E",
    textAlign: "left",
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
  },
  errorMsgView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
});

export default styles;
