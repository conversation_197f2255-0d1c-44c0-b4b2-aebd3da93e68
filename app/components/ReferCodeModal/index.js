import React, { useRef, useState } from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import OTPTextView from "react-native-otp-textinput";
import CButton from "@components/CButton";
import { useDispatch } from "react-redux";
import authAction from "@redux/reducers/auth/actions";

const { setIsReferralCode } = authAction;

const ReferCodeModal = ({
  visible,
  setModalVisible,
  setReferralCode,
  referralValue,
  onSubmit = () => {},
  isError = "",
  isLoading = false,
}) => {
  const dispatch = useDispatch();

  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
      dispatch(setIsReferralCode(true));
    }
  };

  const otpTextInputRef = useRef(null);

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
        dispatch(setIsReferralCode(true));
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Text style={styles.modalTitleText}>
                  {translate("referralCode")}
                </Text>

                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    height: 24,
                    width: 24,
                    borderRadius: 5,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  activeOpacity={0.8}
                  onPress={() => {
                    setModalVisible(false);
                    dispatch(setIsReferralCode(true));
                  }}
                >
                  <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                </TouchableOpacity>
              </View>
              <View style={styles.dataMainViewStyle}>
                <Text style={styles.getReferralCodeTextStyle}>
                  {translate("getReferralCodeText")}
                </Text>
                <OTPTextView
                  containerStyle={styles.textInputContainer}
                  textInputStyle={[
                    styles.roundedTextInput,
                    {
                      backgroundColor: BaseColors.white,
                      color: isError ? "#D6002E" : BaseColors.fontColor,
                      borderColor: isError ? "#D6002E" : BaseColors.gray10,
                    },
                  ]}
                  tintColor={isError ? "#D6002E" : BaseColors.gray}
                  offTintColor={isError ? "#D6002E" : BaseColors.gray}
                  inputCount={4}
                  inputCellLength={1}
                  autoFocus={false}
                  handleTextChange={setReferralCode}
                  handleCellTextChange={() => (isError = "")}
                  ref={otpTextInputRef}
                />
                {isError ? (
                  <View style={styles.errorMsgView}>
                    <CustomIcon
                      name={"BsExclamationCircle"}
                      size={18}
                      color={"#D6002E"}
                    />
                    <Text style={styles.errorTxt}>{translate(isError)}</Text>
                  </View>
                ) : null}
                <View style={styles.btnView}>
                  <CButton onBtnClick={onSubmit} loading={isLoading}>
                    {translate("submitText")}
                  </CButton>
                </View>
                <View>
                  <TouchableOpacity
                    style={styles.dontHaveViewStyle}
                    activeOpacity={0.8}
                    onPress={() => {
                      setModalVisible(false);
                      dispatch(setIsReferralCode(true));
                    }}
                  >
                    <Text style={styles.dontHaveTextStyle}>
                      Don't have code
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default ReferCodeModal;
