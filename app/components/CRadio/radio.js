/* eslint-disable react-native/no-inline-styles */
// Import React and Component
import React from "react";
import { View } from "react-native";
// import { images } from "@config/images";
// import Animated from 'react-native-reanimated';
import { Text } from "react-native";
import { Image } from "react-native";
import { TouchableOpacity } from "react-native";
import styles from "./styles";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";

const Animated = require("react-native-reanimated").default;
const { images } = require("@config/images");

const CRadio = (props) => {
  const {
    title,
    desc,
    tag,
    onClick,
    selected,
    id,
    radioStyle,
    icon,
    titleStyle,
    checkBox,
    noBorder,
  } = props;

  return (
    <Animated.View>
      <TouchableOpacity
        onPress={() => onClick()}
        activeOpacity={0.9}
        style={
          noBorder
            ? { ...styles.noBorderMain, ...radioStyle }
            : {
                backgroundColor:
                  id === selected && id !== undefined && selected !== undefined
                    ? "#EAFFFC"
                    : "#FDFDFD",
                borderColor:
                  id === selected && id !== undefined && selected !== undefined
                    ? BaseColors.primary
                    : "#E4E4E7",
                ...styles.mainBody,
                ...radioStyle,
              }
        }
      >
        <View
          style={{
            paddingRight: 10,
            width: "90%",
            display: "flex",
            flex: 1,
            flexDirection: "row",
          }}
        >
          {icon && (
            <CustomIcon
              name={icon}
              size={24}
              color={
                id === selected && id !== undefined && selected !== undefined
                  ? BaseColors.primary
                  : BaseColors.primary
              }
              style={{
                marginRight: 8,
              }}
            />
          )}
          <View style={{ paddingRight: 10, width: "90%" }}>
            <View style={{ flexDirection: "row", gap: 5 }}>
              <Text
                style={{
                  color:
                    icon &&
                    id === selected &&
                    id !== undefined &&
                    selected !== undefined
                      ? BaseColors.primary
                      : BaseColors.textInputLabel,
                  ...styles.title,
                  ...titleStyle,
                }}
              >
                {title}
              </Text>
              {tag && (
                <Text
                  style={
                    (styles.desc,
                    {
                      justifyContent: "center",
                      alignItems: "center",
                      padding: 5,
                      fontSize: 11,
                      backgroundColor: "#F5F6F8",
                      color: BaseColors.textSecondary,
                      borderRadius: 4,
                    })
                  }
                >
                  {tag}
                </Text>
              )}
            </View>
            {desc && <Text style={styles.desc}>{desc}</Text>}
          </View>
        </View>
        <View
          style={{
            ...styles.checkIconView,
            backgroundColor:
              id === selected && id !== undefined && selected !== undefined
                ? BaseColors.primary
                : "#FDFDFD",
            borderColor:
              id === selected && id !== undefined && selected !== undefined
                ? BaseColors.primary
                : "#E4E4E7",
            width: icon ? 20 : 24,
            height: icon ? 20 : 24,
            borderRadius: checkBox ? 6 : 12,
          }}
        >
          {id === selected && id !== undefined && selected !== undefined && (
            <Image
              tintColor={BaseColors.white}
              source={images.checkIcon}
              style={styles.checkIconImg}
            />
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};
export default CRadio;
