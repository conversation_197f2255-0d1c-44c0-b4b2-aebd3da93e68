import { Platform, StyleSheet } from 'react-native';
import { isIPhoneX } from 'react-native-status-bar-height';

const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  mainBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginVertical: 6,
  },
  noBorderMain: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  desc: {
    color: 'rgba(26,26,26,0.80)',
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  checkIconView: {
    borderRadius: 14,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkIconImg: {
    width: 12,
    height: 12,
  },
});
