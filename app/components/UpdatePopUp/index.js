import { BaseColors } from "../../config/theme";
import { translate } from "../../lang/Translate";
import React, { useCallback, useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Modal } from "react-native";
import codePush from "react-native-code-push";

const UpdatePopup = ({
  state,
  setState,
  loader,
  setUpdateLoader,
  updateProgress,
  setUpdateProgress,
  isAppUpdate,
}) => {

  const onClickUpdate = async () => {
    setUpdateLoader(true);
    await codePush?.sync(
      { installMode: codePush?.InstallMode?.IMMEDIATE },
      (status) => {
            
          try {
          if (status === codePush?.SyncStatus?.DOWNLOADING_PACKAGE) {
            
            setUpdateLoader(true);
          } else if (status === codePush?.SyncStatus?.INSTALLING_UPDATE) {
            
            setUpdateLoader(true);
            setUpdateProgress(100);
          } else if (status === codePush?.SyncStatus?.UP_TO_DATE) {
            
            setState({
              ...state,
              isCheckUpdate: false,
            });
            setUpdateLoader(false);
          }
        } catch (error) {
          
          console.log("🚀 ~ onClickUpdate ~ error:", error);
        }
      },
      (progress) => {
        console.log("🚀 ~ onClickUpdate ~ progress:", progress)
        try {
          if (progress) {
            const progresses = Math.round(
              (Number(progress?.receivedBytes) / Number(progress?.totalBytes)) * 100
            );
            console.log("🚀 ~ onClickUpdate ~ progresses:", progresses)
            setUpdateProgress(progresses);
          }
        } catch (error) {
          console.log("🚀 ~ onClickUpdate ~ error:", error);
        }
      }
    );
  };

  const onClickClose = () => {
    setState({
      ...state,
      isCheckUpdate: false,
    });
    setUpdateLoader(false);
  };
  
  return (
    <Modal transparent={true} animationType={"none"} visible={!isAppUpdate && state?.isCheckUpdate}>
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          <Text style={styles.title}>{translate("Update Available")}</Text>
          <Text style={styles.message}>{state?.message}</Text>
          <View
            style={{ flexDirection: "row", justifyContent: "center", gap: 10 }}
          >
            {!state?.isMandatory ? (
              <TouchableOpacity
                onPress={() => {onClickClose()}}
                activeOpacity={0.9}
                style={[
                  styles.btnStyle,
                  {
                    backgroundColor: BaseColors.white,
                    borderWidth: 1,
                    borderColor: BaseColors.primary,
                  },
                ]}
                disable={loader}
              >
                <Text
                  style={[
                    styles.modalButtonText,
                    { color: BaseColors.primary },
                  ]}
                >
                  {translate("cancelText")}
                </Text>
              </TouchableOpacity>
            ) : null}
            <TouchableOpacity
              onPress={() => {onClickUpdate()}}
              activeOpacity={0.9}
              style={styles.btnStyle}
              disable={loader}
            >
              <Text style={styles.modalButtonText}>
                {loader && Number(updateProgress) < 1
                  ? 'Update preparing'
                  : loader && Number(updateProgress) > 0 && Number(updateProgress) < 100
                    ? `Progress ${Number(updateProgress)}%`
                    : loader && Number(updateProgress) > 99
                      ? 'Installing'
                      : 'Update'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  // Define your styles here
  modalBackground: {
    flex: 1,
    alignItems: "center",
    flexDirection: "column",
    justifyContent: "space-around",
    backgroundColor: "#00000040",
    padding: 50,
  },
  activityIndicatorWrapper: {
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-around",
    padding: 20,
  },
  activityIndicator: {
    alignItems: "center",
    height: 80,
  },
  title: {
    color: BaseColors.primary,
    fontSize: 18,
    fontFamily: "Inter-SemiBold",
    padding: 6,
    textAlign: "center",
  },
  btnStyle: {
    borderRadius: 10,
    backgroundColor: BaseColors.primary,
    marginTop: 10,
    // width: "100%",
  },
  modalButtonText: {
    color: BaseColors.white,
    fontSize: 16,
    fontFamily: "Inter-SemiBold",
    padding: 6,
    textAlign: "center",
    paddingHorizontal: 18,
    textTransform: "capitalize",
  },
  message: {
    color: "#333",
    fontSize: 12,
    fontFamily: "Inter-Regular",
    padding: 6,
    textAlign: "center",
    paddingHorizontal: 10,
  },
});

export default UpdatePopup;
