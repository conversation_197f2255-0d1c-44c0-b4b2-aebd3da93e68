import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    alignItems: "center",
    flexDirection: "column",
    justifyContent: "space-around",
    backgroundColor: "#00000040",
  },
  activityIndicatorWrapper: {
    backgroundColor: "#FFFFFF",
    // height: 100,
    // width: 100,
    height: Dimensions.get("window").height / 3 + 20,
    width: Dimensions.get("window").width - 50,
    borderRadius: 10,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-around",
  },
  activityIndicator: {
    alignItems: "center",
    height: 80,
  },
  descText: {
    fontSize: 22,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.lightBlack10,
    textAlign: "center",
    marginBottom: 20,
    marginHorizontal: 10,
  },
});

export default styles;
