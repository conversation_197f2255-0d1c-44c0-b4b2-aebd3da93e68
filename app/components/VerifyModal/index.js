import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Modal,
  ActivityIndicator,
  Dimensions,
  Text,
} from "react-native";
import styles from "./styles";
import { useDispatch } from "react-redux";
import socketAction from "@redux/reducers/socket/actions";
import { isEmpty } from "lodash-es";
import { FontFamily } from "@config/typography";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;

const { setIsBoostPaymentStatus } = socketAction;

const VerifyModal = (props, { setModalVisible = () => {} }) => {
  const {
    visible,
    navigation,
    setIsModal = () => {},
    data,
    type = "boost",
    subTitle1 = "",
    subTitle2 = "",
    text = "",
    ...attributes
  } = props;
  const dispatch = useDispatch();
  useEffect(() => {
    let timeout;
    if (visible && type !== "boost" && type !== "addOns") {
      timeout = setTimeout(() => {
        setModalVisible(false);
        setIsModal(false);
        navigation.replace("SignUpWithFastRegistration", { data: data });
      }, 1500);
    } else if (type === "addOns") {
      setTimeout(() => {
        setIsModal(false);
        navigation.navigate("AddOnPlans", {
          type: "couponCode",
          data: data,
        });
      }, 1200);
    } else {
      timeout = setTimeout(() => {
        setIsModal(false);
        setModalVisible(false);
        // Empty boost payment emit data which one stored in Redux setIsBoostPaymentStatus
        dispatch(setIsBoostPaymentStatus({}));
        navigation.reset({
          index: 0,
          routes: [{ name: "HomeScreen" }],
        });
      }, 1000);
    }
    return () => clearTimeout(timeout);
  }, [visible, navigation]);
  return (
    <Modal
      transparent={true}
      animationType={"none"}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(false);
        console.log("close modal");
      }}
    >
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          <LottieView
            autoSize={true}
            source={images.verifyImg}
            autoPlay={true}
            // loop={true}
            style={{ height: 200, width: 200 }}
          />
          {!isEmpty(subTitle1) && (
            <Text
              style={[
                styles.descText,
                {
                  fontSize: 14,
                  opacity: 0.8,
                  fontFamily: FontFamily.InterMedium,
                },
              ]}
            >
              {subTitle1}
            </Text>
          )}
          <Text style={styles.descText}>{text}</Text>
          {!isEmpty(subTitle2) && (
            <Text
              style={[
                styles.descText,
                { fontSize: 14, opacity: 0.8, marginBottom: 40 },
              ]}
            >
              {subTitle2}
            </Text>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default VerifyModal;
