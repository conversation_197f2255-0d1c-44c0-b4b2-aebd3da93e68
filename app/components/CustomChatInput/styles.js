import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  recordMainView: {
    position: "absolute",
    zIndex: 999,
    width: "82%",
    minHeight: 45,
    borderRadius: 10,
    marginLeft: 10,
    backgroundColor: BaseColors.gray8,
    paddingRight: 10,
  },
  mainReplyView: {
    position: "absolute",
    top: -80,
    width: "100%",
    backgroundColor: BaseColors.lightblue,
    padding: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  setTime: {
    minWidth: 55,
  },
  mainRecodeView: {
    position: "absolute",
    top: -50,
    backgroundColor: "#D03F5E",
    padding: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 15,
    left: 15,
    borderRadius: 10,
    borderBottomRightRadius: 0,
  },
  timerView: {
    gap: 5,
    flexDirection: "row",
    height: "100%",
    alignItems: "center",
    justifyContent: "space-between",
  },
  closeBox: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: BaseColors.activeTab,
  },
  recorderCloseBox: {
    borderRadius: 25,
    backgroundColor: "#E16D6D",
  },
  selfText: {
    fontFamily: FontFamily.InterBold,
    color: BaseColors.black,
    fontSize: 15,
  },
  selectedMsg: {
    fontFamily: FontFamily.gray6,
    color: BaseColors.black,
    fontSize: 15,
  },
  selectedRecodeMsg: {
    fontFamily: FontFamily.gray6,
    color: BaseColors.white,
    fontSize: 15,
  },
  emojiAndStrikerMain: {
    flexDirection: "row",
    justifyContent: "center",
    marginHorizontal: 15,
  },
  selectedBox: {
    width: 60,
    justifyContent: "center",
    alignItems: "center",
  },
  emojiAndStrikerContent: {
    flexDirection: "row",
    borderWidth: 1,
    borderRadius: 5,
    overflow: "hidden",
    borderColor: "#BDBDBD",
    minHeight: 28,
  },
  timerMain: {
    paddingRight: 8,
    position: "absolute",
    bottom: -20,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  timerText: {
    color: BaseColors.black,
    fontSize: 10.5,
  },
  hederView: {
    marginVertical: 10,
  },
  smileIcon: {
    marginLeft: 10,
  },
  sendBtnMain: {
    backgroundColor: BaseColors.activeTab,
    height: 45,
    width: 45,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  micIcons: {
    marginHorizontal: 10,
  },
  inputToolbar: {
    borderTopWidth: 0,
    marginBottom: 5,
    paddingHorizontal: 15,
  },
  textInput: {
    borderRadius: 10,
    paddingHorizontal: 10,
    marginHorizontal: 10,
    backgroundColor: BaseColors.gray8,
  },
  sendIcon: {
    marginRight: 10,
    marginBottom: 5,
  },
  accessoryStyle: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 10,
  },
  accessoryWrapperStyle: {
    marginLeft: 5,
    marginRight: 5,
  },
  emojiIcon: {
    marginRight: 5,
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: -12,
    paddingTop: 12,
    width: "100%",
  },
  shareButton: {
    borderWidth: 1,
    height: 40,
    width: 40,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 12,
    borderRadius: 23,
    backgroundColor: "#F0F9FF",
    borderColor: BaseColors.primary,
  },
  inputContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginHorizontal: 12,
    backgroundColor: BaseColors.gray8,
    minHeight: 40,
    justifyContent: "space-between",
  },
  input: {
    flex: 1,
    marginRight: 10,
    padding: 8,
    fontFamily: FontFamily.medium,
    fontSize: 18,
    color: BaseColors.black,
    height: "auto",
  },
  sendButton: {
    height: 45,
    width: 45,
  },
  toastMainView: {
    borderWidth: 0.6,
    marginTop: 12,
    marginHorizontal: 16,
    padding: 6,
    backgroundColor: "transparent",
    borderRadius: 8,
    borderColor: BaseColors.gray,
    alignItems: "center",
    justifyContent: "center",
  },
  toastText: {
    fontSize: 14,
    fontFamily: FontFamily.regular,
    color: BaseColors.gray,
    textAlign: "center",
    marginBottom: Platform.OS === "ios" ? 0 : 3,
  },
  staticMsgView: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "flex-end",
    marginBottom: 10,
    marginRight: 12,
  },
  staticMsgTxt: {
    flex: 1,
    color: "#0596FE",
    fontSize: 14,
    borderWidth: 1,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 22,
    borderColor: "#0596FE",
  },
  pdfImg: { width: 30, height: 30 },
  mainMsgView: {
    marginHorizontal: 16,
    paddingTop: 5,
    paddingBottom: 5,
    flex: 1,
    flexDirection: "row",
  },
  msgTxt: {
    fontSize: 16,
    fontFamily: FontFamily.medium,
    maxWidth: "85%",
    borderRadius: 10,
  },
  fileMsgView: {
    alignItems: "center",
    maxWidth: "65%",
    padding: 10,
    borderRadius: 10,
  },
  mainEmojiContent: { width: "100%", backgroundColor: BaseColors.White },
  removeIconsMain: {
    position: "absolute",
    right: 0,
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
});

export default styles;
