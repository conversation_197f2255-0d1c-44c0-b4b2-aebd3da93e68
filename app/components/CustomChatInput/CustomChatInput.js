import {
  ActivityIndicator,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import styles from "./styles";
import { Text } from "react-native";
import { isEmpty, truncate } from "lodash-es";
import AudioRecorderPlayer, {
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
  AudioEncoderAndroidType,
  AudioSourceAndroidType,
} from "react-native-audio-recorder-player";
import Timer from "@components/Timer/Timer";
import { images } from "@config/images";
import LottieView from "lottie-react-native";
import {
  checkMicrophonePermission,
  getFileTypeFromExtension,
  uploadFile,
} from "@app/utils/commonFunction";
import { FontFamily } from "@config/typography";
import Toast from "react-native-simple-toast";

const audioRecorderPlayer = new AudioRecorderPlayer();
const RNFS = require("react-native-fs");
const dayjs = require("dayjs");

const Typing = () => {
  return (
    <View
      style={{
        position: "absolute",
        top: -35,
        left: 0,
        flex: 1,
        flexDirection: "row",
        alignItems: "center",
      }}
    >
      <Text
        style={{
          color: BaseColors.activeTab,
          fontSize: 16,
          fontFamily: FontFamily.RobotoMedium,
        }}
      >
        Typing
      </Text>
      <LottieView
        autoSize={true}
        source={images.typing}
        autoPlay={true}
        style={{
          width: 50,
          height: 50,
        }}
      />
    </View>
  );
};

const duration = require("dayjs/plugin/duration");

const CustomChatInput = ({
  onSendPress,
  message,
  setMessage,
  setKeyboardVisible,
  setIsOpenEmojiModal,
  isOpenEmojiModal = false,
  setKeyboardHeight = () => {},
  inputRef,
  replyViewData = "",
  setReplyViewData = () => {},
  onTyping = () => {},
  typingData,
  isKeyboardVisible = false,
  disable = false,
  stopSound,
  startAudioRecord,
  setStartAudioRecord,
  timeLeft,
  setTimeLeft,
  isAudioDuration,
  setIsAudioDuration,
  sendLoader,
  setSendLoader,
  isEditMessage,
  cancelEditMessage = () => {},
}) => {
  // const [startAudioRecord, setStartAudioRecord] = useState(false);
  // const [timeLeft, setTimeLeft] = useState(60);
  // const [isAudioDuration, setIsAudioDuration] = useState("");
  // const [sendLoader, setSendLoader] = useState(false);

  const handleSend = () => {
    if (message && message.length !== 0) {
      if (message.trim() !== "" && !sendLoader) {
        setSendLoader(true);
        onSendPress({
          _id: new Date().getTime(),
          createdAt: new Date(),
          text: message.trim(),
          user: { _id: 0 },
          type: !isEmpty(replyViewData) ? "reply" : "text",
          messageType: !isEmpty(replyViewData) ? "reply" : "message",
          reply_id: !isEmpty(replyViewData)
            ? replyViewData?.currentMessage?.message_id
            : null,
        });
      }
    } else {
      setSendLoader(false);
    }
  };
  const handleKeyPress = (event) => {
    if (event.nativeEvent.key === "Enter") {
      handleSend();
    }
  };

  const handelSmileIcon = () => {
    if (isOpenEmojiModal) {
      setIsOpenEmojiModal(false);
      inputRef.current.focus();
    } else {
      setIsOpenEmojiModal(true);
      Keyboard.dismiss();
    }
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      (e) => {
        setKeyboardHeight(e.endCoordinates.height);
      }
    );

    audioRecorderPlayer.addRecordBackListener((e) => {
      // Convert milliseconds to seconds
      const seconds = Math.floor(e?.currentPosition / 1000);
      // Create a duration object with the seconds
      const durationObject = dayjs.duration(seconds, "seconds");

      // Format the duration object as mm:ss
      const formattedTime = durationObject.format("mm:ss");
      setIsAudioDuration(formattedTime);
      // if (formattedTime === "01:00") {

      //   sendRecordedMessage();
      // }
    });

    // Remember to remove the listener when the component unmounts
    return () => {
      keyboardDidShowListener.remove();
      audioRecorderPlayer.removeRecordBackListener(() => {});
    };
  }, []);

  useEffect(() => {
    if (isAudioDuration === "01:00") {
      sendRecordedMessage();
    }
  }, [isAudioDuration]);
  const startRecording = async () => {
    stopSound();
    const audioSet = {
      AudioEncoderAndroid: AudioEncoderAndroidType.DEFAULT,
      AudioSourceAndroid: AudioSourceAndroidType.MIC,
      AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
      AVNumberOfChannelsKeyIOS: 2,
      AVFormatIDKeyIOS: AVEncodingOption.aac,
    };
    setStartAudioRecord(true);
    const paths = Platform.select({
      ios: `${dayjs().unix()}.aac`,
      android: `/${RNFS.CachesDirectoryPath}/${dayjs().unix()}.aac`,
    });
    const finalPath = paths.replace("//", "");

    try {
      await audioRecorderPlayer.startRecorder(finalPath, audioSet);
    } catch (error) {
      setStartAudioRecord(false);
      console.log("Failed to start recording:", error);
    }
  };

  const onStopRecord = async () => {
    setStartAudioRecord(false);
    setTimeLeft(60);
    setIsAudioDuration("");
    onCancelRecord();
  };

  const onCancelRecord = async () => {
    try {
      await audioRecorderPlayer.stopRecorder();
    } catch (error) {
      console.log("Failed to cancel recording:", error);
    }
  };

  const sendRecordedMessage = async () => {
    setSendLoader(true);
    try {
      const audioPath = await audioRecorderPlayer.stopRecorder();
      const stats = await RNFS.stat(audioPath);
      const fileSizeInBytes = stats?.size;
      const lastIndex = audioPath.lastIndexOf("/");
      const lastName = audioPath.substring(lastIndex + 1);
      setStartAudioRecord(false);
      setTimeLeft(60);
      const audioData = {
        name: lastName,
        size: fileSizeInBytes,
        type: getFileTypeFromExtension(audioPath),
        uri: audioPath,
      };
      const resp = await uploadFile(audioData, "audio");
      console.log("🚀 ~ sendRecordedMessage ~ resp:", resp?.data?.data);

      if (resp?.data?.data?.success) {
        const newMessage = {
          text: resp?.data?.data?.filename,
          messageType: "audio",
          duration: isAudioDuration || "01:00",
          reply_id: !isEmpty(replyViewData)
            ? replyViewData?.currentMessage?.message_id
            : null,
          meta: !isEmpty(replyViewData) ? "audioReply" : "",
        };

        onSendPress(newMessage);
        setSendLoader(false);
      } else {
        if (resp?.data?.message === "Network error") {
          sendRecordedMessage();
        } else {
          setSendLoader(false);
          Toast.show(
            resp?.data?.data?.message || "Something went wrong please try again"
          );
        }
      }
      setSendLoader(false);
    } catch (error) {
      setSendLoader(false);
      console.error("Error getting image size:", error);
    }
  };

  return (
    <View>
      {!isEmpty(replyViewData) ? (
        <View style={styles.mainReplyView}>
          <View style={{ width: "92%" }}>
            <Text style={styles.selfText}>You</Text>
            <Text style={styles.selectedMsg}>
              {truncate(replyViewData.currentMessage.text, {
                length: 60,
                omission: "...",
              })}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.closeBox}
            activeOpacity={0.8}
            onPress={() => setReplyViewData("")}
          >
            <CustomIcon name={"BsX"} size={20} color={BaseColors.activeTab} />
          </TouchableOpacity>
        </View>
      ) : null}

      {/* Hold To record View */}

      {/* {recordSingleClick ? (
        <View style={styles.mainRecodeView}>
          <View>
            <Text style={styles.selectedRecodeMsg}>
              Hold To record, release to send.
            </Text>
          </View>
          <TouchableOpacity
            style={styles.recorderCloseBox}
            activeOpacity={0.8}
            onPress={() => setRecordSingleClick(false)}
          >
            <CustomIcon name={"BsX"} size={22} color={"#D03F5E"} />
          </TouchableOpacity>
        </View>
      ) : null} */}

      {startAudioRecord ? (
        <View style={styles.recordMainView}>
          <View style={styles.timerView}>
            <View style={styles.setTime}>
              <Timer
                timeLeft={timeLeft}
                setTimeLeft={setTimeLeft}
                onStopRecord={onStopRecord}
                isAudioDuration={isAudioDuration}
              />
            </View>
            <View
              style={{
                height: 40,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <LottieView
                autoSize={true}
                source={images.voiceWave}
                autoPlay={true}
                style={{ height: 100, width: 80 }}
              />
            </View>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => onStopRecord()}
            >
              <CustomIcon
                name={"Delete"}
                size={22}
                color={BaseColors.activeTab}
              />
            </TouchableOpacity>
          </View>
        </View>
      ) : null}

      <View style={styles.container}>
        <View style={styles.inputContainer}>
          <TouchableOpacity
            style={styles.smileIcon}
            activeOpacity={0.8}
            onPress={disable ? null : () => handelSmileIcon()}
          >
            <CustomIcon
              name={
                isOpenEmojiModal
                  ? isKeyboardVisible
                    ? "Smile"
                    : "keyboard"
                  : "Smile"
              }
              size={23}
              color={BaseColors.black102}
            />
          </TouchableOpacity>

          {typingData && isEmpty(replyViewData) ? <Typing /> : null}
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={{ flex: 1 }}
          >
            <TextInput
              ref={inputRef}
              style={styles.input}
              placeholder={"Type message..."}
              value={message}
              onChangeText={(text) => {
                setMessage(text);
                onTyping(text);
              }}
              editable={disable ? false : true}
              placeholderTextColor={BaseColors.gray6}
              onSubmitEditing={() => {
                if (!sendLoader) {
                  handleSend();
                }
              }}
              onKeyPress={handleKeyPress}
              textAlignVertical="top"
              returnKeyType="send"
            />
          </KeyboardAvoidingView>

          {!isEmpty(isEditMessage) ? (
            <TouchableOpacity
              style={styles.micIcons}
              activeOpacity={0.8}
              onPress={() => cancelEditMessage()}
            >
              <CustomIcon
                name={"BsXCircle"}
                size={22}
                color={BaseColors.black102}
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.micIcons}
              activeOpacity={0.8}
              onPress={
                disable
                  ? () => Toast.show("User is Blocked")
                  : () => checkMicrophonePermission(startRecording)
              }
            >
              <CustomIcon
                name={"BsMic"}
                size={22}
                color={BaseColors.black102}
              />
            </TouchableOpacity>
          )}
        </View>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.sendBtnMain}
          onPress={
            disable
              ? () => Toast.show("User is Blocked")
              : startAudioRecord
                ? () => sendRecordedMessage()
                : () => {
                    if (!sendLoader) {
                      handleSend();
                    }
                  }
          }
        >
          {sendLoader ? (
            <ActivityIndicator animating color={BaseColors.White} />
          ) : (
            <CustomIcon name={"Send"} size={20} color={BaseColors.white} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CustomChatInput;
