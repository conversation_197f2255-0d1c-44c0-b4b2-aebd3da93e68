import React from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
} from "react-native";
import { FontFamily } from "@config/typography";
import { BaseColors } from "@config/theme";
import LottieView from "lottie-react-native";
import { images } from "@config/images";
import styles from "./styles";
import CButton from "@components/CButton";
import { translate } from "../../lang/Translate";
import { useSelector } from "react-redux";
import { isEmpty } from "lodash-es";

const PurChasePlanModal = ({ visible, setModalVisible, text, navigation }) => {

  const {activePlanData} = useSelector((s) => s.auth)

  const errorMessage = activePlanData?.plan_status === 0 ? text : 
        activePlanData?.allow_unlimited_post === 0 && 
        activePlanData?.post_count === 0 ? 
          'purchasePostAddOns' : 
          activePlanData?.allow_unlimited_reel === 0 && 
          activePlanData?.post_reels_count === 0 ? 
            'purchaseReelsAddOns' :  
            activePlanData?.post_count === 0 &&  activePlanData?.post_reels_count === 0 ? 
            'purchasePostReelsAddOns' : 'currentlyPlanText'

  const modalType = activePlanData?.plan_status === 0 ? 'subscription_plan' : 
  activePlanData?.allow_unlimited_post === 0 && 
  activePlanData?.post_count === 0 ? 
    'add_ons' : 
    activePlanData?.allow_unlimited_reel === 0 && 
    activePlanData?.post_reels_count === 0 ? 
      'add_ons' :  
      activePlanData?.post_count === 0 &&  activePlanData?.post_reels_count === 0 ? 
      'add_ons' : 'subscription_plan'

  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };
  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View style={styles.sharedTextView}>
                <LottieView
                  autoSize={true}
                  source={images.warningLottie}
                  autoPlay={true}
                  style={{ height: 150, width: 150 }}
                />

                <Text
                  style={{
                    fontSize: 18,
                    fontFamily: FontFamily.RobotoMedium,
                    marginVertical: 24,
                    color: BaseColors.fontColor,
                    textAlign: "center",
                  }}
                >
                  {translate(errorMessage)}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "center",
                  gap: 8,
                }}
              >
                <View style={{ flex: 1 }}>
                  <CButton
                    type="outlined"
                    onBtnClick={() => setModalVisible(false)}
                  >
                    Cancel
                  </CButton>
                </View>
                <View style={{ flex: 1 }}>
                  <CButton
                    onBtnClick={() => {
                      setModalVisible(false);
                      if(modalType === 'add_ons'){
                        setTimeout(() => {
                          navigation.navigate("Auth", {
                            screen: "AddOnPlans",
                            params: {
                              type: "add-ons",
                              data: { isSpecialOffer: false },
                            },
                          });
                        }, 200);
                      }else{
                        setTimeout(() => {
                          navigation.navigate("Auth", {
                            screen: "paymentPlan",
                          });
                        }, 200);
                      }
                    }}
                  >
                    Purchase
                  </CButton>
                </View>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default PurChasePlanModal;
