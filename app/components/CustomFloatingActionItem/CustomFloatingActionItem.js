import { Text, TouchableOpacity, View, FlatList } from "react-native";
import React from "react";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const CustomFloatingActionItem = ({ data, onPress }) => {
  const renderItem = ({ item, index }) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={{
        padding: 10,
        borderTopLeftRadius: index === 0 ? 10 : 0,
        borderBottomLeftRadius: index === 0 ? 10 : 0,
        borderTopRightRadius: index === data.length - 1 ? 10 : 0,
        borderBottomRightRadius: index === data.length - 1 ? 10 : 0,
        minWidth: index === data.length - 1 ? 90 : 120,
        justifyContent: "center",
        alignItems: "center",
      }}
      onPress={() => onPress(item.key)}
    >
      <Text
        style={{
          color: BaseColors.black,
          fontFamily: FontFamily.InterMedium,
          fontSize: 15,
          textAlign: "center",
        }}
      >
        {item.title}
      </Text>
      {(item.key === "reel" || item.key === "draft") && (
        <Text
          style={{
            position: "absolute",
            left: -3,
            bottom: 6,
            color: BaseColors.gray,
            fontSize: 25,
          }}
        >
          |
        </Text>
      )}
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={(item) => item.key}
      horizontal
      contentContainerStyle={{
        paddingHorizontal: 10,
      }}
    />
  );
};

export default CustomFloatingActionItem;
