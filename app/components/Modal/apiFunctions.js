import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";

// Get Intro Screen Data Function
export const getData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=reason`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success && resp?.data?.data) {
        return resp?.data?.data;
      } else {
        Toast.show(resp?.data?.message);
      }
    }
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};
