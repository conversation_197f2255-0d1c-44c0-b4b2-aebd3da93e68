/* eslint-disable react-native/no-inline-styles */
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { translate } from "../../lang/Translate";
import isEmpty from "lodash-es/isEmpty";
import React, { useState } from "react";
import {
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { CountryPicker } from "@app/libs/react-native-country-codes-picker";

const CInput = (props) => {
  const {
    rightinconName = "",
    rightIconSize = "",
    isErrorMsg = false,
    leftSideIcon = false,
    leftSideIconSize = "",
    isButton = false,
    onPressBtn = () => {},
    placeholderText = "",
    returnKeyType,
    onSubmit = () => {},
    maxLength,
    keyBoardType = "default",
    value,
    onChange = () => {},
    onFocus = () => {},
    passwordInputField = false,
    multiline,
    label = "",
    onKeyPress = () => {},
    isRequire = false,
    fontFamilyStyle,
    labelStyle,
    containerStyle,
    inputType = "normal", // "normal" || "mobile",
    selectedCountryCode = () => {},
    numberOfLines,
    reference,
    isCerBtnText,
    onCerBtnPress = () => {},
    disabled = false,
    countryCodeValue,
  } = props;

  // state
  const [secureTextData, setSecureTextData] = useState(passwordInputField);
  const [show, setShow] = useState(false);
  const [countryCode, setCountryCode] = useState("+91");

  return (
    <>
      {!isEmpty(label) ? (
        <Text
          style={{
            color: "#212121",
            fontSize: 18,
            fontWeight: "600",
            marginBottom: 5,
            ...labelStyle,
          }}
        >
          {label}
          {isRequire ? (
            <View style={{ paddingLeft: 1 }}>
              <Text style={{ color: "#F75555", fontSize: 15 }}>*</Text>
            </View>
          ) : null}
        </Text>
      ) : null}
      <View
        style={{
          borderWidth: 1,
          borderRadius: 5,
          flexDirection: "row",
          borderColor: isErrorMsg ? "#D6002E" : "#8E8383",
          backgroundColor: disabled ? BaseColors.gray : BaseColors.white,
          ...containerStyle,
        }}
      >
        {inputType === "mobile" ? (
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => setShow(true)}
            disabled={disabled}
            style={{
              // borderWidth: 1
              borderRightWidth: 1,
              marginVertical: 8,
              flexDirection: "row",

              borderColor: BaseColors.textLightGray,
              gap: 4,

              paddingHorizontal: 10,

              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Text
              style={{
                color: BaseColors.fontColor,

                fontSize: 16,
              }}
            >
              {countryCodeValue || countryCode}
            </Text>
            <CustomIcon
              name={!show ? "BsChevronDown" : "BsChevronUp"}
              size={18}
              color={BaseColors.fontColor}
            />
          </TouchableOpacity>
        ) : null}

        {leftSideIcon ? (
          <View
            style={[
              styles.iconView,
              {
                right: leftSideIcon ? null : 10,
                left: leftSideIcon ? 3 : null,
              },
            ]}
          >
            <CustomIcon
              name={leftSideIcon}
              size={Number(leftSideIconSize)}
              color={BaseColors.gray2}
            />
          </View>
        ) : null}

        <TextInput
          style={[
            styles.texTInputField,
            {
              flex: 1,
              paddingRight: leftSideIcon ? 5 : isButton ? 16 : 50,

              paddingLeft: leftSideIcon ? 40 : 20,
              height: numberOfLines ? 120 : null,
              textAlignVertical: multiline ? "top" : "center",
              ...fontFamilyStyle,
            },
          ]}
          ref={reference}
          placeholder={placeholderText}
          onChangeText={onChange}
          onKeyPress={onKeyPress}
          value={value}
          placeholderTextColor={"#555454"}
          editable={!disabled}
          returnKeyType={returnKeyType}
          onSubmitEditing={onSubmit}
          keyboardType={inputType === "mobile" ? "phone-pad" : keyBoardType}
          maxLength={maxLength}
          secureTextEntry={passwordInputField ? secureTextData : false}
          multiline={multiline}
          numberOfLines={numberOfLines || 1}
          onFocus={onFocus}
        />

        {isButton ? (
          <TouchableOpacity
            style={[
              {
                left: -12,
                justifyContent: "center",
              },
            ]}
            activeOpacity={0.9}
            onPress={() => onPressBtn()}
          >
            <CustomIcon name="Send" size={30} color={BaseColors.activeTab} />
          </TouchableOpacity>
        ) : null}
        {passwordInputField ? (
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => setSecureTextData(!secureTextData)}
            style={[
              styles.iconView,
              {
                right: leftSideIcon ? null : 10,
                left: leftSideIcon ? 10 : null,
              },
            ]}
          >
            <View>
              <CustomIcon
                name={secureTextData ? "BsEyeSlash" : "BsEye"}
                size={20}
                color={BaseColors.fontColor}
              />
            </View>
          </TouchableOpacity>
        ) : (
          <View
            style={[
              styles.iconView,
              {
                right: leftSideIcon ? null : 10,
                left: leftSideIcon ? 10 : null,
              },
            ]}
          >
            <CustomIcon
              name={rightinconName}
              size={Number(rightIconSize)}
              color={BaseColors.gray2}
            />
          </View>
        )}
        <TouchableWithoutFeedback
          onPress={() => (Platform.OS === "android" ? setShow(false) : null)}
        >
          <CountryPicker
            show={show}
            pickerButtonOnPress={(item) => {
              // setCountryCode(item.dial_code);
              setShow(false);
              selectedCountryCode({
                dial_code: item.dial_code,
                country_code: item?.code,
              });
            }}
            style={{
              modal: {
                height: Dimensions.get("window").height / 1.8,
              },
              dialCode: {
                fontSize: 15,
                fontFamily: FontFamily.RobotoMedium,
                color: BaseColors.fontColor,
              },
              // Country name styles [Text]
              countryName: {
                fontSize: 15,
                color: BaseColors.fontColor,
                fontFamily: FontFamily.RobotoMedium,
              },
              textInput: {
                fontSize: 15,
                color: BaseColors.fontColor,
                fontFamily: FontFamily.RobotoMedium,
              },
              searchMessageText: {
                fontSize: 15,
                color: BaseColors.fontColor,
                fontFamily: FontFamily.RobotoMedium,
              },
            }}
            enableModalAvoiding={true}
            onBackdropPress={() => setShow(false)}
          />
        </TouchableWithoutFeedback>
      </View>
      {isErrorMsg ? (
        <View style={styles.isBtnStyle}>
          <View style={styles.errorMsgMainView}>
            <CustomIcon
              name={"BsExclamationCircle"}
              size={18}
              color={"#D6002E"}
            />
            <Text style={styles.errorTxt}>{translate(isErrorMsg)}</Text>
          </View>
          {isCerBtnText ? (
            <TouchableOpacity onPress={onCerBtnPress} style={{ marginTop: 8 }}>
              <Text>
                <Text
                  style={[
                    styles.errorTxt,
                    {
                      fontFamily: FontFamily.RobotoMedium,
                      textDecorationLine: "underline",
                    },
                  ]}
                >
                  {isCerBtnText}
                </Text>
              </Text>
            </TouchableOpacity>
          ) : null}
        </View>
      ) : null}
    </>
  );
};

export default CInput;

const styles = StyleSheet.create({
  errorTxt: {
    color: "#D6002E",
    textAlign: "left",
    marginRight: 20,
  },
  errorMsgMainView: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 8,
    gap: 8,
  },
  iconView: {
    position: "absolute",
    top: 5,
    height: 38,
    width: 38,
    alignItems: "center",
    justifyContent: "center",
  },
  texTInputField: {
    minHeight: 50,
    fontSize: 16,
    position: "relative",
    color: BaseColors.fontColor,
  },
  isBtnStyle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  btnContainerStyle: {
    height: 30,
    paddingVertical: 0,
  },
  btnTextContainerStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
  },
});
