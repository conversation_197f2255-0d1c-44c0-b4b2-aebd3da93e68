import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import {} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import AuthActions from "@redux/reducers/auth/actions";
import { isEmpty } from "lodash-es";
import PurChasePlanModal from "@components/PurchasePlanModal";
import { navigationRef } from "@navigation/NavigationService";
import AsyncStorage from "@react-native-async-storage/async-storage";

const { setPurchaseModal } = AuthActions;


const OpenModalTimer = ({activePlanData,userData, setIsDrawerOpen} ) => {
  const [timeSpent, setTimeSpent] = useState(0);
  
  useEffect(() => {
    let interval;

    const checkModalLogic = async () => {
      const isPlan = activePlanData?.plan_status === 0;
      
      if(
        !isEmpty(userData) &&
      navigationRef?.current?.getCurrentRoute()?.name !== "paymentPlan" &&
      navigationRef?.current?.getCurrentRoute()?.name !== "SplashScreen" &&
      navigationRef?.current?.getCurrentRoute()?.name !== "LoginMainScreen" && 
      isPlan === 0){
      try {
        const lastShownDate = await AsyncStorage.getItem('modalLastShownDate');
        const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format

        // Check if the modal has already been shown today
        if (lastShownDate !== today) {
          // Start the timer if modal hasn't been shown today
          interval = setInterval(() => {
            setTimeSpent((prevTime) => {
              if (prevTime > 60) {
                clearInterval(interval);
                  setIsDrawerOpen(true);
                  AsyncStorage.setItem('modalLastShownDate', today); // Save today's date
              }
              return prevTime + 1;
            });
          }, 1000);
        }
      } catch (error) {
        console.error('Error checking modal display:', error);
      }
    }
    };

    checkModalLogic();

    return () => {
      clearInterval(interval);
    };
  }, [userData, activePlanData, navigationRef?.current]);

  return <></>
}

const OpenPurchaseModalComponent = () => {
  const { userData, activePlanData, purchaseModal } = useSelector(
    (auth) => auth.auth
  );

  const dispatch = useDispatch();

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  
  // const current_date = dayjs();
  // const isDifferent = current_date.diff(purchaseModal?.appStartTime, "minute");

  // const isDifferentDate = async (current_date, date) => {
  //   const currentDate = dayjs(current_date);
  //   const givenDate = dayjs(date);

  //   // Compare the date (ignoring time)
  //   if (!currentDate.isSame(givenDate, "date")) {
  //     // Compare the time difference in hours
  //     const hoursDifference = currentDate.diff(givenDate, "hour");
  //     if (hoursDifference >= 4) {
  //       // Check if they are different dates after adjusting the time
  //       const adjustedDate = givenDate.add(4, "hours");
  //       if (!currentDate.isSame(adjustedDate, "date")) {
  //         return true; // Dates are different after the time difference
  //       }
  //     }
  //   }

  //   return false; // Same date
  // };

  // const openPurchaseModal = async () => {
  //   const current_date = dayjs();

  //   const isPlan =
  //     !activePlanData?.is_plan_active ||
  //     (activePlanData?.is_plan_active && activePlanData?.is_prime_user)||
  //     (!activePlanData?.is_plan_active && activePlanData?.is_free_user);

  //   if (!isUndefined(purchaseModal?.date)) {
  //     const date = dayjs(purchaseModal.date);

  //     const isShowModal =
  //       isDifferentDate(current_date, date) &&
  //       !purchaseModal?.isShow &&
  //       !isEmpty(userData) &&
  //       navigationRef?.current?.getCurrentRoute()?.name !== "paymentPlan" &&
  //       navigationRef?.current?.getCurrentRoute()?.name !== "SplashScreen" &&
  //       navigationRef?.current?.getCurrentRoute()?.name !== "LoginMainScreen" &&
  //       isPlan;

  //     if (isShowModal) {
  //       setTimeout(() => {
  //         dispatch(
  //           setPurchaseModal({
  //             ...purchaseModal,
  //             date: current_date,
  //             isShow: true,
  //           })
  //         );
  //         setIsDrawerOpen(true);
  //       }, 1000);
  //     } else {
  //       setIsDrawerOpen(false);
  //     }
  //   } else {
  //     setTimeout(() => {
  //       dispatch(
  //         setPurchaseModal({
  //           ...purchaseModal,
  //           date: current_date,
  //           isShow: true,
  //         })
  //       );
  //       setIsDrawerOpen(true);
  //     }, 1000);
  //   }
  // };

  // useEffect(() => {
  //   if (!isEmpty(userData)) {
  //     // openPurchaseModal(); // Call the function on mount
  //   }
  // }, [activePlanData]);

  // useEffect(() => {
  //   (async () => {
  //     const isPlan =
  //     !activePlanData?.is_plan_active ||
  //     (activePlanData?.is_plan_active && activePlanData?.is_prime_user)||
  //     (!activePlanData?.is_plan_active && activePlanData?.is_free_user);
      
  //     if(!isEmpty(userData) &&
  //     navigationRef?.current?.getCurrentRoute()?.name !== "paymentPlan" &&
  //     navigationRef?.current?.getCurrentRoute()?.name !== "SplashScreen" &&
  //     navigationRef?.current?.getCurrentRoute()?.name !== "LoginMainScreen" && isPlan){

  //       try {
  //         const lastShownDate = await AsyncStorage.getItem('modalLastShownDate');
  //         const today = new Date().toISOString().split('T')[0]; // Get the date in YYYY-MM-DD format
  //         if (lastShownDate !== today) {
  //           const shouldShowModal = Math.random() < 0.5; // 50% chance to show the modal
  //           if (shouldShowModal) {
  //             setIsDrawerOpen(true);
  //             await AsyncStorage.setItem('modalLastShownDate', today); // Save today's date
  //           }
  //         }else{
  //           setIsDrawerOpen(false);
  //         }
  //       } catch (error) {
  //         console.error('Error checking modal display:', error);
  //       }
  //     }
  //   })();
  // }, [activePlanData, userData, navigationRef?.current?.getCurrentRoute()]);

  // useEffect(() => {
  //   let interval;

  //   const checkModalLogic = async () => {
  //     const isPlan = !activePlanData?.plan_status;
      
  //     if(!isEmpty(userData) &&
  //     navigationRef?.current?.getCurrentRoute()?.name !== "paymentPlan" &&
  //     navigationRef?.current?.getCurrentRoute()?.name !== "SplashScreen" &&
  //     navigationRef?.current?.getCurrentRoute()?.name !== "LoginMainScreen" && isPlan){
  //     try {
  //       const lastShownDate = await AsyncStorage.getItem('modalLastShownDate');
  //       const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format

  //       // Check if the modal has already been shown today
  //       if (lastShownDate !== today) {
  //         // Start the timer if modal hasn't been shown today
  //         interval = setInterval(() => {
  //           setTimeSpent((prevTime) => {
  //             if (prevTime >= 30) {
  //               clearInterval(interval);
  //                 setIsDrawerOpen(true);
  //                 AsyncStorage.setItem('modalLastShownDate', today); // Save today's date
  //             }
  //             return prevTime + 1;
  //           });
  //         }, 1000);
  //       }
  //     } catch (error) {
  //       console.error('Error checking modal display:', error);
  //     }
  //   }
  //   };

  //   checkModalLogic();

  //   return () => {
  //     clearInterval(interval);
  //   };
  // }, [userData, activePlanData, navigationRef?.current]);

  return (
    <>
    <PurChasePlanModal
      visible={isDrawerOpen}
      setModalVisible={() => {
        dispatch(setPurchaseModal({ ...purchaseModal, isShow: false }));
        setIsDrawerOpen(false);
      }}
      text={"currentlyPlanText"}
      navigation={navigationRef.current}
    />
    <OpenModalTimer activePlanData={activePlanData} userData={userData} setIsDrawerOpen={setIsDrawerOpen} />
    </>
  );
};

export default OpenPurchaseModalComponent;
