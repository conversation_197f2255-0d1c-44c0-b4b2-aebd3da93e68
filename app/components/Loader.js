// Import React and Component
// import { images } from "@config/images";
// import LottieView from "lottie-react-native";
import React from "react";
import { StyleSheet, View, Modal } from "react-native";
import MiniLoader from "./MiniLoader";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;

const Loader = (props) => {
  const { loading, ...attributes } = props;

  return (
    <Modal
      transparent={true}
      animationType={"none"}
      visible={loading}
      onRequestClose={() => {}}
    >
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          {/* <ActivityIndicator
            animating={true}
            color="#000000"
            size="large"
            style={styles.activityIndicator}
          /> */}
          <LottieView
            autoSize={true}
            source={images.miniLoader}
            autoPlay={true}
            style={styles.loader}
          />
        </View>
      </View>
    </Modal>
  );
};

export default Loader;

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    alignItems: "center",
    flexDirection: "column",
    justifyContent: "space-around",
    backgroundColor: "#00000040",
  },
  activityIndicatorWrapper: {
    backgroundColor: "#FFFFFF",
    height: 100,
    width: 100,
    borderRadius: 10,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-around",
  },
  activityIndicator: {
    alignItems: "center",
    height: 80,
  },
  loader: {
    height: 175,
    width: "80%",
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 200,
  },
});
