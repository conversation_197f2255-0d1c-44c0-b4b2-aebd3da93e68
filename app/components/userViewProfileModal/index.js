import React, { useCallback, useMemo, useState } from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  Switch,
  TouchableOpacity,
  Image,
} from "react-native";
import { FontFamily } from "@config/typography";
import styles from "./styles";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import Toast from "react-native-simple-toast";

const UserViewProfileModal = ({
  visible,
  setModalVisible,
  data,
  navigation,
  onPressViewProfile = () => {},
  onPressRemoveFollowers = () => {},
  onPressSendMessage = () => {},
}) => {
  console.log("🚀 ~ data:", data?.is_followed);
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };
  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <View />
                <View />

                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    height: 28,
                    width: 28,
                    borderRadius: 5,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  activeOpacity={0.8}
                  onPress={() => setModalVisible(false)}
                >
                  <CustomIcon name="BsX" size={25} color={BaseColors.black} />
                </TouchableOpacity>
              </View>
              <View style={styles.sharedTextView}>
                {/* Image View */}
                <View
                  style={{
                    height: 75,
                    width: 75,
                    borderRadius: 40,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Image
                    source={{ uri: data?.user_dp }}
                    style={{
                      height: 90,
                      width: 90,
                      resizeMode: "contain",
                      borderRadius: 50,
                    }}
                    resizeMode="cover"
                  />
                </View>
                <Text
                  style={{
                    fontSize: 18,
                    fontFamily: FontFamily.RobotoRegular,
                    marginVertical: 24,
                    marginTop: 10,
                    color: BaseColors.fontColor,
                  }}
                >
                  {data?.username}
                </Text>
              </View>
              <TouchableOpacity
                style={{
                  paddingVertical: 20,
                  flexDirection: "row",
                  justifyContent: "space-between",
                  borderBottomWidth: 1,
                  borderBottomColor: "#DCDFE3",
                }}
                activeOpacity={0.8}
                onPress={() => onPressViewProfile(data)}
              >
                <Text
                  style={{
                    fontSize: 18,
                    fontFamily: FontFamily.RobotoRegular,
                    color: BaseColors.black100,
                  }}
                >
                  View Profile
                </Text>
                <CustomIcon
                  name="BsChevronRight"
                  size={18}
                  color={BaseColors.black100}
                />
              </TouchableOpacity>
              {data?.is_followed !== undefined && data?.is_followed === true ? (
                <TouchableOpacity
                  style={{
                    paddingVertical: 20,
                    flexDirection: "row",
                    justifyContent: "space-between",
                    borderBottomWidth: 1,
                    borderBottomColor: "#DCDFE3",
                  }}
                  activeOpacity={0.8}
                  onPress={() => onPressRemoveFollowers(data)}
                >
                  <Text
                    style={{
                      fontSize: 18,
                      fontFamily: FontFamily.RobotoRegular,
                      color: BaseColors.black100,
                    }}
                  >
                    Remove Followers
                  </Text>
                  <CustomIcon
                    name="BsChevronRight"
                    size={18}
                    color={BaseColors.black100}
                  />
                </TouchableOpacity>
              ) : null}

              <TouchableOpacity
                style={{
                  paddingVertical: 20,
                  flexDirection: "row",
                  justifyContent: "space-between",
                  borderBottomWidth: 1,
                  borderBottomColor: "#DCDFE3",
                }}
                activeOpacity={0.8}
                onPress={() => onPressSendMessage(data)}
              >
                <Text
                  style={{
                    fontSize: 18,
                    fontFamily: FontFamily.RobotoRegular,
                    color: BaseColors.black100,
                  }}
                >
                  Send Message
                </Text>
                <CustomIcon
                  name="BsChevronRight"
                  size={18}
                  color={BaseColors.black100}
                />
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default UserViewProfileModal;
