/* eslint-disable react-native/no-inline-styles */

import React from "react";
import PropTypes from "prop-types";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  ActivityIndicator,
} from "react-native";
import { BaseColors } from "../../config/theme";
import { Image } from "react-native";
// import Animated, {
//   Easing,
//   FadeInLeft,
//   FadeInUp,
// } from "react-native-reanimated";

const Animated = require("react-native-reanimated").default;
const Easing = require("react-native-reanimated").Easing;
const FadeInLeft = require("react-native-reanimated").FadeInLeft;
const FadeInUp = require("react-native-reanimated").FadeInUp;

/**
 * Component for Button
 * @module  Button
 *
 */
export default function Button(props) {
  const {
    children,
    type,
    shape,
    raised,
    containerStyle,
    loading,
    onBtnClick,
    style,
    txtSty,
    iconPosition,
    iconName,
    iconSty,
    vIconName,
    vIconSty,
    showRightIcon,
    showLeftIcon,
    imgSrc = "",
    fromModal,
    disabled,
    ...rest
  } = props;

  const renderText = () => (
    <Text
      style={{
        fontSize: 16,
        color:
          type === "secondary"
            ? BaseColors.primary3
            : disabled
              ? "#1A1A1A80"
              : BaseColors.white,
        fontFamily: "Inter-SemiBold",
        ...txtSty,
      }}
    >
      {!loading ? (
        children
      ) : (
        <ActivityIndicator
          animating
          color={type === "secondary" ? BaseColors.primary3 : BaseColors.White}
        />
      )}
    </Text>
  );

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      disabled={disabled}
      {...rest}
      onPress={loading ? () => {} : onBtnClick}
      style={{
        overflow: "hidden",
        ...styles[shape],
        // ...shadow,
        ...styles[type],
        ...style,
      }}
    >
      <View
        style={[
          styles.btnContainer,
          {
            backgroundColor:
              type === "secondary"
                ? "#FDFDFD"
                : disabled
                  ? "#F5F6F8"
                  : BaseColors.primary3,
            borderWidth: fromModal || disabled ? 0 : 1,
            borderColor: BaseColors.primary3,
            position: "relative",
          },
          containerStyle,
        ]}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "center",
          }}
        >
          {renderText()}
          {showLeftIcon ? (
            <Animated.View
              entering={FadeInUp.duration(500).easing(Easing.ease)}
              exiting={FadeInLeft}
              style={{ position: "absolute", left: 0, top: 0, bottom: 0 }}
            >
              <Image
                source={imgSrc}
                resizeMethod={"resize"}
                resizeMode={"cover"}
                style={{
                  height: 24,
                  width: 24,
                }}
              />
            </Animated.View>
          ) : null}
        </View>
      </View>
      {/* )} */}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  round: {
    borderRadius: 100,
  },
  square: {
    borderRadius: 5,
  },
  btnContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    width: "100%",
  },
  outlined: {
    backgroundColor: "#ffff",
    borderWidth: 1,
    borderColor: "#0000",
  },
  text: {
    backgroundColor: "transparent",
  },
  shadow: {
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 0,
  },
});

Button.propTypes = {
  type: PropTypes.oneOf(["primary", "outlined", "text", "secondary"]),
  shape: PropTypes.oneOf(["round", "square"]),
  raised: PropTypes.bool,
  containerStyle: PropTypes.object,
  loading: PropTypes.bool,
  showRightIcon: PropTypes.bool,
  showLeftIcon: PropTypes.bool,
  onBtnClick: PropTypes.func,
  style: PropTypes.object,
  txtSty: PropTypes.object,
  iconPosition: PropTypes.string,
  iconName: PropTypes.string,
  iconSty: PropTypes.object,
  imgSrc: PropTypes.string,
};

Button.defaultProps = {
  type: "primary", // "primary"  | "outlined" | "text"
  shape: "square", // "round"  | "square"
  raised: true, // true | false
  containerStyle: {},
  loading: false, // true | false
  showRightIcon: false,
  showLeftIcon: false,
  onBtnClick: () => {},
  style: {},
  txtSty: {},
  iconPosition: "left",
  iconName: "",
  iconSty: {},
  imgSrc: "",
};
