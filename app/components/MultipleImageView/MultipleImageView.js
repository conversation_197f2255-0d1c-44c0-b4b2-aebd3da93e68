import {
  Dimensions,
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import React, { useState } from "react";
import { styles } from "./style";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { useSelector } from "react-redux";

const MultipleImageView = ({
  selectedImage,
  addMore,
  handleAddMore,
  addMoreBtn = false,
  showDots = false,
}) => {
  const WIDTH = Dimensions.get("window").width;
  const HEIGHT = Dimensions.get("window").height;

  const [activeIndex, setActiveIndex] = useState(0);

  //redux State
  const { adminSettingData } = useSelector((state) => state.auth);
  const imageSizeObject = adminSettingData?.find(
    (obj) => obj?.slug === "IMAGEUPLOADPERPOST"
  );

  const renderPaginationDots = () => {
    return (
      <View style={styles.paginationView}>
        {Array.from({ length: imageSizeObject?.value }).map((_, index) => (
          <View
            key={index}
            style={[
              styles.renderPaginationDotsView,
              {
                backgroundColor:
                  index < selectedImage?.length
                    ? BaseColors.activeTab
                    : BaseColors.white,
                borderColor:
                  index < selectedImage?.length
                    ? BaseColors.activeTab
                    : BaseColors.gray,
              },
            ]}
            onPress={() => setActiveIndex(index)}
          />
        ))}
      </View>
    );
  };

  const renderImage = ({ item, index }) => {
    return (
      <View style={{ width: WIDTH }}>
        <Image
          source={{ uri: item?.uri || item?.fileUrl }}
          style={{
            width: WIDTH,
            resizeMode: "contain",
            height: HEIGHT / 2.5,
          }}
        />
      </View>
    );
  };

  const onMomentumScrollEnd = (event) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.floor(offsetX / (WIDTH - 80));

    if (index !== activeIndex) {
      setActiveIndex(index);
    }
  };

  return (
    <View>
      <View>
        <FlatList
          data={selectedImage}
          renderItem={renderImage}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, index) => index.toString()}
          onMomentumScrollEnd={onMomentumScrollEnd}
        />
      </View>
      {/* Add more Button */}
      {addMoreBtn ? (
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.addMoreMain}
          onPress={() => handleAddMore()}
        >
          {addMore ? (
            <View>
              <View style={styles.addMoreText}>
                <View
                  style={{
                    backgroundColor: "#999999",
                    borderRadius: 5,
                  }}
                >
                  <CustomIcon
                    name={"BsX"}
                    color={"rgba(38, 38, 38, 0.8)"}
                    size={15}
                  />
                </View>
                <Text style={{ color: "#fff" }}>Add more</Text>
              </View>
            </View>
          ) : (
            <Text style={styles.addMoreText}>+ Add more</Text>
          )}
        </TouchableOpacity>
      ) : null}

      {/* render Dots */}
      {addMore ? renderPaginationDots() : null}

      {showDots ? renderPaginationDots() : null}
    </View>
  );
};

export default MultipleImageView;
