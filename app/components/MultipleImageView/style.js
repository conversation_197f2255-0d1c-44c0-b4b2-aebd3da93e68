import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet } = require("react-native");

export const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  paginationView: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
  },
  renderPaginationDotsView: {
    width: 12,
    height: 12,
    borderRadius: 25,
    marginHorizontal: 5,
    borderWidth: 1.5,
    borderColor: BaseColors.activeTab,
  },
  headerMainView: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 18,
  },
  cancelTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  headingTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoMedium,
    color: "#322F2F",
  },

  mainButton: {
    position: "absolute",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
    bottom: 0,
    height: "10%",
  },
  addMoreMain: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  addMoreText: {
    backgroundColor: "rgba(38, 38, 38, 0.8)",
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 50,
    color: "#fff",
    marginBottom: 10,
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center",
    flexDirection: "row",
    gap: 5,
  },
});
