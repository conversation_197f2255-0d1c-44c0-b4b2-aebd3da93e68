/* eslint-disable react-native/no-inline-styles */
import ActivityCard from "../../components/ActivityCard";
import React from "react";
import { SafeAreaView, Dimensions, TouchableOpacity } from "react-native";
// import Animated, {
//   useSharedValue,
//   useAnimatedStyle,
//   useAnimatedScrollHandler,
//   interpolate,
//   Extrapolate,
// } from 'react-native-reanimated';

const Animated = require("react-native-reanimated").default;
const useSharedValue = require("react-native-reanimated").useSharedValue;
const useAnimatedStyle = require("react-native-reanimated").useAnimatedStyle;
const useAnimatedScrollHandler =
  require("react-native-reanimated").useAnimatedScrollHandler;
const interpolate = require("react-native-reanimated").interpolate;
const Extrapolate = require("react-native-reanimated").Extrapolate;

const { width, height } = Dimensions.get("screen");

const slideWidth = width * 0.75;
const slideHeight = height * 0.4;

const Slide = ({ slide, scrollOffset, index, onclickFev, onClickActivity }) => {
  const animatedStyle = useAnimatedStyle(() => {
    const input = scrollOffset.value / slideWidth;
    const inputRange = [index - 1, index, index + 1];

    return {
      transform: [
        {
          scale: interpolate(
            input,
            inputRange,
            [0.9, 1, 0.9],
            Extrapolate.CLAMP
          ),
        },
      ],
    };
  });

  return (
    <Animated.View
      key={index}
      style={[
        {
          flex: 1,
          width: slideWidth,
          height: slideHeight,
          //   marginRight: 12,
        },
        animatedStyle,
      ]}
    >
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => onClickActivity(slide)}
      >
        <ActivityCard
          data={slide}
          onclickFev={(data) => {
            onclickFev(data, index);
          }}
        />
      </TouchableOpacity>
    </Animated.View>
  );
};

const SlideComponent = ({ slides, onclickFev, onClickActivity }) => {
  const scrollOffset = useSharedValue(0);
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollOffset.value = event.contentOffset.x;
    },
  });

  return (
    <SafeAreaView style={{ flex: 1, justifyContent: "space-around" }}>
      <Animated.ScrollView
        scrollEventThrottle={1}
        horizontal
        snapToInterval={slideWidth}
        decelerationRate="fast"
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          alignItems: "center",
          paddingHorizontal: 20,
          gap: 24,
          paddingRight: 40,
          justifyContent: "center",
        }}
        onScroll={scrollHandler}
      >
        {slides.map((slide, index) => {
          return (
            <Slide
              key={index}
              index={index}
              slide={slide}
              arr={slides}
              scrollOffset={scrollOffset}
              onclickFev={onclickFev}
              onClickActivity={onClickActivity}
            />
          );
        })}
      </Animated.ScrollView>
    </SafeAreaView>
  );
};

export default React.memo(SlideComponent);
