import React from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
  FlatList,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import { followListData } from "@config/staticData";
// import FastImage from "react-native-fast-image";
import NoRecord from "@components/NoRecord";
import Animated, { FadeInDown } from "react-native-reanimated";
import CButton from "@components/CButton";

const FastImage = require("react-native-fast-image");

const CurrentPlanInfoModal = ({
  visible,
  setModalVisible,
  onAddNewStory,
  listData = [],
  onViewStory,
  buttonText,
  onBtnPress,
  activePlanData,
}) => {
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };

  const renderFeature = (item, index) => {
    return (
      <View style={{ marginBottom: 5 }}>
        <Animated.View
          style={styles.descRenderMainView}
          entering={FadeInDown}
        >
            <CustomIcon
              name="correct-icon"
              size={16}
              color={BaseColors.lightBlack10}
            />
            {
              item?.type !== 'post_posts' && item?.type !== 'post_reels' ?
            <Text
              style={styles.descTextStyle}
            >
            {item?.title}
            </Text> :  item?.type === 'post_reels' ?
            <Text
              style={styles.descTextStyle}
            >
             {listData?.allow_unlimited_reel ?  `You have unlimited Reels remaining until your plan ends` : 
              listData?.post_reels_count > -1 ?
              `You still have ${listData?.post_reels_count || 0} reels remaining.` : 
              null} 
            </Text>
            : item?.type === 'post_posts' ?
            <Text
              style={styles.descTextStyle}
            >
            {listData?.allow_unlimited_post ? 
              `You have unlimited Posts remaining until your plan ends` : listData?.post_count > -1 ?
              `You still have ${listData?.post_count || 0} post remaining.` : null}
            </Text> : null
            }
          </Animated.View>
        </View>
    )
  }

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "flex-end",
                }}
              >
                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    height: 24,
                    width: 24,
                    borderRadius: 5,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  activeOpacity={0.8}
                  onPress={() => setModalVisible(false)}
                >
                  <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                </TouchableOpacity>
              </View>

              {activePlanData?.allow_unlimited ? (
                <View style={{ marginBottom: 20 }}>
                  <Animated.View
                    style={styles.descRenderMainView}
                    entering={FadeInDown}
                  >
                    <CustomIcon
                      name="correct-icon"
                      size={16}
                      color={BaseColors.lightBlack10}
                    />
                    <View style={{ flex: 1 }}>
                      <Text style={styles.descTextStyle}>
                        You have unlimited Posts and Reels remaining until your
                        plan ends
                      </Text>
                    </View>
                  </Animated.View>
                </View>
              ) : (
                <FlatList
                  data={listData?.features_list_preview}
                  renderItem={({ item, index }) => (
                    renderFeature(item, index)
                  )}
                />
              )}
              {
                activePlanData?.plan_type === 'special_offer' || 
                activePlanData?.plan_type === 'coupon' ? null :
                <CButton
                  onBtnClick={() => setModalVisible(false)}
                  showRightIcon={true}
                >
                  Upgrade
                </CButton>
              }
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default CurrentPlanInfoModal;
