/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import { BaseColors } from "../../config/theme";
import React from "react";
import { Modal, StyleSheet, Text, View } from "react-native";
import * as Progress from "react-native-progress";
import { FontFamily } from "@config/typography";

const ProgressBarModal = ({ visible, style, progress = 1 }) => {
  return (
    <View style={{ ...styles.centeredView, ...style }}>
      <Modal
        animationType="fade"
        transparent={true}
        visible={visible}
        style={{ ...style }}
      >
        <View style={styles.ovarlayStyle}>
          <View style={styles.modalView}>
            <View style={styles.container}>
              <Progress.Pie
                progress={progress}
                size={100}
                color={BaseColors.activeTab}
                borderWidth={1.5}
              />
            </View>
            <View style={{ paddingTop: 10 }}>
              <Text
                style={{
                  fontSize: 22,
                  fontWeight: FontFamily.InterBold,
                  color: BaseColors.activeTab,
                  textAlign: "center",
                }}
              >
                Uploading...
              </Text>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
    height: 130,
  },
  centeredView: {
    justifyContent: "center",
    alignItems: "center",
  },

  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.2)",
    flex: 1,
    justifyContent: "center",
  },
  modalView: {
    margin: 20,
    backgroundColor: BaseColors.white,
    borderRadius: 20,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    marginVertical: 150,
  },
});

export default ProgressBarModal;
