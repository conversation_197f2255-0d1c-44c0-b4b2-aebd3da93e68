// Import React and Component
import React from "react";
// import LottieView from "lottie-react-native";
// import { images } from "@config/images";
import { StyleSheet, View } from "react-native";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;

const Loader = () => {
  return (
    <View style={styles.mainView}>
      <LottieView
        autoSize={true}
        source={images.footLoader}
        autoPlay={true}
        style={styles.loader}
      />
    </View>
  );
};

export default React.memo(Loader);

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loader: {
    height: 175,
    width: "80%",
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 200,
  },
});
