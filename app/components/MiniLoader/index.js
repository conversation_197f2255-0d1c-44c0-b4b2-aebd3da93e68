// import { images } from "@config/images";
// import LottieView from "lottie-react-native";
import React from "react";
import { View } from "react-native";
import styles from "./styles";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;

function MiniLoader({ size = "medium" }) {
  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
      }}
    >
      <View
        style={[
          styles.mainView,
          { height: size === "large" ? 100 : size === "medium" ? 65 : 50 },
        ]}
      >
        <LottieView
          autoSize={true}
          source={images.miniLoader}
          autoPlay={true}
          style={styles.loader}
        />
      </View>
    </View>
  );
}

export default MiniLoader;
