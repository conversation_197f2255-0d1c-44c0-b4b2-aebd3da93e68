// import { images } from '../../config/images';
// import LottieView from 'lottie-react-native';
import React, { useEffect } from "react";
import { Dimensions } from "react-native";
import { View, StyleSheet, Modal } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import socketActions from "@redux/reducers/socket/actions";
import isEmpty from "lodash-es/isEmpty";
import { isNull } from "lodash-es";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;
const { initialization } = socketActions;

const NetworkLost = ({ isVisible }) => {
  const dispatch = useDispatch();
  const socketIo = useSelector((uState) => uState?.socket?.socketObj);
  const { accessToken, userData } = useSelector((state) => state.auth);

  //  Socket connection logic
  useEffect(() => {
    if (!isEmpty(accessToken) && isNull(socketIo)) {
      dispatch(initialization());
    }
  }, [socketIo, accessToken, isVisible]);

  return (
    <Modal transparent={true} animationType={"none"} visible={isVisible}>
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          <LottieView
            autoSize={true}
            source={images.network}
            autoPlay={true}
            // loop={true}
            style={{
              width: Dimensions.get("screen").width - 100,
              height: Dimensions.get("screen").width - 100,
            }}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  // Define your styles here
  modalBackground: {
    flex: 1,
    alignItems: "center",
    flexDirection: "column",
    justifyContent: "space-around",
    backgroundColor: "#00000040",
    padding: 50,
  },
  activityIndicatorWrapper: {
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-around",
    padding: 20,
  },
});

export default NetworkLost;
