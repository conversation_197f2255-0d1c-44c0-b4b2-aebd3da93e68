import React, { useCallback, useMemo, useState } from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  Switch,
  TouchableOpacity,
} from "react-native";
import { FontFamily } from "@config/typography";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import CButton from "@components/CButton";
import { BaseColors } from "@config/theme";
import { renderData } from "./compont";
import { CustomIcon } from "@config/LoadIcons";
import Toast from "react-native-simple-toast";
import { shareStoryMethods } from "./apiCallFunctions";
import { getStoryData } from "@components/StoryComponent/apiCallFunction";
import AuthActions from "../../redux/reducers/auth/actions";
import { useDispatch } from "react-redux";

const { setUserStoryList, setFromScreenName } = AuthActions;

const StoryShareModal = ({
  visible,
  setModalVisible,
  image,
  navigation,
  video_duration,
  storyType,
  type,
}) => {
  const dispatch = useDispatch();
  // State's
  const [onChangeValue, setOnchangeValue] = useState(true);
  const [isStoryChecked, setIsStoryChecked] = useState(true);
  const [isHighLightChecked, setIsHIghtLightChecked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Memo's
  const onChangeValueMemo = useMemo(() => onChangeValue, [onChangeValue]);
  const isStoryCheckedMemo = useMemo(() => isStoryChecked, [isStoryChecked]);
  const isHighLightCheckedMemo = useMemo(
    () => isHighLightChecked,
    [isHighLightChecked]
  );
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);

  //OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }

    Keyboard.dismiss();
  };

  // Checkbox Handling Method's For Story
  const handleCheckboxChangeForStory = useCallback(
    (value) => {
      setIsStoryChecked(value);
    },
    [isStoryCheckedMemo]
  );

  // Checkbox Handling Method's For HighLight
  const handleCheckboxChangeForHighLight = useCallback(
    (value) => {
      setIsHIghtLightChecked(value);
    },
    [isHighLightCheckedMemo]
  );

  const handleToShare = useCallback(
    async (duration) => {
      const apiData = await {
        is_highlighted: isHighLightCheckedMemo,
        is_on_story: isStoryCheckedMemo,
        is_shareable: onChangeValueMemo,
        type: image?.mimetype.split("/")[0],
      };
      if (type !== "reels") {
        apiData.filename = image?.filename;
      } else {
        apiData.reel_id = image?.reelId;
      }
      if (storyType === "video") {
        apiData.story_seconds = duration;
      }

      if (isHighLightChecked === false && isStoryCheckedMemo === false) {
        Toast.show("Please select any one option");
      } else {
        setIsLoading(true);
        const resp = await shareStoryMethods(apiData);
        if (resp?.data?.success) {
          setIsLoading(false);
          handleToGetData();
          await setModalVisible(false);
          dispatch(setFromScreenName("createStoryScreen"));
          navigation.navigate("Home", {
            screen: "HomeTab",
            params: {},
          });
        } else {
          await setModalVisible(false);
          setIsLoading(false);
          Toast.show(resp?.data?.message);
        }
      }
    },
    [
      isLoadingMemo,
      isStoryCheckedMemo,
      onChangeValueMemo,
      isHighLightCheckedMemo,
    ]
  );

  const shortingData = (array, key) => {
    let selfIndex;
    array.forEach((item, index) => {
      if (item.hasOwnProperty(key) && item[key] === true) {
        selfIndex = index;
        return;
      }
    });

    // Move the object with the "self" key to the 0 index
    if (selfIndex !== undefined) {
      const selfObject = array.splice(selfIndex, 1)[0];
      array.unshift(selfObject);
    }

    return array;
  };
  const handleToGetData = async () => {
    const respData = await getStoryData();
    if (respData?.data?.success) {
      const data = await shortingData(respData?.data?.data, "self");

      dispatch(setUserStoryList(data));
    } else {
      Toast.show(respData?.data?.message || "Something went wrong please try again!");
    }
  };
  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  backgroundColor: BaseColors.white,
                  padding: 20,
                  borderTopStartRadius: 20,
                  borderTopEndRadius: 20,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <View />
                  <Text style={styles.modalTitleText}>
                    {translate("shareText")}
                  </Text>

                  <TouchableOpacity
                    style={{
                      borderWidth: 1,
                      height: 28,
                      width: 28,
                      borderRadius: 5,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    activeOpacity={0.8}
                    onPress={() => setModalVisible(false)}
                  >
                    <CustomIcon name="BsX" size={25} color={BaseColors.black} />
                  </TouchableOpacity>
                </View>
                {renderData(
                  image,
                  "addToStoryText",
                  "storyDes",
                  handleCheckboxChangeForStory,
                  isStoryCheckedMemo
                )}
                <View style={{ marginTop: 10 }} />
                {renderData(
                  image,
                  "addToHighlight",
                  "highlightDes",
                  handleCheckboxChangeForHighLight,
                  isHighLightCheckedMemo
                )}
              </View>
              <View style={styles.sharedTextView}>
                <Text style={styles.textView}>{translate("sharedText")}</Text>
                <View
                  style={[
                    styles.switchCardView,
                    {
                      backgroundColor: "#EFEFEF",
                    },
                  ]}
                >
                  <Switch
                    trackColor={{
                      false: "transparent",
                      true: "transparent",
                    }}
                    thumbColor={
                      onChangeValueMemo ? BaseColors.activeTab : "white"
                    }
                    ios_backgroundColor="transparent"
                    onValueChange={() => setOnchangeValue(!onChangeValueMemo)}
                    value={onChangeValueMemo}
                  />
                </View>
              </View>
              <View style={styles.btnView}>
                <CButton
                  txtSty={{
                    fontSize: 18,
                    fontFamily: FontFamily.RobotSemiBold,
                  }}
                  onBtnClick={() =>
                    handleToShare(video_duration * 1000 || 30000)
                  }
                  loading={isLoadingMemo}
                >
                  {translate("shareText")}
                </CButton>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default StoryShareModal;
