import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalView: {
    backgroundColor: "#F5F5F5",
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
  },

  modalTitleText: {
    fontSize: 24,
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginBottom: 15,
    textAlignVertical: "center",
  },
  dataMainView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  storyImageMainView: {
    height: 75,
    width: 75,
    overflow: "hidden",
    borderRadius: 43,
    borderWidth: 1,
  },
  storyImageView: {
    height: 75,
    width: 75,
    resizeMode: "cover",
  },
  headingText: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  descText: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.gray3,
  },
  separatorView: {
    borderWidth: 1,
    marginTop: 10,
  },
  sharedTextView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 10,
    backgroundColor: BaseColors.white,
    padding: 20,
  },
  textView: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  btnView: {
    marginBottom: 10,
    marginHorizontal: 19,
  },
  switchCardView: {
    borderRadius: 20,
    overflow: "hidden",
  },
});

export default styles;
