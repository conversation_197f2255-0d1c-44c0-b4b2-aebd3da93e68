import { BaseColors } from "@config/theme";
import { translate } from "../../lang/Translate";
import { Text, View, Image, TouchableOpacity } from "react-native";
import styles from "./styles";
import Checkbox from "@components/Checkbox";
import { useMemo, useState } from "react";

export const renderData = (
  image,
  headingText,
  desText,
  onChangeCheckBox,
  isCheck
) => {
  return (
    <TouchableOpacity
      style={[styles.dataMainView, { justifyContent: "space-between" }]}
      activeOpacity={0.9}
      onPress={() => {
        onChangeCheckBox(!isCheck);
      }}
    >
      <View style={[styles.dataMainView]}>
        <View
          style={[
            styles.storyImageMainView,
            { borderColor: BaseColors.activeTab },
          ]}
        >
          <Image
            source={{
              uri: image?.thubnails !== null ? image?.thubnails : image?.uri,
            }}
            style={styles.storyImageView}
          />
        </View>
        <View>
          <Text style={styles.headingText}>{translate(headingText)}</Text>
          <Text style={styles.descText}>{translate(desText)}</Text>
        </View>
      </View>
      <View>
        <Checkbox
          isChecked={isCheck}
          toggleCheckbox={() => onChangeCheckBox(!isCheck)}
        />
      </View>
    </TouchableOpacity>
  );
};
