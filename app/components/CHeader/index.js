import React from "react";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import styles from "./styles";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
// import Animated, { FadeInUp } from "react-native-reanimated";
import isEmpty from "lodash-es/isEmpty";
import { FontFamily } from "@config/typography";
import Popover from "react-native-popover-view/dist/Popover";
import Oicons from "react-native-vector-icons/Octicons";
import { translate } from "../../lang/Translate";

const Animated = require("react-native-reanimated").default;
const FadeInUp = require("react-native-reanimated").FadeInUp;
const FastImage = require("react-native-fast-image");

const CHeader = ({
  headingTitle = "",
  headerTitleStyle,
  handleBackButton = () => {},
  camera = false,
  cameraColor = BaseColors.black,
  backIconColor = BaseColors.black,
  cameraSize = 20,
  onPressCamera = () => {},
  handleOptionsBtn = () => {},
  onPressChatHeader = () => {},
  clearAllNotification = () => {},
  isBackIcon = true,
  CancelTxt = "",
  cameraIcon = "Camera",
  chat = false,
  userImage = "",
  chatTitle = "",
  optionData = [],
  setShowPopover = () => {},
  showPopover = false,
  isOnline = false,
  clearBtn = false,
  isGroupChat = false,
  groupMember = 0,
}) => {
  return (
    <Animated.View style={styles.mainView} entering={FadeInUp}>
      {chat ? (
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            width: "100%",
            justifyContent: "space-between",
            paddingHorizontal: 20,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <TouchableOpacity
              style={[styles.chatBackIconView, { borderColor: backIconColor }]}
              activeOpacity={0.8}
              onPress={handleBackButton}
            >
              <CustomIcon name="back-arrow" size={18} color={backIconColor} />
            </TouchableOpacity>

            <TouchableOpacity
              style={{ flexDirection: "row", alignItems: "center", gap: 5 }}
              onPress={onPressChatHeader}
              activeOpacity={0.8}
            >
              <View>
                <FastImage source={{ uri: userImage }} style={styles.imgView} />
                {isOnline ? <View style={styles.greenDot}></View> : null}
              </View>
              <Text
                style={{
                  fontSize: 18,
                  fontFamily: FontFamily.RobotoMedium,
                  color: BaseColors.black,
                  textTransform: "capitalize",
                }}
              >
                {chatTitle}
              </Text>
              {/* green tick */}
            </TouchableOpacity>
          </View>

          {/* Popover list */}
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            {isGroupChat ? (
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Oicons
                  name="person-add"
                  size={20}
                  color={BaseColors.headerIcon}
                />
                <Text
                  style={{
                    fontSize: 14,
                    color: BaseColors.headerIcon,
                    fontFamily: FontFamily.RobotoBold,
                  }}
                >
                  {groupMember}
                </Text>
              </View>
            ) : null}
            <Popover
              from={
                <TouchableOpacity
                  activeOpacity={0.9}
                  onPress={() => setShowPopover(true)}
                  style={styles.dots}
                >
                  <CustomIcon
                    name={"dot"}
                    size={28}
                    color={BaseColors.headerIcon}
                  />
                </TouchableOpacity>
              }
              popoverStyle={styles.popoverStyle}
              isVisible={showPopover}
              onRequestClose={() => setShowPopover(false)}
              popoverArrow={false}
              children={
                <FlatList
                  data={optionData || []}
                  renderItem={({ item, index }) => (
                    <TouchableOpacity
                      style={[
                        styles.mainPopData,
                        {
                          borderBottomWidth:
                            index === optionData?.length - 1 ? 0 : 1,
                        },
                      ]}
                      activeOpacity={0.8}
                      onPress={() => {
                        if (item.key === "block") {
                          setShowPopover(false);
                          handleOptionsBtn("block");
                        } else if (item.key === "report") {
                          setShowPopover(false);
                          handleOptionsBtn("report");
                        } else if (item.key === "clearChat") {
                          setShowPopover(false);
                          handleOptionsBtn("clearChat");
                        } else {
                          handleOptionsBtn(item?.key);
                          setShowPopover(false);
                        }
                      }}
                    >
                      <CustomIcon
                        name={item.icon}
                        size={22}
                        color={
                          item.key === "clearChat"
                            ? BaseColors.activeTab
                            : BaseColors.black
                        }
                      />
                      <Text
                        style={{
                          color:
                            item.key === "clearChat"
                              ? BaseColors.activeTab
                              : BaseColors.black,
                          fontSize: 16,
                          fontFamily: FontFamily.InterMedium,
                        }}
                      >
                        {item.text}
                      </Text>
                    </TouchableOpacity>
                  )}
                />
              }
            />
          </View>
        </View>
      ) : null}

      {!isEmpty(CancelTxt) ? (
        <TouchableOpacity
          style={styles.cBackIconView}
          activeOpacity={0.8}
          onPress={handleBackButton}
        >
          <Text style={{ color: BaseColors.black }}>
            {translate(CancelTxt)}
          </Text>
        </TouchableOpacity>
      ) : isBackIcon ? (
        <View>
          <TouchableOpacity
            style={[styles.backIconView, { borderColor: backIconColor }]}
            activeOpacity={0.8}
            onPress={handleBackButton}
          >
            <CustomIcon name="back-arrow" size={18} color={backIconColor} />
          </TouchableOpacity>
        </View>
      ) : (
        <View style={[styles.backIconView, { borderWidth: 0 }]} />
      )}

      {!isEmpty(headingTitle) ? (
        <View style={styles.headingTitleTextView}>
          <Text style={[styles.titleTextStyle, { ...headerTitleStyle }]}>
            {translate(headingTitle)}
          </Text>
        </View>
      ) : null}

      {camera ? (
        <View style={styles.cameraChangeBtn}>
          <TouchableOpacity activeOpacity={0.8} onPress={onPressCamera}>
            {}
            <CustomIcon
              name={cameraIcon}
              size={cameraSize}
              color={cameraColor}
            />
          </TouchableOpacity>
        </View>
      ) : clearBtn ? (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={clearAllNotification}
          style={styles.cameraChangeBtn}
        >
          <Text style={styles.clearText}>Clear All</Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.cameraChangeBtn} />
      )}
    </Animated.View>
  );
};
export default CHeader;
