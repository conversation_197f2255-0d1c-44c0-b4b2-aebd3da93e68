import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Platform, StatusBar, StyleSheet } from "react-native";

const getStatusBarHeight = () => {
  if (Platform.OS === "android") {
    return StatusBar.currentHeight;
  } else if (Platform.OS === "ios") {
    return 20;
  } else {
    return 0;
  }
};
const statusBarHeight = getStatusBarHeight();
const styles = StyleSheet.create({
  mainView: {
    height: 35,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "transparent",
    // marginTop: 12,
    marginVertical: 12,
    width: "100%",
  },
  greenDot: {
    position: "absolute",
    right: 0,
    height: 18,
    width: 18,
    backgroundColor: BaseColors.green,
    borderRadius: 25,
    bottom: 0,
    borderWidth: 3,
    borderColor: BaseColors.white,
  },
  popoverStyle: {
    borderWidth: 1,
    width: 150,
    borderRadius: 8,
    marginRight: 12,
    padding: 15,
    borderColor: BaseColors.gray,
  },
  mainPopData: {
    borderColor: BaseColors.gray,
    flexDirection: "row",
    gap: 8,
    paddingVertical: 6,
  },
  imgView: {
    height: 50,
    width: 50,
    borderRadius: 33,
  },
  backIconStyle: { height: 40, width: 40 },
  titleTextStyle: {
    fontSize: 24,
    fontFamily: FontFamily.RobotoMedium,
    color: "#343434",
    textAlign: "center",
    alignSelf: "center",
    lineHeight: 28.13,
  },
  clearText: {
    fontSize: 15,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.activeTab,
    paddingRight: 15,
  },
  backIconView: {
    marginHorizontal: 20,
    borderWidth: 1,
    padding: 6,
    borderRadius: 23,
  },
  chatBackIconView: {
    borderWidth: 1,
    padding: 6,
    borderRadius: 23,
    marginRight: 15,
  },
  cBackIconView: {
    width: "18%",
    justifyContent: "center",
    alignItems: "center",
  },
  cameraChangeBtn: {
    width: "20%",
    justifyContent: "center",
    alignItems: "center",
  },
  backTxtView: {
    marginHorizontal: 20,
    padding: 6,
  },
  dots: {
    width: 20,
  },
});

export default styles;
