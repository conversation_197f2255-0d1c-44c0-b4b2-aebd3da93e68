import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import RadioButton from "@components/CRadioButton";
// import { reportDataArr } from "@config/staticData";
import CInput from "@components/TextInput";
// import Animated, { FadeInDown, FadeOut } from "react-native-reanimated";
import CButton from "@components/CButton";
import { isEmpty } from "lodash-es";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const FadeOut = require("react-native-reanimated").FadeOut;
const { reportDataArr } = require("@config/staticData");

const ReportModal = ({
  visible,
  setModalVisible,
  selectedOptionForReason = () => {},
  reasonValue = {},
  textInputData = "",
  setTextInputValue = () => {},
  onBtnPress = () => {},
  isErrMessage,
  isLoading = false,
}) => {
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <View>
                  <Text style={styles.modalTitleText}>
                    {translate("reportText")}
                  </Text>
                </View>

                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    height: 24,
                    width: 24,
                    borderRadius: 5,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  activeOpacity={0.8}
                  onPress={() => setModalVisible(false)}
                >
                  <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                </TouchableOpacity>
              </View>

              <RadioButton
                type="options"
                options={reportDataArr}
                selectedOption1={(e) => {
                  selectedOptionForReason(e);
                }}
              />
              {reasonValue?.options === "somethingElseText" &&
              !isEmpty(reasonValue) ? (
                <Animated.View entering={FadeInDown} exiting={FadeOut}>
                  <CInput
                    placeholderText={"Type here your reason..."}
                    containerStyle={styles.textInputStyle}
                    multiline
                    numberOfLines={3}
                    onChange={setTextInputValue}
                    value={textInputData}
                    isError={!isEmpty(isErrMessage)}
                    isErrorMsg={isErrMessage}
                  />
                </Animated.View>
              ) : null}
              <Animated.View
                style={styles.buttonView}
                entering={FadeInDown}
                exiting={FadeOut}
              >
                <CButton
                  disabled={isEmpty(reasonValue)}
                  onBtnClick={onBtnPress}
                  loading={isLoading}
                >
                  {translate("submitText")}
                </CButton>
              </Animated.View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default ReportModal;
