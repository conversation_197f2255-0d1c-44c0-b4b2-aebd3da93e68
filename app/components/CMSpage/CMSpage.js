/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import { BaseColors } from '../../config/theme';
import React, { useCallback, useEffect, useState } from 'react';
import { View, useWindowDimensions } from 'react-native';
import isEmpty from 'lodash-es/isEmpty';
import { defaultStyle } from '../../utils/commonFunction';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import Toast from 'react-native-simple-toast';
import RenderHTML from 'react-native-render-html';

const CMSpage = ({ navigation }) => {
  const [data, setData] = useState('');
  const defaultHead = `<meta name="viewport" content="width=device-width, initial-scale=1">
${defaultStyle}`;

  useEffect(() => {
    fetchData();
    return () => {};
  }, []);

  /**
   * fetch about data
   * @function fetchData
   */

  const fetchData = useCallback(async () => {
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.policy}?slug=terms-and-conditions`,
        'GET',
      );
      if (resp?.data?.success && resp?.data?.data) {
        setData(resp?.data?.data || 'test');
      } else {
        setData('');
        Toast.show(resp?.message || 'In else');
      }
      // setLoader(false);
    } catch (error) {
      setData('');
      console.log('ERRR', error);
      // setLoader(false);
    }
  }, []);

  const { width } = useWindowDimensions();
  return (
    <View
      style={{
        flexGrow: 1,
        backgroundColor: BaseColors.white,
        paddingHorizontal: 20,
        paddingTop: 20,
      }}>
      {!isEmpty(data) ? (
        <View style={{ minHeight: 500 }}>
          <RenderHTML
            contentWidth={width}
            baseStyle={{ color: BaseColors.textInputLabel }}
            source={{
              html: !isEmpty(data?.html_body)
                ? `${defaultHead}${data.html_body}`
                : '',
            }}
          />
        </View>
      ) : null}
    </View>
  );
};

export default CMSpage;
