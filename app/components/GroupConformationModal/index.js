import React from "react";
import {
  Modal,
  View,
  Text,
  TouchableWithoutFeedback,
  StyleSheet,
} from "react-native";
import CButton from "@components/CButton";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const GroupConformationModal = ({
  visible,
  header = "",
  content = "",
  optionOneLabel = "Yes",
  optionTwoLabel = "No",
  onOptionOne = () => {},
  onOptionTwo = () => {},
  onRequestClose = () => {},
  loading = false,
}) => {
  console.log("🚀 ~ GroupConformationModal ~ header:", header);
  console.log("🚀 ~ GroupConformationModal ~ visible:", visible);
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onRequestClose}
      style={{ zIndex: 100000 }}
    >
      <View style={styles.overlay}>
        <TouchableWithoutFeedback onPress={onRequestClose}>
          <View style={StyleSheet.absoluteFill} />
        </TouchableWithoutFeedback>
        <View style={styles.modalView}>
          {!!header && <Text style={styles.header}>{header}</Text>}
          {!!content && <Text style={styles.content}>{content}</Text>}
          <View style={styles.buttonRow}>
            <View style={{ width: "48%" }}>
              <CButton onBtnClick={onOptionOne} loading={loading}>
                {optionOneLabel}
              </CButton>
            </View>
            <View style={{ width: "48%" }}>
              <CButton type="outlined" onBtnClick={onOptionTwo}>
                {optionTwoLabel}
              </CButton>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalView: {
    margin: 20,
    backgroundColor: BaseColors.white,
    borderRadius: 20,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    minWidth: 280,
    maxWidth: 340,
  },
  header: {
    fontSize: 20,
    color: BaseColors.black,
    fontFamily: FontFamily.InterBold,
    textAlign: "center",
    marginBottom: 10,
  },
  content: {
    fontSize: 16,
    color: BaseColors.black,
    fontFamily: FontFamily.InterRegular,
    textAlign: "center",
    marginBottom: 20,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
});

export default GroupConformationModal;
