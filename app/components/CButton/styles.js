import { StyleSheet } from 'react-native';
import { BaseColors } from '@config/theme';
import FORTAB from '@components/MQ';

const styles = StyleSheet.create({
  btn: {
    height: FORTAB ? 65 : 53,
    backgroundColor: BaseColors.whiteColor,
    marginTop: 20,
    marginBottom: 0,
    shadowColor: '#26BEB326',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },

  iconBtnStyle: {
    borderRadius: FORTAB ? 100 : 60,
    backgroundColor: BaseColors.primary,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: BaseColors.whiteColor,
    alignSelf: 'center',

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.39,
    shadowRadius: 8.3,
    elevation: 5,
  },
  iconStyle: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    color: BaseColors.primary,
    textAlign: 'center',
  },
});
export default styles;
