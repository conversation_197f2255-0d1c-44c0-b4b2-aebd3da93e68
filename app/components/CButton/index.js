import React from "react";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  ActivityIndicator,
} from "react-native";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { CustomIcon } from "@config/LoadIcons";

export default function CButton(props) {
  const {
    disabled = false,
    children,
    type,
    shape,
    raised,
    containerStyle,
    loading,
    onBtnClick,
    style,
    txtSty,
    iconPosition,
    titleFontSize,
    iconName,
    iconSty,
    titleFontFamily,
    fontColor = type === "outlined" ? BaseColors.activeTab : BaseColors.white,
    vIconName,
    outLineBorderWidth = 2,
    vIconSty,
    showRightIcon,
    blackBtn = false,
    ...rest
  } = props;

  const renderText = () => (
    <Text
      // eslint-disable-next-line react-native/no-inline-styles
      style={[
        txtSty,
        {
          fontSize: Number(titleFontSize) ? Number(titleFontSize) : 18,
          color: fontColor,
          fontWeight: "600",

          letterSpacing: 0.5,
          fontFamily: blackBtn
            ? FontFamily.InterSemiBold
            : titleFontFamily
              ? titleFontFamily
              : FontFamily.RobotoBold,
        },
      ]}
    >
      {!loading ? (
        children
      ) : (
        <ActivityIndicator
          animating
          color={type === "outlined" ? BaseColors.activeTab : BaseColors.White}
        />
      )}
    </Text>
  );

  const shadow = raised && type === "primary" ? styles.shadow : styles.shadow;

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      {...rest}
      onPress={loading ? () => {} : onBtnClick}
      disabled={disabled}
      // eslint-disable-next-line react-native/no-inline-styles
      style={{
        // opacity: disabled ? 0 : 1,
        overflow: "hidden",
        ...styles[shape],
        ...shadow,
        ...styles[type],
        ...style,
        borderWidth: type === "outlined" ? 1 : 1,
        borderColor: "transparent",
        opacity: disabled ? 0.6 : 1,
        borderRadius: 5,

        // backgroundColor: 'red',
      }}
    >
      <View
        style={[
          styles.btnContainer,
          containerStyle,

          {
            backgroundColor: disabled
              ? type === "outlined"
                ? BaseColors.white
                : BaseColors.activeTab
              : type === "outlined"
                ? BaseColors.white
                : BaseColors.activeTab,
            borderWidth: type === "outlined" ? outLineBorderWidth : 0,

            borderColor: blackBtn ? BaseColors.black : BaseColors.activeTab,
          },
        ]}
      >
        <View style={styles.renderRightIcon}>
          {renderText()}
          {showRightIcon && !loading ? (
            <CustomIcon
              name="BsChevronRight"
              size={titleFontSize || 18}
              color={
                type === "outlined" ? BaseColors.activeTab : BaseColors.white
              }
            />
          ) : null}
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  round: {
    borderTopStartRadius: 100,
    borderTopEndRadius: 100,
    borderBottomStartRadius: 100,
    borderBottomEndRadius: 100,
  },
  square: {
    borderTopStartRadius: 5,
    borderTopEndRadius: 5,
    borderBottomStartRadius: 5,
    borderBottomEndRadius: 5,
  },
  btnContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 5,
    width: "100%",
    height: 52,
    borderColor: "#000000",
  },
  outlined: {
    backgroundColor: "#ffff",
    borderWidth: 1,
    borderColor: "#0000",
  },
  text: {
    backgroundColor: "transparent",
    fontSize: 32,
  },
  shadow: {
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 0,
  },
  renderRightIcon: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
});

CButton.defaultProps = {
  type: "primary", // "primary"  | "outlined" | "text"
  shape: "square", // "round"  | "square"
  raised: true, // true | false
  containerStyle: {},
  loading: false, // true | false
  showRightIcon: false,
  onBtnClick: () => {},
  style: {},
  txtSty: {},
  iconPosition: "left",
  iconName: "",
  iconSty: {},
};
