/* eslint-disable react-native/no-inline-styles */
// Import React and Component
import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { BaseColors } from '../config/theme';
import BaseSetting from '../config/setting';
import { translate } from '../lang/Translate';
import { Text } from 'react-native';
import { useMemo } from 'react';

const PlaceAutoComplete = ({
  refs,
  isError,
  isErrorMsg,
  onAutoCompleteAddressSelect,
  placeholder,
  onChangeText = () => {},
  isDisable = false,
}) => {
  const [isListViewVisible, setIsListViewVisible] = useState(false);
  const isListViewMemo = useMemo(() => isListViewVisible, [isListViewVisible]);

  const dropdownBorder = isListViewMemo ? 1 : 0;

  return (
    <ScrollView
      contentContainerStyle={{ zIndex: 10000 }}
      keyboardShouldPersistTaps={'handled'}>
      <GooglePlacesAutocomplete
        ref={refs}
        placeholder={placeholder || translate('addressPlaceholder')}
        query={{
          key: BaseSetting.googleAutoCompleteKey,
          language: 'en', // language of the results
          components: 'country:id',
        }}
        keepResultsAfterBlur
        returnKeyType={'search'}
        keyboardShouldPersistTaps="always"
        fetchDetails
        currentLocation={true}
        currentLocationLabel="anand"
        listUnderlayColor="#E6EFFE"
        listViewDisplayed={isDisable ? false : isListViewMemo}
        textInputProps={{
          editable: !isDisable,
          placeholderTextColor: isError ? '#FF0B1E' : BaseColors.textSecondary,
          onChangeText: (txt) => {
            onChangeText(txt);
            setIsListViewVisible(true);
          },
        }}
        onPress={(data, details = null) => {
          onAutoCompleteAddressSelect(data, details);
          setIsListViewVisible(false);
        }}
        styles={{
          container: {
            paddingBottom: 5,
          },
          listView: {
            backgroundColor: BaseColors.White,
            borderWidth: dropdownBorder,
            borderColor: '#E6EFFE',
            borderRadius: 10,
            paddingHorizontal: 10,
          },
          separator: {
            borderColor: '#E6EFFE',
            borderBottomWidth: 1,
            borderRadius: 5,
          },
          row: {
            borderRadius: 7,
            backgroundColor: BaseColors.white,
          },
          poweredContainer: {
            display: 'none',
          },
          textInputContainer: {
            backgroundColor: BaseColors.White,
            borderRadius: 25,
            marginVertical: 10,
            color: BaseColors.textSecondary,
            marginBottom: 0,
          },
          textInput: {
            backgroundColor: BaseColors.white,
            fontSize: 15,
            color: BaseColors.textSecondary,
            height: 50,
            borderWidth: 1,
            borderColor: isError ? '#FF0B1E' : '#E6EFFE',
            borderRadius: 10,
            paddingHorizontal: 20,
            fontFamily: 'Inter-Regular',
          },
          description: {
            color: BaseColors.textSecondary,
          },
        }}
      />
      {isError && (
        <Text
          style={{
            fontSize: 15,
            color: '#FF0B1E',
            marginLeft: 6,
            marginTop: -6,
          }}>
          {translate(isErrorMsg)}
        </Text>
      )}
    </ScrollView>
  );
};

export default React.memo(PlaceAutoComplete);
