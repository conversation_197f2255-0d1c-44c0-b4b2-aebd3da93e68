import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const multiStyles = StyleSheet.create({
  root: { marginBottom: 15 },
  labelContainer: {
    marginBottom: 3,
    flexDirection: "row",
    alignItems: "center",
  },
  errorMsgMainView: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 8,
    gap: 5,
  },
  errorTxt: {
    color: "#FF0B1E",
    paddingLeft: 5,
    textAlign: "left",
  },
  labelAndRequiredIconContainer: {
    flexDirection: "row",
    marginRight: 5,
  },
  requiredIcon: {
    color: BaseColors.red,
    marginLeft: 2,
    fontSize: 8,
  },
  valueListCloseIcon: {
    fontSize: 24,
    color: BaseColors.white,
    marginLeft: 5,
  },
  searchIcon: {
    fontSize: 20,
    color: BaseColors.grey50,
    marginLeft: 5,
  },
  iconContainer: {
    width: "10%",
    alignItems: "flex-end",
  },
  arrowIconContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  arrowIcon: {
    fontSize: 16,
    color: BaseColors.primary,
  },
  noteText: {
    fontFamily: FontFamily.RobotoRegular,
    textAlign: "left",
    color: BaseColors.white,
    marginStart: 2,
    marginEnd: 24,
    marginTop: 5,
    fontSize: 12,
    letterSpacing: 1,
  },
  errorMsgText: {
    fontFamily: FontFamily.RobotoRegular,
    textAlign: "left",
    color: BaseColors.red,
    marginStart: 2,
    marginEnd: 24,
    marginTop: 5,
    fontSize: 12,
    letterSpacing: 1,
  },
  valueListContainer: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 5,
    marginRight: 5,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BaseColors.primary,
  },
  placeholderText: {
    fontSize: 16,
    position: "relative",
    color: BaseColors.placeHolderColor,
    fontFamily: FontFamily.RobotoRegular,
  },
  closeIcon: {
    fontSize: 24,
    color: BaseColors.white,
    marginLeft: 5,
  },
  mainContainer: {
    backgroundColor: BaseColors.white,
    paddingRight: 10,
    paddingStart: 15,
    borderWidth: 0.6,
    borderColor: "#8E8383",
    borderRadius: 5,
    borderWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    minHeight: 60,
  },
  dropDownArrowIcon: {
    fontSize: 16,
    color: BaseColors.primary,
  },

  // DropDown List Styles
  mainListContainer: {
    position: "absolute",
  },
  disabled: {
    backgroundColor: BaseColors.grey20,
    opacity: 0.7,
  },
  disabled: {
    backgroundColor: BaseColors.grey20,
    opacity: 0.7,
  },
  listContainer: {
    backgroundColor: BaseColors.white,
    elevation: 5,
    shadowColor: BaseColors.grey90,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.18,
    shadowRadius: 4.59,
    borderRadius: 5,
    zIndex: 50,
  },
  bodyContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    paddingBottom: 20,
  },
  searchInputContainer: {
    borderWidth: 1,
    borderColor: BaseColors.borderColor,
    marginHorizontal: 10,
    borderRadius: 5,
    marginTop: 5,
    height: 50,
    marginBottom: 10,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 6,
  },
  dropDownListContainer: {
    backgroundColor: BaseColors.white,
    borderTopWidth: 1,
    borderTopColor: BaseColors.primary,
  },
  dropDownList: {
    justifyContent: "center",
    borderBottomColor: BaseColors.borderColor,
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  dropDownEmptyContainer: {
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  tooltip: {
    padding: 6,
    alignItems: "center",
    justifyContent: "center",
  },
  tooltipText: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.white,
  },
  listEmptyText: {
    textAlign: "center",
    color: BaseColors.grey50,
  },
  selectRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  buttonText: {
    fontSize: 16,
    color: "#555454",
    marginRight: 5,
    color: BaseColors.black100,
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    marginBottom: 3,
    textTransform: "capitalize",
  },
  inputButtonText: {
    fontSize: 16,
    color: "#555454",
    borderWidth: 1,
    padding: 5,
    borderRadius: 5,
    marginRight: 5,
    borderColor: "#555454",
    // fontFamily: FontFamily.medium,
  },
});
export default multiStyles;
