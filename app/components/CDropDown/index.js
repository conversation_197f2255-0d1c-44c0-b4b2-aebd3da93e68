/* eslint-disable react-native/no-inline-styles */
import { BaseColors } from "@config/theme";
import React, { useEffect, useRef, useState } from "react";
import { Text, TextInput, View } from "react-native";
import styles from "./styles";
import { Dropdown, MultiSelect } from "react-native-element-dropdown";
import { CustomIcon } from "@config/LoadIcons";
import { translate } from "../../lang/Translate";
import Checkbox from "@components/Checkbox";
import { FontFamily } from "@config/typography";
import isEmpty from "lodash-es/isEmpty";
import { ScrollView } from "react-native";

const CDropdown = ({
  ref,
  data = [],
  type = "",
  WIDTH_DROP_DOWN,
  labelplaceholder = "",
  defaultIcon = false,
  HEIGHT_DROP_DOWN,
  isBackgroundColor = false,
  setItem = () => {},
  isErrorMsg,
  isError,
  selectType,
  value,
  setSelected = () => {},
  dropdownDisable = false,
  logo,
  position = "auto",
  search = false,
  setSelectedCountry = () => {},
  setCountryCheckBox = () => {},
  countryCheckBox,
  selectAll = false,
  setCountryIds = () => {},
  stateCheckBox,
  setStateCheckBox = () => {},
  droDownName,
  setAudienceCheckBox,
  audienceCheckBox,
  labelField = "label",
  valueField = "value",
}) => {
  const ref1 = useRef(null);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [images1, setImages1] = useState("BsChevronDown");
  const [selectedIDs, setSelectedIds] = useState(data);

  // Dropdown Open
  const handleDropdownOpen = () => {
    setIsDropdownOpen(true);
    setImages1("BsChevronUp");
  };

  // Dropdown Close
  const handleDropdownClose = () => {
    setIsDropdownOpen(false);
    setImages1("BsChevronDown");
  };

  // set data into setSelectedIds state
  useEffect(() => {
    if (!isEmpty(data)) {
      setSelectedIds(data);
    }
  }, [data]);

  // this function is run when select all check is clicked
  const handleToggleCheckbox = () => {
    if (droDownName === "SelectedCountry") {
      setCountryCheckBox(!countryCheckBox);
      countryCheckBox ? setSelectedIds([]) : setSelectedIds(data);
    } else if (droDownName === "Audience") {
      setAudienceCheckBox(!audienceCheckBox);
      audienceCheckBox ? setSelectedIds([]) : setSelectedIds(data);
    } else {
      setStateCheckBox(!stateCheckBox);
      stateCheckBox ? setSelectedIds([]) : setSelectedIds(data);
    }
  };

  if (type === "multi") {
    return (
      <>
        <View
          style={[
            styles.container,
            {
              width: WIDTH_DROP_DOWN,
              minHeight: value.length > 1 ? 80 : 50,
            },
          ]}
        >
          <MultiSelect
            inside
            dropdownPosition={position}
            search={search}
            ref={ref}
            disable={dropdownDisable}
            style={[
              styles.dropdown,
              {
                borderBottomLeftRadius: isDropdownOpen
                  ? position === "top"
                    ? 23
                    : 0
                  : 5,
                borderBottomRightRadius: isDropdownOpen
                  ? position === "top"
                    ? 23
                    : 0
                  : 5,
                height: "auto",
                maxHeight: 120,
                padding: 4,
                backgroundColor: isBackgroundColor
                  ? "rgba(241, 242, 244, 1)"
                  : dropdownDisable
                    ? "#efefef"
                    : BaseColors.white,
                borderColor: isError
                  ? "#FF0000"
                  : isBackgroundColor
                    ? "rgba(241, 242, 244, 1)"
                    : "#8E8383",
                minHeight: HEIGHT_DROP_DOWN ? HEIGHT_DROP_DOWN : 50,
                overflow: "scroll",
              },
            ]}
            containerStyle={{
              borderWidth: 0.5,
              borderTopLeftRadius: position === "top" ? 23 : 0,
              borderTopRightRadius: position === "top" ? 23 : 0,
              borderBottomEndRadius: position === "top" ? 23 : 23,
              borderBottomLeftRadius: position === "top" ? 23 : 23,
              borderColor: BaseColors.DavtiveBorderColor,
              marginTop: 2,
              maxHeight: 150,
              marginBottom: position === "top" ? 44 : 0,
            }}
            placeholderStyle={[styles.placeholderStyle]}
            selectedTextStyle={styles.selectedTextStyle}
            inputSearchStyle={styles.inputSearchStyle}
            iconStyle={styles.iconStyle}
            itemTextStyle={{ color: BaseColors.black100 }}
            data={data}
            // renderInputSearch={renderInputSearch}
            labelField={labelField}
            valueField={valueField}
            placeholder={labelplaceholder}
            searchPlaceholder="Search..."
            value={value}
            renderInputSearch={(s) => {
              return (
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    marginHorizontal: 10,
                    gap: 10,
                    alignItems: "center",
                    marginTop: 10,
                  }}
                >
                  <View style={{ width: selectAll ? "64.5%" : "94.5%" }}>
                    <TextInput
                      style={{
                        fontSize: 16,
                        position: "relative",
                        color: BaseColors.fontColor,
                        borderWidth: 1,
                        borderRadius: 5,
                        height: 40,
                        borderColor: "#8E8383",
                        paddingLeft: 10,
                      }}
                      placeholder="Search...."
                      onChangeText={(e) => {
                        s(e);
                      }}
                    />
                  </View>
                  {selectAll && (
                    <View
                      style={{
                        flexDirection: "row",
                        gap: 5,
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <View>
                        <Text> Select All</Text>
                      </View>
                      <Checkbox
                        isChecked={
                          selectedIDs?.length === data?.length ? true : false
                        }
                        toggleCheckbox={handleToggleCheckbox}
                      />
                    </View>
                  )}
                </View>
              );
            }}
            renderItem={(item, selected) => (
              <>
                <View style={styles.selectRow}>
                  <Text style={styles.buttonText}>{item.label}</Text>
                </View>
              </>
            )}
            renderSelectedItem={(i, d) => {
              return (
                <View style={[styles.renderItemDataView]}>
                  <Text
                    style={{
                      color: BaseColors.black100,
                      fontSize: 14,
                      fontFamily: FontFamily.medium,
                      textTransform: "capitalize",
                    }}
                  >
                    {i?.label}
                  </Text>
                  <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                </View>
              );
            }}
            onChange={(item) => {
              setSelected(item);
              setItem(item);
              setCountryIds(item);
              setSelectedCountry(item);
              setSelectedIds(item);
            }}
            mode="default"
            selectedStyle={styles.selectedStyle}
            // flatListProps={{ ListHeaderComponent: renderSelectAllIcon }}
            renderRightIcon={() => (
              <CustomIcon name={images1} size={20} color={BaseColors.gray2} />
            )}
            onFocus={handleDropdownOpen}
            onBlur={handleDropdownClose}
          />
        </View>
        {isErrorMsg ? (
          <Text style={styles.errorTxt}>{translate(isErrorMsg)}</Text>
        ) : null}
      </>
    );
  } else {
    return (
      <>
        <View
          style={[
            styles.row,
            {
              width: WIDTH_DROP_DOWN,
              height: HEIGHT_DROP_DOWN,
            },
          ]}
        >
          <Dropdown
            dropdownPosition={position}
            ref={ref1}
            style={[
              styles.dropdown,
              {
                borderBottomLeftRadius: isDropdownOpen
                  ? position === "top"
                    ? 5
                    : 0
                  : 5,
                borderBottomRightRadius: isDropdownOpen
                  ? position === "top"
                    ? 5
                    : 0
                  : 5,
                backgroundColor: isBackgroundColor
                  ? "rgba(241, 242, 244, 1)"
                  : dropdownDisable
                    ? "#efefef"
                    : null,
                borderColor: isError
                  ? "#FF0000"
                  : isBackgroundColor
                    ? "rgba(241, 242, 244, 1)"
                    : "#8E8383",
                height: HEIGHT_DROP_DOWN ? HEIGHT_DROP_DOWN : 50,
              },
            ]}
            containerStyle={{
              borderWidth: 1,
              borderBottomLeftRadius: 5,
              borderBottomRightRadius: 5,
              borderTopLeftRadius: position === "top" ? 5 : 0,
              borderTopRightRadius: position === "top" ? 5 : 0,

              borderColor: "rgba(241, 242, 244, 1)",
              overflow: "hidden",
              marginTop: 2,
            }}
            selectedTextProps={{
              numberOfLines: 1,
            }}
            placeholderStyle={styles.placeholderStyle}
            selectedTextStyle={styles.selectedTextStyle}
            inputSearchStyle={styles.inputSearchStyle}
            iconStyle={styles.iconStyle}
            data={data}
            disable={dropdownDisable}
            itemTextStyle={{
              color: BaseColors.fontColor,
            }}
            placeholder={labelplaceholder}
            maxHeight={300}
            labelField={selectType === "courseType" ? "name" : "label"}
            valueField={selectType === "courseType" ? "id" : "value"}
            searchPlaceholder="Search..."
            value={value}
            onChange={(item) => {
              setItem(item);
            }}
            onChangeText={(item) => {}} // Keep search keyword
            renderRightIcon={() =>
              !defaultIcon ? (
                <View>
                  {logo ? (
                    <CustomIcon
                      name={logo}
                      size={20}
                      color={BaseColors.gray2}
                    />
                  ) : (
                    <CustomIcon
                      name={images1}
                      size={20}
                      color={BaseColors.gray2}
                    />
                  )}
                </View>
              ) : (
                <CustomIcon name={images1} size={8} color={BaseColors.gray2} />
              )
            }
            onFocus={handleDropdownOpen}
            onBlur={handleDropdownClose}
          />
        </View>

        {isErrorMsg ? (
          <View style={styles.errorMsgMainView}>
            <CustomIcon
              name={"BsExclamationCircle"}
              size={18}
              color={"#D6002E"}
            />
            <Text style={styles.errorTxt}>{translate(isErrorMsg)}</Text>
          </View>
        ) : null}
      </>
    );
  }
};

export default CDropdown;
