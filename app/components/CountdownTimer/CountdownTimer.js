import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet } from "react-native";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const CountdownTimer = ({
  duration,
  startTimer = false,
  selectedMsgId,
  renderingMsgId,
}) => {
  const [timeLeft, setTimeLeft] = useState(duration);

  // Reset timer when duration prop changes
  useEffect(() => {
    setTimeLeft(duration);
  }, [duration]);

  // Countdown logic
  useEffect(() => {
    let interval = null;
    if (selectedMsgId !== renderingMsgId) {
      setTimeLeft(duration);
    }
    if (startTimer) {
      interval = setInterval(() => {
        setTimeLeft((prevTimeLeft) => {
          if (prevTimeLeft <= 0) {
            clearInterval(interval);
            return 0;
          }
          return prevTimeLeft - 1;
        });
      }, 1000);
    }

    return () => {
      clearInterval(interval);
    };
  }, [duration, startTimer]);

  // Handle timer completion and reset
  useEffect(() => {
    if (timeLeft === 0) {
      setTimeout(() => {
        setTimeLeft(duration);
        // setTimerStarted(false);
      }, 2000);
    }
  }, [timeLeft, duration]);

  // Format time into MM:SS format
  const formattedTime = () => {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.timerText}>{formattedTime()}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  timerText: {
    fontSize: 14,
    fontFamily: FontFamily.InterMedium,
    color: BaseColors.black,
  },
});

export default CountdownTimer;
