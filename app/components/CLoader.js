import React from "react";
import { View, ActivityIndicator, StyleSheet, Dimensions } from "react-native";
import { BaseColors } from "../config/theme";

const styles = StyleSheet.create({
  mainConSty: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
    paddingBottom: 55,
  },
  lottieContainer: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
});
/**
 *Displays Loader
 * @function CLoader
 */
function CLoader(props) {
  const { size, color, otherTheme } = props;
  return (
    <View
      style={[
        styles.mainConSty,
        { backgroundColor: BaseColors.white, justifyContent: "center" },
        otherTheme,
      ]}
    >
      <ActivityIndicator
        size={size || "large"}
        color={color || BaseColors.primary}
        animating
      />
    </View>
  );
}

export default React.memo(CLoader);
