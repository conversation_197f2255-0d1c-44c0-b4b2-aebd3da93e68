import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
  },
  mainNestedComment: {
    flexDirection: "row",
    marginTop: 10,
  },
  nestedConner: {
    justifyContent: "flex-start",
    alignItems: "flex-start",
    flexDirection: "row",
    gap: 5,
  },
  commentDetails: {
    justifyContent: "flex-start",
    alignItems: "flex-start",
    width: "83%",
  },
  nestedCommentData: {
    width: Dimensions.get("window").width - 150,
    justifyContent: "flex-start",
    alignItems: "flex-start",
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalView: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
    height: Dimensions.get("window").height / 1.6,
  },

  bottomLoader: {
    height: 80,
  },
  centerMain: {
    height: Dimensions.get("window").height / 2.4,
  },

  modalTitleText: {
    fontSize: 24,
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginBottom: 15,
    textAlignVertical: "center",
  },
  dataMainView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  storyImageMainView: {
    height: 75,
    width: 75,
    overflow: "hidden",
    borderRadius: 43,
    borderWidth: 1,
  },
  storyImageView: {
    height: 75,
    width: 75,
    resizeMode: "cover",
  },
  headingText: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  descText: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.gray3,
  },
  separatorView: {
    borderWidth: 1,
    marginTop: 10,
  },
  sharedTextView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 10,
    backgroundColor: BaseColors.white,
    padding: 20,
  },
  textView: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  btnView: {
    marginBottom: 10,
    marginHorizontal: 19,
  },
  switchCardView: {
    borderRadius: 20,
    overflow: "hidden",
  },
  textContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",

    borderColor: "#8E8383",
    padding: 14,
  },
  textStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
  },
  profileDPStyle: {
    height: 40,
    width: 40,
    borderRadius: 25,
  },
  userDPStyle: {
    height: 40,
    width: 40,
    borderRadius: 25,
    borderColor: BaseColors.activeTab,
    borderWidth: 1,
  },
  renderItemMainView: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
  },
  userDataComponent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 10,
  },
  textComponentStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.black,
  },
  commentText: {
    fontSize: 14,
    fontFamily: FontFamily.InterRegular,
    color: BaseColors.black,
  },
  likeCount: {
    fontSize: 14,
    fontFamily: FontFamily.InterRegular,
    color: "#8E8383",
  },
  commentText: {
    fontSize: 14,
    fontFamily: FontFamily.InterRegular,
    color: BaseColors.black,
  },
  replyText: {
    paddingTop: 5,
    fontSize: 13,
    fontFamily: FontFamily.InterRegular,
    color: "#8E8383",
  },
  TimeText: {
    fontSize: 12,
    fontFamily: FontFamily.InterRegular,
    color: "#555454",
  },

  timeAndProfileTextMain: {
    flexDirection: "row",
    gap: 5,
    justifyContent: "center",
    alignItems: "center",
  },
  likeBtnViewStyle: {
    alignItems: "center",
    position: "absolute",
    right: 0,
    top: 0,
    paddingTop: 20,
  },
  nestedStyle: {
    height: "100%",
    alignItems: "center",
  },
  commentData: {
    justifyContent: "flex-start",
    alignItems: "flex-start",
    width: "80%",
  },
  mainModal: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  closeBtn: {
    borderWidth: 1,
    height: 24,
    width: 24,
    borderRadius: 5,
    alignItems: "center",
    justifyContent: "center",
  },
  userDpMain: {
    position: "absolute",
    width: "100%",
    backgroundColor: BaseColors.White,
    height: 80,
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    borderTopWidth: 2,
    borderColor: BaseColors.borderColor,
  },
  sendComment: {
    backgroundColor: BaseColors.activeTab,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    minWidth: 75,
  },
  sendText: {
    color: "#fff",
    fontSize: 15,
    fontFamily: FontFamily.RobotoMedium,
  },
});

export default styles;
