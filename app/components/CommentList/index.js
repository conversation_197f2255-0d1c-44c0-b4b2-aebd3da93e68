import React from "react";
import {
  Modal,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
// import FastImage from "react-native-fast-image";
import NoRecord from "@components/NoRecord";
import { isEmpty } from "lodash-es";
import { useSelector } from "react-redux";
import MiniLoader from "@components/MiniLoader";
import { FontFamily } from "@config/typography";

const FastImage = require("react-native-fast-image");

const CommentList = ({
  visible,
  setModalVisible,
  listData = [],
  commentLodging = false,
  commentModalLodging = false,
  handleHideComment,
  submitCommentText,
  handleReply,
  handleCommentLike,
  setComment,
  comment,
  textInputRef,
  handleBottom,
  bottomLoader,
  UserId,
  onPressItemClick
}) => {
  // redux commentList state
  const { userData, commentData } = useSelector((auth) => auth.auth);

  const renderNestedComment = (item, index, mainIndex) => {
    const data = item;
    return (
      <View style={styles.mainNestedComment}>
        {/* Nested Comment Header Image */}
        <View style={styles.nestedConner}>
          <View>
            {data?.user_data[0]?.user_dp ? (
              <FastImage
                source={{ uri: data?.user_data[0]?.user_dp }}
                style={styles.profileDPStyle}
              />
            ) : null}
          </View>

          {/* comment data */}
          <View style={styles.nestedCommentData}>
            <View style={styles.timeAndProfileTextMain}>
              {data?.user_data[0]?.username ? (
                <Text style={styles.textComponentStyle}>
                  {data?.user_data[0]?.username}
                </Text>
              ) : null}
              <Text style={styles.TimeText}>{data?.stamp}</Text>
            </View>
            <Text style={styles.commentText}>{data?.comment?.trim()}</Text>

            {UserId === userData?.user_id ? (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => handleHideComment(data)}
              >
                {data?.is_hide ? (
                  <Text style={styles.replyText}>{translate("unhide")}</Text>
                ) : (
                  <Text style={styles.replyText}>{translate("hide")}</Text>
                )}
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        {/*Nested Comment like button */}
        <TouchableOpacity
          style={styles.nestedStyle}
          onPress={() =>
            handleCommentLike(data?.comment_id, "nested", mainIndex, index)
          }
          activeOpacity={0.8}
        >
          <CustomIcon
            name={data?.is_liked ? "BsSuitHeartFill" : "heart-outline"}
            size={14}
            color={data?.is_liked ? BaseColors.activeTab : "#322F2F"}
          />
          {Number(data?.likes_counts) > 0 ? (
            <Text style={styles.likeCount}>{Number(data?.likes_counts)}</Text>
          ) : null}
        </TouchableOpacity>
      </View>
    );
  };

  const renderItem = ({ item, index }) => {
    const mainIndex = index;
    const data = item;
    return (
      <View style={styles.renderItemMainView}>
        {/* Header Image */}
        <View style={styles.userDataComponent}>
          <TouchableOpacity activeOpacity={0.8} onPress={() => onPressItemClick(data?.user_data[0])}>
            {data?.user_data[0]?.user_dp ? (
              <FastImage
                source={{ uri: data?.user_data[0]?.user_dp }}
                style={styles.profileDPStyle}
              />
            ) : null}
          </TouchableOpacity>

          {/* comment data */}
          <View style={styles.commentData}>
            <TouchableOpacity 
              activeOpacity={0.8} 
              style={styles.timeAndProfileTextMain} 
              onPress={() => onPressItemClick(data?.user_data[0])}
              >
              {data?.user_data[0]?.username ? (
                <Text style={styles.textComponentStyle}>
                  {data?.user_data[0]?.username}
                </Text>
              ) : null}

              <Text style={styles.TimeText}>{data?.stamp}</Text>
            </TouchableOpacity>
            <Text style={styles.commentText}>{data?.comment}</Text>

            <View style={{ flexDirection: "row", gap: 15 }}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => handleReply(data)}
              >
                <Text style={styles.replyText}>{translate("reply")}</Text>
              </TouchableOpacity>

              {UserId === userData?.user_id ? (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => handleHideComment(data)}
                >
                  {data?.is_hide ? (
                    <Text style={styles.replyText}>{translate("unhide")}</Text>
                  ) : (
                    <Text style={styles.replyText}>{translate("hide")}</Text>
                  )}
                </TouchableOpacity>
              ) : null}
            </View>

            {!isEmpty(data?.replies) ? (
              <FlatList
                data={data?.replies}
                renderItem={({ item, index }) =>
                  renderNestedComment(item, index, mainIndex)
                }
              />
            ) : null}
          </View>
        </View>

        {/* like button */}

        <TouchableOpacity
          style={styles.likeBtnViewStyle}
          onPress={() => handleCommentLike(data?.comment_id, "main", mainIndex)}
          activeOpacity={0.8}
        >
          <CustomIcon
            name={data?.is_liked ? "BsSuitHeartFill" : "heart-outline"}
            size={14}
            color={data?.is_liked ? BaseColors.activeTab : "#322F2F"}
          />
          {Number(data.likes_counts) ? (
            <Text style={styles.likeCount}>{Number(data.likes_counts)}</Text>
          ) : null}
        </TouchableOpacity>
      </View>
    );
  };

  const noRecordView = () => {
    return <NoRecord title="noComments" />;
  };

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (isCloseToBottom && commentData?.next_enable) {
      handleBottom();
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <View style={styles.ovarlayStyle}>
          <View style={styles.modalView}>
            <View style={styles.mainModal}>
              <Text style={styles.modalTitleText}>
                {translate("commentsText")}
              </Text>

              <TouchableOpacity
                style={styles.closeBtn}
                activeOpacity={0.8}
                onPress={() => setModalVisible(false)}
              >
                <CustomIcon name="BsX" size={20} color={BaseColors.black} />
              </TouchableOpacity>
            </View>
            {commentModalLodging ? (
              <View style={styles.centerMain}>
                <MiniLoader size="small" />
              </View>
            ) : (
              <ScrollView
                onScroll={handleScroll}
                scrollEventThrottle={16}
                showsVerticalScrollIndicator={false}
              >
                <View style={{ marginBottom: 60 }}>
                  <FlatList
                    data={listData}
                    renderItem={renderItem}
                    ListEmptyComponent={noRecordView}
                    showsVerticalScrollIndicator={false}
                  />
                  {bottomLoader ? (
                    <View style={styles.bottomLoader}>
                      <MiniLoader size="small" />
                    </View>
                  ) : null}
                </View>
              </ScrollView>
            )}
          </View>
          <View style={styles.userDpMain}>
            <View style={{ width: "10%" }}>
              {userData?.profile_picture ? (
                <FastImage
                  source={{ uri: userData?.profile_picture }}
                  style={styles.userDPStyle}
                />
              ) : null}
            </View>
            <View style={{ width: "70%" }}>
              <TextInput
                placeholder="Add a comment..."
                placeholderTextColor={"#B5B5B5"}
                style={{
                  paddingLeft: 10,
                  color: BaseColors.fontColor,
                  fontFamily: FontFamily.RobotoRegular,
                  fontSize: 14,
                }}
                onChangeText={(e) => setComment(e)}
                value={comment}
                ref={textInputRef}
                returnKeyType="send"
              />
            </View>
            <View>
              <TouchableOpacity
                style={styles.sendComment}
                activeOpacity={0.8}
                onPress={() => submitCommentText()}
              >
                {commentLodging ? (
                  <ActivityIndicator animating color={BaseColors.White} />
                ) : (
                  <Text style={styles.sendText}>{translate("send")}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default CommentList;
