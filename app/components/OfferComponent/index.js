import { images } from "@config/images";
import <PERSON>tieView from "lottie-react-native";
import { ImageBackground, TouchableOpacity } from "react-native";
import { Text, View } from "react-native";
import styles from "./styles";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import FastImage from "react-native-fast-image";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

const CountdownTimer = ({ targetDate }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date().getTime();
      const distance = new Date(targetDate).getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor(
            (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
          ),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000),
        });
      } else {
        clearInterval(interval);
        setTimeLeft({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
        });
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [targetDate]);

  return (
    <Text style={styles.clockText}>
      {`Closes - ${timeLeft?.days}d ${timeLeft?.hours}h ${timeLeft?.minutes}m ${timeLeft?.seconds}s`}
    </Text>
  );
};

const OfferComponent = ({ handleToSpecialOffer }) => {
  const { specialOfferData } = useSelector((auth) => auth.auth);

  return (
    <View style={styles.discountMainView}>
      {/* <ImageBackground
          source={images.offerBanner}
            style={styles.background}
            resizeMode="cover" // Options: 'cover', 'contain', 'stretch', etc.
          >

                <View style={styles.discountTxtViewMain}>
                  <Text style={styles.discountTextView} numberOfLines={2}>
                   {specialOfferData?.special_offer_title}
                  </Text>

                 <CountdownTimer targetDate={specialOfferData?.special_offer_end_date} />
                  
                </View>
                  <TouchableOpacity
                    onPress={() => handleToSpecialOffer()}
                    activeOpacity={0.8}
                    style={styles.claimOfferView}
                  >
                    <Text
                      style={[
                        styles.exploreView,
                      ]}
                    >
                      Claim Offer
                    </Text>
                      <FastImage
                        source={images.RightArrow}
                        style={{
                          width: 22,
                          height: 22,
                        }}
                      />
                  </TouchableOpacity>
                
                <View style={{
                  position: 'absolute',
                  right: 8,
                  top: 30
                }}>
                  <LottieView
                    source={images.offerLottie}
                    autoPlay={true}
                    loop
                    style={{
                      height: 60,
                      width: 60,
                    }}
                  />
                </View>
          </ImageBackground> */}
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => handleToSpecialOffer()}
        style={styles.offerMainView}
      >
        <View style={styles.offerMainViewText}>
          <Text style={styles.discountTextView} numberOfLines={2}>
            {specialOfferData?.special_offer_title}
          </Text>
          <CountdownTimer
            targetDate={specialOfferData?.special_offer_end_date}
          />
        </View>
        <View>
          {specialOfferData?.banner_image_url ? (
            <FastImage
              source={{
                uri: specialOfferData?.banner_image_url,
                priority: "high",
              }}
              style={{ height: 60, width: 60 }}
              resizeMode="cover"
            />
          ) : (
            <LottieView
              source={images.offerAnimation}
              autoPlay={true}
              loop
              style={{ height: 60, width: 60 }}
            />
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default OfferComponent;
