// Import React and Component
// import { images } from "@config/images";
// import LottieView from "lottie-react-native";
import React from "react";
import { View, Modal } from "react-native";

import FastImage from "react-native-fast-image";
import styles from "./styles";
import { TouchableOpacity } from "react-native";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { TouchableWithoutFeedback } from "react-native";

const ProfilePreview = ({ visible, setModalVisible, image = "" }) => {
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
  };
  return (
    <Modal
      transparent={true}
      animationType={"none"}
      visible={visible}
      onRequestClose={() => {
        console.log("close modal");
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <View style={styles.modalBackground}>
          <View style={styles.activityIndicatorWrapper}>
            <View
              style={{
                width: "100%",
                alignItems: "flex-end",
                zIndex: 1111,
                position: "absolute",
                top: 8,
                right: 8,
              }}
            >
              <TouchableOpacity
                style={{
                  borderWidth: 1,
                  height: 24,
                  width: 24,
                  borderRadius: 5,
                  alignItems: "center",
                  justifyContent: "center",
                  shadowColor:BaseColors.black,
                  backgroundColor:BaseColors.white,
                  elevation:25
                }}
                activeOpacity={0.8}
                onPress={() => setModalVisible(!visible)}
              >
                <CustomIcon name="BsX" size={20} color={BaseColors.black} />
              </TouchableOpacity>
            </View>

            <FastImage
              source={{ uri: image }}
              style={{ height: "100%", width: "100%" }}
              resizeMode="contain"
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default ProfilePreview;
