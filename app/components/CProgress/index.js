import React from "react";
import { Dimensions, StyleSheet, Text, TouchableOpacity } from "react-native";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Bar } from "react-native-progress";

const CProgressBar = ({ value = 0, handleToPressProgress = () => {} }) => {
  return (
    <TouchableOpacity
      style={styles.mainView}
      activeOpacity={0.8}
      onPress={handleToPressProgress}
    >
      <Text style={styles.mainViewText}>Complete Your Profile</Text>
      <Text
        style={[
          {
            color: BaseColors.black100,
            fontFamily: FontFamily.InterSemiBold,
            fontSize: 16,
          },
        ]}
      >
        {" "}
        {value}%
      </Text>
      <Bar
        progress={value / 100}
        width={Number(Dimensions.get("window").width) < 400 ?  Dimensions.get("window").width - 60 : Dimensions.get("window").width / 2 - 50}
        height={12}
        borderRadius={12}
        animationType="decay"
        borderWidth={0}
        color={BaseColors.activeTab}
        useNativeDriver={true}
        unfilledColor="rgba(228, 20, 20, 0.11)"
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  mainView: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(185, 188, 200, 0.1)",
    borderRadius: 4,
    borderColor: "rgba(185, 188, 200, 0.1)",
    borderWidth: 1,
    height: "auto",
    padding: 8,
    gap: 4,
    flexWrap: "wrap",
  },
  mainViewText: {
    textAlign: "center",
    textAlignVertical: "center",
    fontSize: 14,
    fontFamily: FontFamily.RobotoMedium,
    fontWeight: "500",
    lineHeight: 18.86,
    letterSpacing: 0.03,
    color: BaseColors.gray4,

    marginBottom: 2,
  },
});

export default CProgressBar;
