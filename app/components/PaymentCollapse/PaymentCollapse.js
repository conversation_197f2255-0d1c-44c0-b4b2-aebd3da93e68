import React, { useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import styles from "./styles";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import { images } from "@config/images";
import FastImage from "react-native-fast-image";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

const PaymentsListRenderItem = ({ navigation, item, index, type = "" }) => {
  const [activeIndex, setActiveIndex] = useState(0);

  const toggleCollapse = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <Animated.View entering={FadeInDown} key={index} style={styles.mainBox}>
      <TouchableOpacity
        onPress={
          type === "list"
            ? () => navigation.navigate("PaymentDetails", { paymentList: item })
            : () => toggleCollapse(index)
        }
        activeOpacity={0.8}
        style={{ flex: 1 }}
      >
        <View
          style={
            type === "list"
              ? styles.itemContainer
              : activeIndex === index
                ? styles.listItemContainer
                : styles.itemContainer
          }
        >
          {/* img */}
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <View
              style={{
                width: 52,
                height: 52,
                backgroundColor: BaseColors.activeTab,
                borderColor: "#104cba",
                borderRadius: 50,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {item?.planType === "boost" ? (
                <CustomIcon
                  name={"boost"}
                  size={32}
                  color={BaseColors.white}
                  style={{ paddingTop: 6, paddingRight: 2 }}
                />
              ) : item?.plan_type === "coupon" || item?.plan_type === "special_offer" ? (
                <FastImage
                  source={images.discountPercentage}
                  style={{
                    width: 30,
                    height: 30,
                  }}
                  tintColor={BaseColors.white}
                />
              ) : (
                    <CustomIcon name={"crown"} size={25} color={BaseColors.white} />
                  )}
            </View>
          </View>
          {/* plan Name and date */}
          <View style={{ width: "55%", justifyContent: "center" }}>
            <Text numberOfLines={2} style={styles.listItemText}>{item?.title}</Text>
            <Text style={styles.listItemDate}>{item?.date}</Text>
          </View>
          {/* plan price */}
          <View style={styles.listItemAmount}>
            <Text style={styles.listItemText}>{item?.amount}</Text>
          </View>
        </View>
      </TouchableOpacity>
      {activeIndex === index && type !== "list" && (
        <>
          {/* bank details view */}
          <View style={styles.bankDetailsContainer}>
            {/* <View>
                <Text style={styles.bankName}>HDFC Bank 1236</Text>
                <Text style={styles.bankStatus}>Completed</Text>
              </View> */}

            {/* start date  & end date view */}
            <View style={styles.dateContainer}>
              <View style={styles.dateItem}>
                <Text style={styles.title}>Start Date</Text>
                <Text style={styles.itemDate}>{item?.plan_expire_start}</Text>
              </View>
              <View style={styles.dateItem}>
                <Text style={styles.title}>End Date</Text>
                <Text style={styles.itemDate}>{item?.plan_expire_date}</Text>
              </View>
            </View>

            {/* Frequency &  Dabit on */}
            {/* <View style={styles.FrequencyContainer}>
                <View style={styles.dateItem}>
                  <Text style={styles.title}>Frequency</Text>
                  <Text style={styles.itemDate}>As presented </Text>
                </View>
                <View style={styles.dateItem}>
                  <Text style={styles.title}>Dabit on</Text>
                  <Text style={styles.itemDate}>
                    As and when required, till Feb 10,2026
                  </Text>
                </View>
              </View> */}
          </View>
        </>
      )}
    </Animated.View>
  );
};

export default PaymentsListRenderItem;
