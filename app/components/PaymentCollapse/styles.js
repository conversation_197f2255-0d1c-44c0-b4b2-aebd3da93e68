import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  mainBox: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  mainView: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  headerTitle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 20,
    color: BaseColors.black,
  },
  listItemContainer: {
    borderWidth: 1,
    flexDirection: "row",
    width: "100%",
    padding: 10,
    gap: 10,
    borderColor: BaseColors.textLightGray,
    borderTopEndRadius: 10,
    borderTopLeftRadius: 10,
  },
  itemContainer: {
    borderWidth: 1,
    flexDirection: "row",
    width: "100%",
    padding: 10,
    gap: 10,
    borderColor: BaseColors.textLightGray,
    borderRadius: 10,
  },
  listItemText: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
    color: BaseColors.black,
    marginBottom: 5
  },
  listPlanItemText: {
    fontFamily: FontFamily.RobotSemiBold,
    fontSize: 8,
    color: BaseColors.white,
    textAlign: "center",
    textTransform: "uppercase",
  },
  listItemDate: {
    fontFamily: FontFamily.InterRegular,
    fontSize: 13,
    color: BaseColors.gray11,
  },
  itemDate: {
    fontFamily: FontFamily.InterRegular,
    fontSize: 14,
    color: BaseColors.gray11,
  },
  listItemPriceText: {
    minWidth: 80,
    borderWidth: 1,
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
    color: BaseColors.black,
    textAlign: "right",
  },
  bankDetailsContainer: {
    borderWidth: 1,
    borderColor: BaseColors.textLightGray,
    borderBottomRightRadius: 10,
    borderBottomLeftRadius: 10,
    borderTopWidth: 0,
    padding: 10,
  },
  listItemAmount: {
    flex:1,
    justifyContent: "center",
    alignItems: 'flex-end',
    maxWidth: '40%'
  },
  bankName: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
    color: BaseColors.black,
  },
  title: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
    color: BaseColors.black,
  },
  bankStatus: {
    fontFamily: FontFamily.InterRegular,
    fontSize: 13,
    color: BaseColors.gray11,
  },
  dateContainer: {
    flexDirection: "row",
    width: "100%",
    marginVertical: 10,
  },
  FrequencyContainer: {
    flexDirection: "row",
    width: "100%",
  },
  dateItem: {
    width: "50%",
  },
});
export default styles;
