import { BaseColors } from '@config/theme';
import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  mainView: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'hsla(360, 20%,2%, 0.6)',
    // paddingBottom: 20,
    paddingTop: '50%',
  },
  background: {
    backgroundColor: BaseColors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 20,
    elevation: 3,
  },
  barStyle: {
    alignSelf: 'center',
    backgroundColor: BaseColors.borderColor,
    borderRadius: 8,
    width: 65,
    height: 5,
    marginBottom: 10,
  },
  sheetView: {
    flexGrow: 1,
    // minHeight: 225,
  },
  titleView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 7,
    marginVertical: 5,
    backgroundColor: BaseColors.whiteColor,
    borderRadius: 50,
  },
  title: {
    justifyContent: 'center',
    alignContent: 'center',
    fontSize: 16,
    paddingHorizontal: 15,
    color: BaseColors.textInputLabel,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    flex: 1,
    // marginLeft: -36,
  },
  viewText: {
    justifyContent: 'center',
    alignContent: 'center',
    fontSize: 16,
    paddingHorizontal: 15,
    paddingVertical: 20,
    color: BaseColors.textInputLabel,
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  clearView: {
    position: 'absolute',
    right: 20,
    top: 10,
    zIndex: 1,
  },
  clearTxt: {
    color: BaseColors.primary2,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
});
