/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { useCallback } from "react";
import {
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { translate } from "../../lang/Translate";
import styles from "./styles";

/**
 * Component for BottomDrawerAction
 * @module BottomDrawerAction
 *
 */
const IOS = Platform.OS === "ios";
export default function BottomDrawerAction(props) {
  const {
    modalTitle,
    visible,
    setVisible,
    from,
    initData = {},
    navigation,
    reSetInitData = () => {},
  } = props;

  const renderComponent = useCallback(() => {
    return null;
  }, [from, modalTitle, initData]);
  const handleOverlayClick = (e) => {
    // Check if the click event target is the overlay
    if (e.target === e.currentTarget) {
      setVisible(false);
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      close={() => {
        setVisible(!visible);
      }}
      onRequestClose={() => {
        setVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.mainView}
          enabled
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          contentContainerStyle={{ zIndex: 10000 }}
          keyboardShouldPersistTaps={"handled"}
        >
          <View>
            <View style={styles.background}>
              <View style={styles.barStyle} />
              <View
                style={{
                  flexDirection: "row",
                  borderBottomWidth: 1,
                  borderColor: BaseColors.borderColor,
                  paddingVertical: 10,
                }}
              >
                <CustomIcon
                  onPress={() => {
                    setVisible(!visible);
                    reSetInitData();
                  }}
                  name="close"
                  size={16}
                  color={BaseColors.textInputLabel}
                  style={{
                    position: "absolute",
                    left: 20,
                    zIndex: 1111111,
                    top: 10,
                  }}
                />
                <Text style={styles.title}>{translate(modalTitle)}</Text>
              </View>
              <ScrollView
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                style={{ flexGrow: 1 }}
              >
                <>{renderComponent(from)}</>
              </ScrollView>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
}
