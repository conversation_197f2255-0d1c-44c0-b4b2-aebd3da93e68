import React, { useCallback } from "react";
import {
  Modal,
  Text,
  View,
  // TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import { followListData } from "@config/staticData";
import NoRecord from "@components/NoRecord";
import CInput from "@components/TextInput";
import authAction from "@redux/reducers/auth/actions";
import { useDispatch, useSelector } from "react-redux";
import { handleFollowToggle } from "@app/utils/commonFunction";
import MiniLoader from "@components/MiniLoader";

const FastImage = require("react-native-fast-image");

const { setUserLikeList } = authAction;
const ShareListModal = ({
  visible,
  setModalVisible,

  listData = [],
  type = "share", // share || reel || post
  title = "",
  buttonText,
  onChangeSearch = () => {},
  handleBtnPress = () => {},
  loader = false,
  setLoader = () => {},
  onPressItemClick = () => {},
  likeViewCount = 0,
  likeCount = 0,
  searchValue,
  shareItem = "",
}) => {
  const dispatch = useDispatch();
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
      onChangeSearch("");
      Keyboard.dismiss();
    }
    // setModalVisible(false);
    Keyboard.dismiss();
  };
  const { userLikeList, userData } = useSelector((s) => s?.auth);
  const followUnFollowAnimation = (index) => {
    userLikeList.viewData[index].is_followed =
      !userLikeList.viewData[index].is_followed;
    dispatch(setUserLikeList(userLikeList));
  };
  const handleToBtnPressForFollow = async (data) => {
    followUnFollowAnimation(data?.index);
    const resp = await handleFollowToggle(data?.item);
    if (resp !== undefined && resp?.data?.success) {
      console.log("Success");
    } else {
      followUnFollowAnimation(data?.index);
    }
  };
  const renderItem = (item, index) => {
    const data = item?.item;

    return (
      // Item Profile data
      <TouchableOpacity
        style={[
          styles.renderItemMainView,
          {
            borderBottomWidth:
              item?.index !== followListData?.length - 1 ? 0.75 : null,
          },
        ]}
        activeOpacity={0.9}
        onPress={() => onPressItemClick(data)}
      >
        {/* Header Image */}
        <View style={styles.userDataComponent}>
          <View>
            <FastImage
              source={{ uri: data?.user_dp }}
              style={styles.profileDPStyle}
            />
          </View>
          <Text style={styles.textComponentStyle}>{data?.username}</Text>
        </View>

        {userData?.user_id !== data?.user_id ? (
          <TouchableOpacity
            style={[
              styles.btnViewStyle,
              {
                backgroundColor:
                  type !== "share" && !data?.is_followed
                    ? BaseColors.activeTab
                    : BaseColors.white,
              },
            ]}
            onPress={() =>
              type !== "share"
                ? handleToBtnPressForFollow({
                    item: data?.user_id,
                    index: item?.index,
                  })
                : handleBtnPress(
                    {
                      item: data?.user_id,
                      index: item?.index,
                    },
                    shareItem
                  )
            }
          >
            <Text
              style={[
                styles.btnTextStyle,
                {
                  color:
                    type !== "share" && !data?.is_followed
                      ? BaseColors.white
                      : BaseColors.fontColor,
                },
              ]}
            >
              {type === "share"
                ? buttonText
                : !data?.is_followed
                  ? "Follow"
                  : "Following"}
            </Text>
          </TouchableOpacity>
        ) : null}
      </TouchableOpacity>
    );
  };

  const noRecordView = () => {
    return (
      <View style={{ marginBottom: 50 }}>
        <NoRecord title="noRecordFound" />
      </View>
    );
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <KeyboardAvoidingView
        style={styles.centeredView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        {/* <TouchableWithoutFeedback onPress={handleOverlayClick}> */}
        <View style={styles.ovarlayStyle}>
          <View style={styles.modalView}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              {type !== "share" ? (
                <Text style={styles.modalTitleText}>{translate(title)}</Text>
              ) : (
                <Text style={styles.modalTitleText}>
                  {translate("shareText")}
                </Text>
              )}

              <TouchableOpacity
                style={{
                  borderWidth: 1,
                  height: 24,
                  width: 24,
                  borderRadius: 5,
                  alignItems: "center",
                  justifyContent: "center",
                }}
                activeOpacity={0.8}
                onPress={() => setModalVisible(false)}
              >
                <CustomIcon name="BsX" size={20} color={BaseColors.black} />
              </TouchableOpacity>
            </View>

            <View style={{ marginBottom: 20 }}>
              {type !== "share" ? (
                <CInput
                  placeholderText={"Search"}
                  leftSideIcon={"Search-1"}
                  leftSideIconSize={20}
                  onChange={onChangeSearch}
                  value={searchValue}
                />
              ) : null}

              {type === "reel" ? (
                <View style={styles.likeCountViewStyle}>
                  <CustomIcon
                    name="BsPlayCircleFill"
                    size={20}
                    color={BaseColors.black}
                  />
                  <Text style={styles.likeCountTextStyle}>
                    {likeViewCount} Plays
                  </Text>
                </View>
              ) : null}

              {type !== "share" ? (
                <View style={styles.likeByViewStyle}>
                  <Text style={styles.likeByTextStyle}>
                    {translate("likeByText")}
                  </Text>
                  <Text style={styles.likeByTextStyle}>{likeCount} Likes</Text>
                </View>
              ) : null}
              {loader === true ? (
                <View
                  style={{
                    minHeight: Dimensions.get("window").height / 3,
                    justifyContent: "center",
                  }}
                >
                  <MiniLoader />
                </View>
              ) : (
                <FlatList
                  data={listData}
                  renderItem={renderItem}
                  ListEmptyComponent={noRecordView}
                  style={{ marginBottom: 50 }}
                />
              )}
            </View>
            {/* )} */}
          </View>
        </View>
        {/* </TouchableWithoutFeedback> */}
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default ShareListModal;
