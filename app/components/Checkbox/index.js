/* eslint-disable react-native/no-inline-styles */
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "../../config/theme";
import React from "react";
import { View, Text, TouchableOpacity, Image, Platform } from "react-native";
// import Animated, { FadeInDown } from "react-native-reanimated";
import { FontFamily } from "@config/typography";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

const Checkbox = ({ isChecked, toggleCheckbox, title, disabled }) => {
  return (
    <TouchableOpacity
      onPress={toggleCheckbox}
      activeOpacity={0.9}
      disabled={disabled}
      style={{
        flexDirection: "row",
        justifyContent: "flex-start",
        alignItems: "center",
        marginTop: Platform.OS === "android" ? 2 : 0,
      }}
    >
      <View
        style={{
          width: 20,
          height: 20,
          borderWidth: !isChecked ? 1 : 0,
          borderColor: isChecked ? BaseColors.activeTab : "#a1a1a1",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: isChecked ? null : "#F4F4F5", // Change the background color when checked
          borderRadius: 5,
        }}
      >
        {isChecked && (
          <CustomIcon
            name="BsFillCheckSquareFill"
            size={20}
            color={BaseColors.activeTab}
            style={{ borderRadius: 5 }}
          />
        )}
      </View>
      <Text
        style={{
          marginLeft: 6,
          fontSize: 14,
          fontFamily: FontFamily.RobotoRegular,
          color: "rgba(26, 26, 26, 0.80)",
        }}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default Checkbox;
