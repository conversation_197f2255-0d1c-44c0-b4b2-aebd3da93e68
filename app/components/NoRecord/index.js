import React from "react";
import { Text, View } from "react-native";
import styles from "./styles";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { isEmpty } from "lodash-es";
import { translate } from "../../lang/Translate";
import CButton from "@components/CButton";
const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;

function NoRecord({
  title = "",
  type = "",
  description = "",
  iconName = "BsChat",
  groupChat,
  onPressCreateGroup = () => {},
}) {
  if (type === "chat") {
    return (
      <View style={styles.main}>
        <View style={styles.chatMain}>
          <View style={styles.chatIconMain}>
            <CustomIcon
              name={!isEmpty(iconName) ? iconName : "BsChat"}
              size={30}
              color={BaseColors.black}
            />
          </View>
        </View>
        {groupChat ? (
          <CButton
            containerStyle={{
              width: "50%",
              alignSelf: "center",
              marginVertical: 30,
              height: 45,
            }}
            onBtnClick={() => onPressCreateGroup()}
          >
            Create Groups
          </CButton>
        ) : (
          <>
            <Text style={styles.emptyTextChat}>{translate(title)}</Text>
            <Text style={styles.descriptionText}>{translate(description)}</Text>
          </>
        )}
      </View>
    );
  } else {
    return (
      <View style={styles.main}>
        <View style={styles.mainView}>
          <LottieView
            autoSize={true}
            source={images.noData}
            autoPlay={true}
            style={styles.loader}
          />
        </View>
        <Text style={styles.emptyText}>{translate(title)}</Text>
      </View>
    );
  }
}

export default NoRecord;
