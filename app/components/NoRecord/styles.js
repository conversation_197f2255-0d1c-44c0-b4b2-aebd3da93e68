import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  main: {
    flex: 1,
    paddingHorizontal: 14,
    marginBottom: 50,
    justifyContent: "center",
  },
  mainView: {
    height: 175,
    justifyContent: "center",
    alignItems: "center",
  },
  chatMain: {
    justifyContent: "center",
    alignItems: "center",
  },
  loader: {
    height: 175,
    width: "80%",
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 200,
  },
  emptyTextChat: {
    fontSize: 20,
    color: BaseColors.black,
    fontFamily: FontFamily.InterBold,
    textAlign: "center",
    marginTop: 15,
  },
  emptyText: {
    fontSize: 20,
    color: BaseColors.activeTab,
    fontFamily: FontFamily.InterRegular,
    textAlign: "center",
  },
  descriptionText: {
    fontSize: 16,
    color: BaseColors.gray6,
    fontFamily: FontFamily.InterRegular,
    textAlign: "center",
    marginTop: 10,
  },
  chatIconMain: {
    height: 100,
    width: 100,
    backgroundColor: "red",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 100,
    backgroundColor: "rgba(214, 0, 46, 0.2)",
    borderWidth: 1,
    borderColor: BaseColors.activeTab,
  },
});
