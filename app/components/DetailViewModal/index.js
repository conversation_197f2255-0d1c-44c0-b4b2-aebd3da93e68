import React, { useCallback } from "react";
import {
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
  FlatList,
  Modal,
  ScrollView,
} from "react-native";

import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import { followListData } from "@config/staticData";
// import FastImage from "react-native-fast-image";
import NoRecord from "@components/NoRecord";
import { useSelector } from "react-redux";
import styles from "./styles";

const FastImage = require("react-native-fast-image");

const DetailViewModal = ({
  visible,
  setModalVisible,
  listData = [],
  title = "",
  buttonText,
  user_id,

  handleBtnPress = () => {},
}) => {
  const { userData, userFollowList, userFollowingList } = useSelector(
    (s) => s.auth
  );

  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };

  const renderItem = useCallback(
    (item, index) => {
      const data = item?.item;
      return (
        <View style={styles.renderItemMainViewStyle}>
          <Text style={styles.headerViewText}>{data?.title}</Text>
          <Text style={styles.itemDescText}>{data?.desc}</Text>
        </View>
      );
    },
    [listData, userFollowList, userFollowingList]
  );

  const noRecordView = () => {
    return <NoRecord title="noShareList" />;
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <View style={styles.ovarlayStyle}>
          <View style={styles.modalView}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={styles.modalTitleText}>{translate(title)}</Text>

              <TouchableOpacity
                style={{
                  borderWidth: 1,
                  height: 24,
                  width: 24,
                  borderRadius: 5,
                  alignItems: "center",
                  justifyContent: "center",
                }}
                activeOpacity={0.8}
                onPress={() => setModalVisible(false)}
              >
                <CustomIcon name="BsX" size={20} color={BaseColors.black} />
              </TouchableOpacity>
            </View>

            <View style={{ marginBottom: 20 }}>
              <FlatList
                data={listData}
                renderItem={renderItem}
                ListEmptyComponent={noRecordView}
                scrollEnabled
              />
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default DetailViewModal;
