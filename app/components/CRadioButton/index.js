/* eslint-disable no-sparse-arrays */
/* eslint-disable react-native/no-inline-styles */
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { translate } from "../../lang/Translate";
import { useFocusEffect } from "@react-navigation/native";
import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from "react-native";

const RadioButton = ({
  options,
  selectedOption1 = () => {},
  type = "",
  defaultValue,
  disable = false,
  defaultData = 0,
}) => {
  const [clickId, setClickId] = useState(defaultValue || 0);
  const [selectedOption, setSelectedOption] = useState(defaultData);

  useEffect(() => {
    setSelectedOption(defaultData);
  }, [defaultData]);

  const handleSelectOption = (option, id) => {
    if (!disable) {
      setSelectedOption(id);
      setClickId(id);
      selectedOption1(option, id);
    }
  };
  useEffect(() => {
    if (defaultValue) {
      setClickId(defaultValue);
    }
  }, [defaultValue, clickId]);
  useFocusEffect(
    useCallback(() => {
      if (defaultValue) {
        setClickId(Number(defaultValue));
      }
    }, [defaultValue, clickId])
  );

  if (type === "dot") {
    return (
      <View style={[styles.container1]}>
        {options.map((option, index) => (
          <TouchableOpacity
            activeOpacity={disable ? 1 : 0.8}
            key={index}
            style={[styles.radioButton]}
            onPress={() => handleSelectOption(option.value, index)}
            disabled={disable}
          >
            <View
              style={[
                index === selectedOption
                  ? {
                      height: 24,
                      width: 24,
                      borderWidth: 1,
                      borderRadius: 23,
                      borderColor: BaseColors.activeTab,
                      alignItems: "center",
                      justifyContent: "center",
                    }
                  : {
                      height: 24,
                      width: 24,
                      borderWidth: 1,
                      borderRadius: 23,
                      alignItems: "center",
                      justifyContent: "center",
                    },
              ]}
            >
              <View
                style={[
                  ,
                  index === selectedOption
                    ? {
                        height: 16,
                        width: 16,
                        borderRadius: 25,
                        backgroundColor: BaseColors.activeTab,
                      }
                    : {
                        height: 16,
                        width: 16,
                        borderRadius: 23,
                      },
                ]}
              />
            </View>
            <Text
              style={[
                styles.radioText,
                {
                  color:
                    index === selectedOption
                      ? BaseColors.black
                      : BaseColors.fontColor,
                  marginLeft: 13,
                  marginBottom: 3,

                  // textAlignVertical: 'center',
                },
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  } else if (type === "options") {
    return (
      <View style={{ width: "90%" }}>
        {options.map((option, index) => {
          return (
            <TouchableOpacity
              activeOpacity={disable ? 1 : 0.8}
              key={index}
              style={[
                {
                  alignItems: "center",
                  backgroundColor: "transparent",
                  textAlignVertical: "center",
                  borderRadius: 50,
                  flexDirection: "row",

                  marginVertical: 12,
                  gap: 10,
                },
              ]}
              onPress={() => handleSelectOption(option, option?.id)}
              disabled={disable}
            >
              <View
                style={[
                  option?.id === clickId || defaultValue
                    ? {
                        height: 24,
                        width: 24,
                        borderWidth: 2,
                        borderRadius: 23,
                        borderColor: BaseColors.activeTab,
                        alignItems: "center",
                        justifyContent: "center",
                      }
                    : {
                        height: 24,
                        width: 24,
                        borderWidth: 2,
                        borderRadius: 23,
                        alignItems: "center",
                        justifyContent: "center",
                      },
                ]}
              >
                <View
                  style={[
                    ,
                    option?.id === clickId
                      ? {
                          height: 16,
                          width: 16,
                          borderRadius: 25,
                          backgroundColor: BaseColors.activeTab,
                        }
                      : {
                          height: 16,
                          width: 16,
                          borderRadius: 23,
                        },
                  ]}
                />
              </View>
              <Text
                style={{
                  fontSize: 18,
                  fontFamily: FontFamily.RobotoMedium,
                  color: BaseColors.black,
                  textAlign: "center",
                  textAlignVertical: "center",
                  marginBottom: 2,
                }}
              >
                {translate(option?.options)}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  } else {
    return (
      <View style={[styles.container, { height: 45 }]}>
        {options.map((option, index) => (
          <TouchableOpacity
            activeOpacity={disable ? 1 : 0.8}
            key={index}
            style={[
              index === defaultValue
                ? styles.radioButtonActive
                : styles.radioButton,
              {
                justifyContent: "center",
                height: 45,
              },
            ]}
            onPress={() => handleSelectOption(option.value, index)}
            disabled={disable}
          >
            <Text
              style={[
                index === defaultValue
                  ? styles.radioTextActive
                  : styles.radioText,
                { marginBottom: Platform.OS === "ios" ? 0 : 6, fontSize: 18 },
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 0.5,
    flexDirection: "row",
    height: 53,
    alignItems: "center",
    borderRadius: 40,
    borderColor: BaseColors.gray,
  },
  container1: {
    flexDirection: "row",
    height: 53,
    alignItems: "center",
  },
  radioButton: {
    flex: 1,
    height: 50,
    alignItems: "center",
    // justifyContent: 'center',
    backgroundColor: "transparent",
    textAlignVertical: "center",
    borderRadius: 50,
    flexDirection: "row",
    justifyContent: "center",
  },
  radioButtonActive: {
    flex: 1,
    height: 54,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BaseColors.activeTab,

    borderRadius: 50,
  },
  radioButtonActive1: {
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BaseColors.activeTab,
    borderRadius: 50,
  },
  radioText: {
    fontSize: 20,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.black,
    // marginLeft: 23,
    // margin: 9,
    lineHeight: 26.94,
    textAlign: "center",
    textTransform: "capitalize",
    textAlignVertical: "center",
  },
  radioTextActive: {
    fontSize: 20,
    fontFamily: FontFamily.medium,
    color: BaseColors.white,
    // marginLeft: 23,
    // margin: 9,
    lineHeight: 26.94,
    textAlign: "center",
    textTransform: "capitalize",
    textAlignVertical: "center",
  },
});

export default RadioButton;
