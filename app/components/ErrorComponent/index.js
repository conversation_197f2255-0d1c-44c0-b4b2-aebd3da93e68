import React from "react";
// import Animated from "react-native-reanimated";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
// import FastImage from "react-native-fast-image";
// import { images } from "@config/images";

const { images } = require("@config/images");
const Animated = require("react-native-reanimated").default;
const FastImage = require("react-native-fast-image");

function ErrorComponent(props) {
  const { onPressClose, text } = props;
  return (
    <Animated.View style={styles.mainView}>
      <View style={styles.textView}>
        <CustomIcon name={"BsExclamationCircle"} size={20} color={"#EF4343"} />
        <Text style={styles.errorText}>{text}</Text>
      </View>
      {/* <CustomIcon
        name={"close"}
        size={16}
        color={"#EF4343"}
        onPress={onPressClose}
      /> */}
      <TouchableOpacity activeOpacity={0.8} onPress={onPressClose}>
        <FastImage
          source={images.closeImg}
          style={{
            width: 20,
            height: 20,
          }}
          tintColor={"#EF4343"}
        />
      </TouchableOpacity>
    </Animated.View>
  );
}
const styles = StyleSheet.create({
  mainView: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: "#FEF1F1",
    marginBottom: 20,
    borderRadius: 4,
  },
  textView: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  errorText: {
    fontSize: 14,
    color: BaseColors.red,
    fontFamily: "Inter-SemiBold",
    width: "86%",
  },
});
export default ErrorComponent;
