import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Text,
} from "react-native";
import Popover from "react-native-popover-view/dist/Popover";

const CSearch = ({
  placeholder = "Search",
  backBtn = false,
  options = false,
  handleOptionsBtn = () => {},
  optionData = [],
  searchBtnData = "",
  setSearchBtnData = () => {},
  searchLeft = false,
  searchDisable = false,
  handleSearchNavigation = () => {},
  handleSubmitEditing = () => {},
  inputRef = null,
}) => {
  const [showPopover, setShowPopover] = useState(false);

  const handleInputChange = async (value) => {
    setSearchBtnData(value);
  };

  return (
    <View style={styles.main}>
      {/* Back button */}
      {backBtn ? (
        <TouchableOpacity
          style={[styles.backIconView, { borderColor: BaseColors.headerIcon }]}
          activeOpacity={0.8}
        >
          <CustomIcon
            name="back-arrow"
            size={18}
            color={BaseColors.headerIcon}
          />
        </TouchableOpacity>
      ) : null}

      {/* Search Input */}
      {searchDisable ? null : (
        <View style={[styles.container]}>
          {searchLeft ? (
            <CustomIcon
              name={"Search-1"}
              size={20}
              color={BaseColors.headerIcon}
            />
          ) : null}

          <TextInput
            ref={inputRef}
            style={{
              flex: 1,
              fontSize: 16,
              color: BaseColors.black100,
              height: 50,
              paddingLeft: searchLeft ? 8 : 0,
            }}
            placeholder={placeholder}
            placeholderTextColor={"#555454"}
            value={searchBtnData}
            onChangeText={handleInputChange}
            returnKeyType="search"
            onSubmitEditing={handleSubmitEditing}
          />
          {searchLeft ? null : (
            <CustomIcon
              name={"Search-1"}
              size={20}
              color={BaseColors.headerIcon}
            />
          )}
        </View>
      )}

      {searchDisable ? (
        <TouchableOpacity
          activeOpacity={0.9}
          style={[styles.container, {}]}
          onPress={() => {
            handleSearchNavigation();
          }}
        >
          {searchLeft ? (
            <CustomIcon
              name={"Search-1"}
              size={20}
              color={BaseColors.headerIcon}
            />
          ) : null}

          <TextInput
            style={{
              fontSize: 16,
              color: BaseColors.black100,
              height: 50,
              paddingLeft: searchLeft ? 8 : 0,
            }}
            placeholder={placeholder}
            placeholderTextColor={"#555454"}
            value={searchBtnData}
            onChangeText={handleInputChange}
            editable={!searchDisable}
          />
          {searchLeft ? null : (
            <CustomIcon
              name={"Search-1"}
              size={20}
              color={BaseColors.headerIcon}
            />
          )}
        </TouchableOpacity>
      ) : null}

      {/* three Dots */}
      {options ? (
        <View>
          <Popover
            from={
              <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => setShowPopover(true)}
                style={styles.dots}
              >
                <CustomIcon
                  name={"dot"}
                  size={28}
                  color={BaseColors.headerIcon}
                />
              </TouchableOpacity>
            }
            popoverStyle={styles.popoverStyle}
            isVisible={showPopover}
            onRequestClose={() => setShowPopover(false)}
            popoverArrow={false}
            children={
              <FlatList
                data={optionData || []}
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                    style={[
                      styles.mainPopData,
                      {
                        borderBottomWidth:
                          index === optionData?.length - 1 ? 0 : 1,
                      },
                    ]}
                    activeOpacity={0.8}
                    onPress={() => {
                      if (item.key === "block") {
                        handleOptionsBtn("block");
                        setShowPopover(false);
                      } else if (item.key === "report") {
                        handleOptionsBtn("report");
                      } else if (item.key === "clearChat") {
                        handleOptionsBtn("clearChat");
                      }
                    }}
                  >
                    <CustomIcon
                      name={item.icon}
                      size={22}
                      color={
                        item.key === "clearChat"
                          ? BaseColors.activeTab
                          : BaseColors.black
                      }
                    />
                    <Text
                      style={{
                        color:
                          item.key === "clearChat"
                            ? BaseColors.activeTab
                            : BaseColors.black,
                        fontSize: 16,
                        fontFamily: FontFamily.InterMedium,
                      }}
                    >
                      {item.text}
                    </Text>
                  </TouchableOpacity>
                )}
              />
            }
          />
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 40,
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    backgroundColor: BaseColors.white,
    paddingHorizontal: 10,
    borderWidth: 0.5,
    borderColor: BaseColors.gray7,
    flex: 1,
  },
  mainPopData: {
    borderColor: BaseColors.gray,
    flexDirection: "row",
    gap: 8,
    paddingVertical: 6,
  },
  popoverStyle: {
    borderWidth: 1,
    width: 150,
    borderRadius: 8,
    marginRight: 12,
    padding: 15,
    borderColor: BaseColors.gray,
  },
  backIconView: {
    borderWidth: 1,
    padding: 6,
    borderRadius: 23,
  },
  main: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  dots: {
    width: 20,
  },
});

export default CSearch;
