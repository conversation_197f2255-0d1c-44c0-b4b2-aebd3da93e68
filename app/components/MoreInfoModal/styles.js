import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, StyleSheet } from "react-native";
import { isIPhoneX } from "react-native-status-bar-height";

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalView: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
    paddingBottom: isIPhoneX() ? 40 : 20
  },

  modalTitleText: {
    fontSize: 24,
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginBottom: 15,
    textAlignVertical: "center",
  },
  dataMainView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  storyImageMainView: {
    height: 75,
    width: 75,
    overflow: "hidden",
    borderRadius: 43,
    borderWidth: 1,
  },
  storyImageView: {
    height: 75,
    width: 75,
    resizeMode: "cover",
  },
  headingText: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  descText: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.gray3,
  },
  separatorView: {
    borderWidth: 1,
    marginTop: 10,
  },
  sharedTextView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 10,
    backgroundColor: BaseColors.white,
    padding: 20,
  },
  textView: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black100,
  },
  btnView: {
    marginBottom: 10,
    marginHorizontal: 19,
  },
  switchCardView: {
    borderRadius: 20,
    overflow: "hidden",
  },
  textContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",

    borderColor: "#8E8383",
    padding: 14,
  },
  textStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
  },
  profileDPStyle: {
    // borderWidth: 1,
    height: 50,
    width: 50,
    borderRadius: 5,
  },
  renderItemMainView: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 20,
  },
  userDataComponent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  textComponentStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.black,
  },
  btnViewStyle: {
    padding: 5,
    paddingHorizontal: 15,
    borderRadius: 5,
    backgroundColor: BaseColors.white,
    shadowColor: BaseColors.gray,
    borderWidth: 0.75,
    borderColor: BaseColors.gray,
    elevation: 21,
  },
  btnTextStyle: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
  },
});

export default styles;
