import React from "react";
import { Dimensions, View } from "react-native";
import { styles } from "./style";

import ShimmerPlaceholder from "react-native-shimmer-placeholder";
import LinearGradient from "react-native-linear-gradient";

const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;

const ShimmerPostComponent = () => {
  const WIDTH = Dimensions.get("window").width;
  const HEIGHT = Dimensions.get("window").height;

  return (
    <Animated.View
      entering={FadeIn}
      style={{
        ...styles.postMain,
        minHeight: HEIGHT / 2.3,
        // minHeight: HEIGHT / 2.3,
      }}
    >
      {/* Heder */}
      <View style={styles.headerMain}>
        <View style={styles.leftSideMain}>
          <View>
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              style={{
                width: WIDTH / 8,
                height: WIDTH / 8,
                borderRadius: 25,
              }}
            />
          </View>

          <View>
            <ShimmerPlaceholder
              style={styles.title}
              LinearGradient={LinearGradient}
            />
            <ShimmerPlaceholder
              width={120}
              style={[styles.dataAndTime, { marginTop: 10 }]}
              LinearGradient={LinearGradient}
            />
          </View>
        </View>
      </View>

      <View style={{ flex: 1 }}>
        {/* Center Image */}

        {/* Center Video */}

        <ShimmerPlaceholder
          style={{
            width: WIDTH - 40,
            height: HEIGHT / 3.5,
          }}
          LinearGradient={LinearGradient}
          height={HEIGHT / 3.5}
        />
      </View>

      {/* Footer */}
      <View style={styles.footerMain}>
        <View style={styles.likeAndComment}>
          <ShimmerPlaceholder
            height={30}
            width={30}
            style={{ borderRadius: 12, marginRight: 12 }}
          />
          <ShimmerPlaceholder
            height={30}
            width={30}
            style={{ borderRadius: 12, marginRight: 12 }}
          />
        </View>

        <View style={styles.shareAndSave}>
          <ShimmerPlaceholder
            height={30}
            width={30}
            style={{ borderRadius: 12, marginRight: 12 }}
          />

          <ShimmerPlaceholder
            height={30}
            width={30}
            style={{ borderRadius: 12, marginRight: 12 }}
          />
        </View>
      </View>
      {/* description */}
      <ShimmerPlaceholder
        height={12}
        LinearGradient={LinearGradient}
        style={{ marginHorizontal: 12, marginBottom: 12 }}
      />
    </Animated.View>
  );
};

export default ShimmerPostComponent;
