import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Dimensions,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  ActivityIndicator,
  Platform,
} from "react-native";
import { styles } from "./style";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import {
  formatNumber,
  getUserFollowingList,
  getUserFollowList,
  handleFollowToggle,
  onSharePost,
} from "@app/utils/commonFunction";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import AuthActions from "../../redux/reducers/auth/actions";
import {
  ClickedCommentLikeButton,
  addComment,
  getCommentData,
  getPostLikeList,
  hideComment,
} from "./apiCallFunction";
import Toast from "react-native-simple-toast";
import isArray from "lodash-es/isArray";
import startCase from "lodash-es/startCase";
import truncate from "lodash-es/truncate";
import isEmpty from "lodash-es/isEmpty";
import CommentList from "@components/CommentList";
import ShareListModal from "@components/ShareListModal";
import { navigationRef } from "@navigation/NavigationService";
import MoreInfoModal from "@components/MoreInfoModal";
import { postMoreInfo } from "@config/staticData";
import ReportModal from "@components/ReportModal";
import { translate } from "../../lang/Translate";
import {
  handleToGetLikeList,
  handleToReportUser,
} from "@screens/ViewReels/apiFunctions";
import { FadeInDown, FadeInUp } from "react-native-reanimated";
import ContactInfoModal from "@components/ContactModal";
import AutoHeightImage from "react-native-auto-height-image";
import PurChasePlanModal from "@components/PurchasePlanModal";
import CButton from "@components/CButton";
import { FontFamily } from "@config/typography";
import ZoomableImage from "./ZoomableImage";
import { debounce } from "lodash-es";
const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;
const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;
const FastImage = require("react-native-fast-image");
const Video = require("react-native-video").default;

const {
  setCreatedPostList,
  setCommentData,
  setUserLikeList,
  setSavedPostList,
  setModalData,
  setUserFollowingList,
  setUserFollowList,
} = AuthActions;

const PostComponent = ({
  item,
  index,
  handleLike,
  isLikeAnim,
  setIsLikeAnim,
  handleToFollow,
  handleSaveButton,
  selectedLikeIndex,
  setSelectedLikeIndex,
  isFocused,
  onReelPress,
  setFollowLodging,
  followLodging,
  navigation,
  type = "anotherProfile",
  commentModelVisible = false,
  commentPostId = "",
  handleToPressBoost = () => {},
}) => {
  const currentScreen = navigationRef.current.getCurrentRoute();
  // ref
  const textInputRef = useRef(null);
  const dispatch = useDispatch();

  // Device Height and width
  const WIDTH = Dimensions.get("window").width;
  const HEIGHT = Dimensions.get("window").height;

  const [activeIndex, setActiveIndex] = useState(0);
  const [expandedIndex, setExpandedIndex] = useState(null);
  const [commentVisible, setCommentVisible] = useState(false);
  const [commentLodging, setCommentLodging] = useState(false);
  const [commentModalLodging, setCommentModalLodging] = useState(false);
  const [shareListModal, setIsShareListModal] = useState(false);
  const [comment, setComment] = useState("");
  const [commentId, setCommentId] = useState("");
  const [selectedUname, setSelectedUname] = useState("");
  const [bottomLoader, setBottomLoader] = useState(false);
  const [orientation, setOrientation] = useState("portrait");
  const orientationMemo = useMemo(() => orientation, [orientation]);
  const [isShareListLoader, setIsShareListLoader] = useState(false);
  const [onMoreInfo, setMoreInfo] = useState(false);
  const [isReportModal, setIsReportModal] = useState(false);
  const [reportTextInput, setReportTextinput] = useState("");
  const [reportReason, setReportReason] = useState({});
  const [likeListModal, setLikeListModal] = useState(false);
  const [searchValue, setOnChangeSearchValue] = useState("");
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [shareReelId, setShareReelId] = useState("");
  const [isContactModal, setIsContactModal] = useState(false);
  const [reasonErrorMessage, setIsReasonErrMsg] = useState("");
  const [isLoader, setIsLoader] = useState(false);
  const [isActiveZoom, setIsActiveZoom] = useState(false);
  const [currentIndex, setIndex] = useState(0);

  // redux state
  const {
    createdPostList,
    commentData,
    userFollowList,
    userData,
    isCurrentPlan,
    userLikeList,
    savedPostList,
    userFollowingList,
    activePlanData,
  } = useSelector((auth) => auth.auth);

  // memos
  const shareListModalMemo = useMemo(() => shareListModal, [shareListModal]);

  // set input empty when modal open
  useEffect(() => {
    if (commentVisible) {
      setComment("");
      setCommentId("");
      setSelectedUname("");
    }
  }, [commentVisible]);

  useEffect(() => {
    if (commentModelVisible && !isEmpty(commentPostId)) {
      const timer = setTimeout(() => {
        handleComment(commentPostId, true, 1, false, "post");
      }, 100);

      return () => clearTimeout(timer);
    }
  }, []);

  // Parse the ISO date string
  const parsedDate = dayjs(item?.createdAt);

  // fetch comment List API set
  const handleComment = async (
    post_id,
    modalClose = true,
    page = 1,
    bottomLoading = false,
    postType
  ) => {
    if (modalClose) {
      setCommentVisible(!commentVisible);
      setCommentModalLodging(true);
    } else {
      if (bottomLoading) {
        setBottomLoader(true);
      } else {
        setCommentLodging(true);
      }
    }

    const resp = await getCommentData(post_id, page, postType);

    if (resp?.data?.data !== undefined && !isEmpty(resp?.data?.data)) {
      dispatch(
        setCommentData({
          page: page,
          next_enable: resp?.data?.hasNextPage,
          data:
            page > 1
              ? [...commentData?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );

      setBottomLoader(false);
      setCommentLodging(false);
      setCommentModalLodging(false);
    } else {
      setBottomLoader(false);
      dispatch(setCommentData({}));
      setCommentLodging(false);
      setCommentModalLodging(false);
    }
  };

  const handleToAction = (action, data, conversation_id) => {
    if (type === "anotherProfile" && data?.user_id !== userData?.user_id) {
      if (action === "visit_my_profile") {
        if (data?.user_id !== userData?.user_id) {
          navigation.navigate("ProfileNew", {
            data: data,
            type: "anotherProfile",
          });
        } else {
          navigation.navigate("ProfileNew", { data: data });
        }
      } else if (action === "contact_now") {
        setIsContactModal(true);
      } else if (action === "send_message") {
        navigation.navigate("MessagesInfo", {
          userInfo: {
            is_blocked: conversation_id?.is_blocked,
            conversation_id: conversation_id?.conversation_id || "",
            userData: {
              user_dp: data?.user_dp,
              user_id: data?.user_id,
              full_name: data?.full_name,
            },
          },
        });
      } else {
        Toast.show("Current User Not perform any action");
      }
    } else {
      navigation.navigate("BoostedPostInsights", { data: item });
    }
  };

  // handle Hide comment API
  const handleHideComment = async (value) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      const toggleCommentVisibility = (dataArray, commentId) => {
        // Create a shallow copy to maintain immutability
        const updatedArray = [...dataArray];

        // Find the index of the comment that needs to be updated
        const commentIndex = updatedArray.findIndex(
          (obj) => obj.comment_id === commentId
        );

        if (commentIndex !== -1) {
          // Toggle visibility directly at the found index
          updatedArray[commentIndex].is_hide = updatedArray[commentIndex]
            .is_hide
            ? 0
            : 1;

          // Recursively update replies if they exist
          if (updatedArray[commentIndex].replies?.length > 0) {
            updatedArray[commentIndex].replies = toggleCommentVisibility(
              updatedArray[commentIndex].replies,
              commentId
            );
          }
        }

        return updatedArray;
      };

      const toggleCommentVisibility1 = (dataArray, commentId) => {
        const updatedArray = [...dataArray]; // Create a shallow copy to maintain immutability

        // Find the index of the comment that needs to be updated
        const commentIndex = updatedArray.findIndex(
          (obj) => obj.comment_id === commentId
        );

        if (commentIndex !== -1) {
          const targetComment = updatedArray[commentIndex];

          // Toggle visibility (hide/unhide) and update the replies recursively if needed
          updatedArray[commentIndex] = {
            ...targetComment, // Preserve other properties
            is_hide: targetComment.is_hide ? 0 : 1,
            replies: targetComment.replies?.length
              ? toggleCommentVisibility(targetComment.replies, commentId) // Recursively toggle in replies
              : targetComment.replies,
          };
        }

        return updatedArray;
      };

      const updatedData = toggleCommentVisibility1(
        commentData?.data,
        value?.comment_id
      );

      dispatch(
        setCommentData({
          ...commentData,
          data: updatedData,
        })
      );
      const d = {
        comment_id: value?.comment_id,
        hide_id: value?.type === "post" ? value?.post_id : value?.reel_id,
        post_type: value?.type === "post" ? "post" : "reel",
      };
      const resp = await hideComment(d);
      if (resp !== undefined && resp?.data?.success) {
      } else {
        Toast.show(resp?.data?.message || "Soothing went wrong");
      }
    } else {
      if (Platform.OS === "ios") {
        setCommentVisible(!commentVisible);

        setTimeout(() => {
          setIsPaymentModal(true);
        }, 200);
      } else {
        setIsPaymentModal(true);
      }
    }
  };

  const onPressLikeCount = async (id, type, page = 1, searchingData="") => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (type === "post") {
        const resp = await getPostLikeList(id, searchingData);
        if (resp !== undefined && resp?.data?.data && resp?.data?.success) {
          dispatch(
            setUserLikeList({
              page: page,
              next_enable: resp?.data?.data?.hasNextPage,
              viewData:
                page > 1
                  ? [...commentData?.viewData, ...resp?.data?.data?.viewData]
                  : resp?.data?.data?.viewData,
              totalLike: resp?.data?.data?.totalLikes || 0,
              viewCount: item?.view_count || 0,
            })
          );
        }

        setLikeListModal(true);
      } else {
        const resp = await handleToGetLikeList(id, searchingData);

        if (resp !== undefined && resp?.data?.success) {
          dispatch(
            setUserLikeList({
              page: page,
              next_enable: resp?.data?.data?.hasNextPage,
              viewData:
                page > 1
                  ? [...commentData?.viewData, ...resp?.data?.data?.viewData]
                  : resp?.data?.data?.viewData,
              totalLike: resp?.data?.data?.totalLikes || 0,
              viewCount: item?.view_count || 0,
            })
          );

          setLikeListModal(true);
        } else {
          Toast.show(
            resp?.data?.message || "Something went wrong please try again"
          );
        }
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  const updateCommentCounts = (postList, postId) => {
    const postIndex = postList?.data?.findIndex(
      (post) => post.post_id === postId
    );

    if (postIndex !== -1) {
      const updatedPostList = [...postList.data]; // Clone the array to maintain immutability
      updatedPostList[postIndex] = {
        ...updatedPostList[postIndex],
        comment_counts: updatedPostList[postIndex].comment_counts + 1,
      };

      return updatedPostList;
    }

    return postList?.data;
  };

  // submit comment value
  const submitCommentText = async () => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (!isEmpty(comment)) {
        setCommentLodging(true);
        let updateComment = !isEmpty(selectedUname)
          ? comment?.replace(selectedUname, "")
          : comment;

        const dataToSend = {
          comment: updateComment,
        };
        item?.type === "post"
          ? (dataToSend.post_id = item?.post_id)
          : (dataToSend.reel_id = item?.reel_id);

        if (!isEmpty(commentId)) {
          dataToSend.reply_id = commentId;
        }

        const resp = await addComment(dataToSend);
        if (resp !== undefined && resp?.data?.success) {
          await handleComment(
            item?.type === "post" ? item?.post_id : item?.reel_id,
            false,
            1,
            false,
            item?.type
          );
          setComment("");
          setCommentId("");
          const updatedPosts = updateCommentCounts(
            createdPostList,
            item?.post_id || item?.reel_id
          );
          dispatch(
            setCreatedPostList({
              ...createdPostList,
              data: updatedPosts,
            })
          );
          const updatedPostsComment = updateCommentCounts(
            savedPostList,
            item?.post_id || item?.reel_id
          );
          dispatch(
            setSavedPostList({
              ...savedPostList,
              data: updatedPostsComment,
            })
          );
         
        } else {
          setCommentLodging(false);
          Toast.show(
            resp?.data?.message || "Something went wrong please try again"
          );
        }
      } else {
        Toast.show("An empty comment cannot be posted");
      }
    } else {
      if (Platform.OS === "ios") {
        setCommentVisible(!commentVisible);

        setTimeout(() => {
          setIsPaymentModal(true);
        }, 200);
      } else {
        setIsPaymentModal(true);
      }
    }
  };

  // Focus function
  const focusTextInput = () => {
    if (textInputRef.current) {
      textInputRef.current.focus();
    }
  };

  // comment Reply set in state
  const handleReply = (value) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      setComment(`@${value?.user_data[0]?.username} `);
      setSelectedUname(`@${value?.user_data[0]?.username}`);
      setCommentId(value?.comment_id);
      focusTextInput();
    } else {
      if (Platform.OS === "ios") {
        setCommentVisible(!commentVisible);

        setTimeout(() => {
          setIsPaymentModal(true);
        }, 200);
      } else {
        setIsPaymentModal(true);
      }
    }
  };

  // function when click like button using redux and API call
  const handleCommentLike = async (c_id) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      const updatedPosts = commentData?.data?.map((post) => {
        if (post.comment_id === c_id) {
          return {
            ...post,
            likes_counts: post.is_liked
              ? post.likes_counts - 1
              : post.likes_counts + 1,
            is_liked: post.is_liked ? 0 : 1,
          };
        } else {
          const updatedReplies = post?.replies?.map((reply) => {
            if (reply.comment_id === c_id) {
              return {
                ...reply,
                likes_counts: reply.is_liked
                  ? reply.likes_counts - 1
                  : reply.likes_counts + 1,
                is_liked: reply.is_liked ? 0 : 1,
              };
            } else {
              return reply;
            }
          });
          return {
            ...post,
            replies: updatedReplies,
          };
        }
      });
      dispatch(
        setCommentData({
          ...commentData,
          data: updatedPosts,
        })
      );

      const resp = await ClickedCommentLikeButton(c_id);
      if (resp !== undefined) {
        if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
          console.log("liked");
        } else {
          const updatedPosts = commentData?.data?.map((post) => {
            if (post.comment_id === c_id) {
              return {
                ...post,
                likes_counts: post.is_liked
                  ? post.likes_counts - 1
                  : post.likes_counts + 1,
                is_liked: post.is_liked ? 0 : 1,
              };
            } else {
              const updatedReplies = post?.replies?.map((reply) => {
                if (reply.comment_id === c_id) {
                  return {
                    ...reply,
                    likes_counts: reply.is_liked
                      ? reply.likes_counts - 1
                      : reply.likes_counts + 1,
                    is_liked: reply.is_liked ? 0 : 1,
                  };
                } else {
                  return reply;
                }
              });
              return {
                ...post,
                replies: updatedReplies,
              };
            }
          });

          dispatch(
            setCommentData({
              ...commentData,
              data: updatedPosts,
            })
          );
          Toast.show(resp?.data?.message || "Soothing went wrong");
        }
      }
    } else {
      if (Platform.OS === "ios") {
        setCommentVisible(!commentVisible);

        setTimeout(() => {
          setIsPaymentModal(true);
        }, 200);
      } else {
        setIsPaymentModal(true);
      }
    }
  };

  // Format the date as "DD MMM YYYY"
  const formattedDate = parsedDate?.format("DD MMM YYYY");

  const renderPaginationDots = () => {
    return (
      <View style={styles.paginationView}>
        {item?.ImageData?.map((_, dotIndex) => (
          <TouchableOpacity
            key={dotIndex}
            style={[
              styles.renderPaginationDotsView,
              {
                backgroundColor:
                  dotIndex === activeIndex
                    ? BaseColors.activeTab
                    : BaseColors.white,
                borderColor:
                  dotIndex === activeIndex
                    ? BaseColors.activeTab
                    : BaseColors.gray,
              },
            ]}
            onPress={() => setActiveIndex(dotIndex)}
          />
        ))}
      </View>
    );
  };

  const onMomentumScrollEnd = (event) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const dotIndex = Math.floor(offsetX / (WIDTH - 80));

    if (dotIndex !== activeIndex) {
      setActiveIndex(dotIndex);
    }
  };

  const renderImgItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={[
          {
            flex: 1,
            backgroundColor: "#fff",
            justifyContent: "center",
            alignItems: "center",
          },
        ]}
        onPress={() => {
          setIndex(index);
          setIsActiveZoom(true);
        }}
      >
        <AutoHeightImage
          width={WIDTH - 40}
          source={{ uri: item.fileUrl, priority: FastImage.priority.normal }}
          height={HEIGHT / 1.6}
          key={item?.post_id}
        />
      </TouchableOpacity>
    );
  };

  const toggleCollapse = (moreIndex) => {
    setExpandedIndex(expandedIndex === moreIndex ? null : moreIndex);
  };

  const selectedLike = (selected_post_Id, PostType, selected_index) => {
    handleLike(selected_post_Id, PostType);
    setSelectedLikeIndex(selected_index);
  };

  const handleToReport = useCallback(async () => {
    setIsLoader(true);
    const data = {
      user_id: item?.user_data[0]?.user_id,
      reason: translate(reportReason?.options),
      type: item?.type === "reel" ? "reel" : "post",
      reporting_id: item?.type === "reel" ? item?.reel_id : item?.post_id,
    };

    if (reportReason?.options === "somethingElseText") {
      data.reason = reportTextInput;
    }
    if (
      reportReason?.options === "somethingElseText" &&
      reportTextInput === ""
    ) {
      setIsReasonErrMsg("Please enter,\nWhy you are report this user!");
      setIsLoader(false);
    } else {
      const resp = await handleToReportUser(data);
      if (resp !== undefined && resp?.data?.success) {
        Toast.show(resp?.data?.message);
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
        setIsLoader(false);
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
        setReportReason({});
        setReportTextinput("");
        setIsReportModal(false);
        setIsLoader(false);
      }
      setIsLoader(false);
    }
  });

  const handleSelectedFollow = (selectedID, fIndex, type) => {
    setFollowLodging({
      loader: true,
      selectedIndex: fIndex,
    });
    handleToFollow(selectedID, fIndex, type);
  };

  const handleToPressUser = (data) => {
    if (data?.user_id !== userData?.user_id) {
      navigation.navigate("ProfileNew", {
        data: data,
        type: "anotherProfile",
      });
    } else {
      navigation.navigate("ProfileNew", { data: data });
    }
  };

  const getUserShareList =
    userFollowList?.data && userFollowingList?.data
      ? [...new Set([...userFollowList?.data, ...userFollowingList?.data])]
      : [];

  function removeDuplicates(arr, prop) {
    return arr.filter(
      (obj, index, self) =>
        index === self.findIndex((o) => o[prop] === obj[prop])
    );
  }
  const handleToBtnPressForFollow = async (data) => {
    const resp = handleFollowToggle(data?.id);
    if (resp !== undefined && resp?.data?.success) {
      console.log("Success", userData.followers);
    }
  };

  const getUserShareListApi = useCallback(async () => {
    setIsShareListLoader(true);
    const resp = await getUserFollowList(userData?.user_id);
    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(setUserFollowList(resp?.data));
      setIsShareListLoader(false);
    } else {
      console.log("Something went wrong please try again");
      setIsShareListLoader(false);
    }

    const resp1 = await getUserFollowingList(userData?.user_id);
    if (resp1 !== undefined && resp1?.data?.success && resp1?.data?.data) {
      dispatch(setUserFollowingList(resp1?.data));
      setIsShareListLoader(false);
    } else {
      console.log("Something went wrong please try again");
      setIsShareListLoader(false);
    }
    setIsShareListLoader(false);
  }, [userData]);

  const debouncedSearch = useMemo(
    () => debounce((item_id, type, page, text) => onPressLikeCount(item_id, type, page, text), 500),
    []
  );

  const handleChange = (item_id, type, page, text) => {
    debouncedSearch(item_id, type, page, text);
  };

  return (
    <Animated.View
      entering={FadeIn}
      style={{
        ...styles.postMain,
        minHeight: item?.type === "post" ? HEIGHT / 2.3 : HEIGHT / 1.2,
        // minHeight: HEIGHT / 2.3,
      }}
    >
      {/* Heder */}
      <View style={styles.headerMain}>
        <TouchableOpacity
          style={styles.leftSideMain}
          onPress={() => handleToPressUser(item?.user_data[0])}
          activeOpacity={0.8}
        >
          {isArray(item?.user_data) && (
            <View>
              <FastImage
                source={{
                  uri: item?.user_data[0]?.user_dp
                    ? item?.user_data[0]?.user_dp
                    : null,
                }}
                style={{
                  width: WIDTH / 8,
                  height: WIDTH / 8,
                  borderRadius: 25,
                }}
              />
            </View>
          )}
          {item?.user_data ? (
            <View>
              <Text style={styles.title}>
                {truncate(startCase(item?.user_data[0]?.username), {
                  length: 18,
                  omission: "...",
                })}
              </Text>
              <Text style={styles.dataAndTime}>{formattedDate}</Text>
            </View>
          ) : null}
        </TouchableOpacity>
        {item?.user_data ? (
          <View style={styles.rightSideMain}>
            {item?.user_data[0]?.user_id !== userData?.user_id ? (
              <TouchableOpacity
                style={{
                  borderRadius: 5,
                  borderWidth: 1,
                  paddingHorizontal: 20,
                  paddingVertical: 5,
                  maxWidth: 100,
                }}
                onPress={() =>
                  handleSelectedFollow(item?.user_data[0]?.user_id, index)
                }
                activeOpacity={0.8}
              >
                {followLodging?.loader &&
                followLodging.selectedIndex === index ? (
                  <ActivityIndicator animating color={BaseColors.activeTab} />
                ) : (
                  <Text style={styles.followText} numberOfLines={1}>
                    {item?.is_followed ? "Following" : "Follow"}
                  </Text>
                )}
              </TouchableOpacity>
            ) : null}

            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => setMoreInfo(true)}
            >
              <CustomIcon
                name={"dot"}
                size={20}
                color={BaseColors.headerIcon}
              />
            </TouchableOpacity>
          </View>
        ) : null}
      </View>
      {item?.type === "post" ? null : (
        <View
          style={{
            alignItems: "flex-end",
            paddingRight: 8,
            position: "absolute",
            right: -20,
            top: 60,
            zIndex: 999,
          }}
        >
          <LottieView
            autoSize={true}
            source={images.reelAnim}
            autoPlay={true}
            loop
            style={styles.reelAnimView}
          />
        </View>
      )}

      <View style={{ flex: 1 }}>
        {/* Center Video */}
        {item?.type === "post" ? (
          <FlatList
            data={item?.ImageData}
            renderItem={renderImgItem}
            keyExtractor={(item) => item?.post_id}
            horizontal
            pagingEnabled
            bounces={false}
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={onMomentumScrollEnd}
          />
        ) : null}
        {/* For Display Image Preview */}
        <ZoomableImage
          isActiveZoom={isActiveZoom}
          setIsActiveZoom={setIsActiveZoom}
          dataList={item?.ImageData}
          initialIndex={currentIndex}
        />
        {item?.type === "reel" ? (
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => onReelPress(item)}
          >
            <Video
              source={{
                uri: item?.ReelData?.fileUrl,
              }}
              poster={item?.ReelData?.thumbnailData?.thumbUrl}
              posterResizeMode={"contain"}
              minLoadRetryCount={6}
              resizeMode={orientationMemo === "landscape" ? "contain" : "cover"}
              paused={!isFocused || currentScreen.name !== "HomeTab"}
              onLoad={(e) => {
                setOrientation(e?.naturalSize?.orientation);
              }}
              style={styles.videoStyle}
              preload="auto" // Preload videos automatically
              repeat={true}
              onError={() => Toast.show("Failed to load video")}
            />
          </TouchableOpacity>
        ) : null}
        {Number(item?.is_boost) === 1 ? (
          <Animated.View entering={FadeInDown.delay(200)} exiting={FadeInUp}>
            <TouchableOpacity
              style={styles.boostPostView}
              activeOpacity={0.8}
              onPress={() =>
                handleToAction(
                  item?.action,
                  item?.user_data[0],
                  item?.conversation_id
                )
              }
            >
              <Text style={styles.actionTextStyle}>
                {type === "anotherProfile" &&
                item?.user_data[0]?.user_id !== userData?.user_id
                  ? item?.action === "contact_now"
                    ? "Contact Now"
                    : item?.action === "send_message"
                      ? "Send Message"
                      : item?.action === "visit_my_profile"
                        ? "Visit My Profile"
                        : null
                  : "View insights"}
              </Text>

              <CustomIcon
                name="BsChevronRight"
                size={16}
                color={BaseColors.white}
              />
            </TouchableOpacity>
          </Animated.View>
        ) : null}
      </View>
      {item?.user_data[0]?.user_id === userData?.user_id &&
      item?.whereCome === "boost" &&
      Number(item.is_boost) === 0 ? (
        <Animated.View entering={FadeInDown.delay(200)} exiting={FadeInUp}>
          <TouchableOpacity
            style={styles.boostPostView}
            activeOpacity={0.8}
            onPress={() =>
              handleToAction(
                item?.action,
                item?.user_data[0],
                item?.conversation_id
              )
            }
          >
            <Text style={styles.actionTextStyle}>View insights</Text>

            <TouchableOpacity
              activeOpacity={0.9}
              style={styles.boostBtnView}
              onPress={() => handleToPressBoost(item, navigation)}
            >
              <Text style={styles.boostBtnText}>
                {translate("boostPostText")}
              </Text>
            </TouchableOpacity>
          </TouchableOpacity>
        </Animated.View>
      ) : null}

      {item?.user_data[0]?.user_id === userData?.user_id &&
      item?.whereCome === undefined &&
      item?.is_boost === 0 ? (
        <View style={styles.boostPostView1}>
          <CButton
            containerStyle={styles.btnContainerStyle}
            txtSty={styles.btnTextContainerStyle}
            titleFontSize={15}
            titleFontFamily={FontFamily.RobotoMedium}
            onBtnClick={() => handleToPressBoost(item, navigation)}
          >
            {item?.type === "post"
              ? translate("boostPostText")
              : translate("boostReelText")}
          </CButton>
        </View>
      ) : null}

      {/* Footer */}
      <View style={styles.footerMain}>
        <View style={styles.likeAndComment}>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.likeMain}
            onPress={() =>
              selectedLike(
                item?.type === "post" ? item?.post_id : item?.reel_id,
                item?.type,
                index
              )
            }
          >
            {item?.is_liked && isLikeAnim && selectedLikeIndex === index ? (
              <LottieView
                autoSize={true}
                source={images.like}
                autoPlay={true}
                loop={false}
                style={styles.likeView}
                onAnimationFinish={() => setIsLikeAnim(false)}
              />
            ) : null}
            <CustomIcon
              name={item?.is_liked ? "BsSuitHeartFill" : "heart-outline"}
              size={24}
              color={item?.is_liked ? BaseColors.activeTab : "#71717A"}
            />
            {/* )} */}
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() =>
              onPressLikeCount(
                item?.type === "post" ? item?.post_id : item?.reel_id,
                item?.type
              )
            }
          >
            <Text style={styles.likeText}>
              {formatNumber(item?.like_counts || 0)}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.likeMain}
            onPress={() =>
              handleComment(
                item?.type === "post" ? item?.post_id : item?.reel_id,
                true,
                1,
                false,
                item?.type
              )
            }
          >
            <CustomIcon name={"chat"} size={24} color={"#536471"} />
          </TouchableOpacity>
          <Text style={styles.likeText}>
            {formatNumber(item?.comment_counts || 0)}
          </Text>
        </View>
        {item?.ImageData?.length > 1 ? (
          <View style={styles.dots}>{renderPaginationDots()}</View>
        ) : null}

        <View style={styles.shareAndSave}>
          {item?.is_shareable ? (
            <TouchableOpacity
              activeOpacity={0.8}
              style={styles.likeMain}
              onPress={() => {
                if (
                  !isEmpty(isCurrentPlan) ||
                  activePlanData?.is_prime_user ||
                  activePlanData?.is_free_user
                ) {
                  setIsShareListModal(true);
                  setShareReelId(
                    item?.type === "post" ? item?.post_id : item?.reel_id
                  );
                  getUserShareListApi();
                } else {
                  setIsPaymentModal(true);
                }
              }}
            >
              <CustomIcon name={"share"} size={24} color={"#536471"} />
            </TouchableOpacity>
          ) : null}

          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.likeMain}
            onPress={() =>
              handleSaveButton(
                item?.type === "post" ? item?.post_id : item?.reel_id,
                item?.type
              )
            }
          >
            {item?.is_saved ? (
              <CustomIcon
                name={"BsBookmarkFill"}
                size={24}
                color={BaseColors.activeTab}
              />
            ) : (
              <CustomIcon name={"save"} size={24} color={"#536471"} />
            )}
          </TouchableOpacity>
        </View>
      </View>
      {/* description */}
      <TouchableOpacity
        style={styles.descriptionMain}
        onPress={() => toggleCollapse(index)}
        activeOpacity={0.8}
      >
        <Text style={styles.description}>
          {expandedIndex === index
            ? item?.description
            : truncate(item?.description, { length: 35, omission: "..." })}
          {item?.description?.length > 35 ? (
            expandedIndex === index ? (
              <Text>
                <Text style={styles.moreDescription}> Less</Text>
              </Text>
            ) : (
              <Text style={styles.moreDescription}> More</Text>
            )
          ) : null}
        </Text>
      </TouchableOpacity>

      {commentVisible ? (
        <CommentList
          setModalVisible={setCommentVisible}
          listData={commentData?.data || []}
          commentLodging={commentLodging}
          commentModalLodging={commentModalLodging}
          handleHideComment={handleHideComment}
          submitCommentText={submitCommentText}
          handleReply={handleReply}
          handleCommentLike={handleCommentLike}
          setComment={setComment}
          comment={comment}
          textInputRef={textInputRef}
          handleBottom={() =>
            handleComment(
              item?.type === "post" ? item?.post_id : item?.reel_id,
              false,
              commentData?.page + 1,
              true,
              item?.type
            )
          }
          onPressItemClick={(e) => {
            setCommentVisible(false);
            handleToPressUser(e);
          }}
          bottomLoader={bottomLoader}
          UserId={item?.user_id}
        />
      ) : null}

      {shareListModalMemo ? (
        <ShareListModal
          visible={shareListModalMemo}
          setModalVisible={() => {
            setIsShareListModal(!shareListModalMemo);
            setShareReelId("");
          }}
          buttonText={"Share"}
          listData={removeDuplicates(getUserShareList, "username") || []}
          shareItem={shareReelId}
          loader={isShareListLoader}
          onPressItemClick={(e) => {
            setIsShareListModal(false);
            setShareReelId("");
            handleToPressUser(e);
          }}
          handleBtnPress={(s, i) => {
            onSharePost(s?.item, i);
          }}
        />
      ) : null}
      <MoreInfoModal
        visible={onMoreInfo}
        setModalVisible={() => setMoreInfo(!onMoreInfo)}
        listData={
          item?.is_followed
            ? item?.user_data[0]?.user_id === userData?.user_id
              ? postMoreInfo?.filter((item) => item?.id === 3)
              : postMoreInfo?.filter((item) => item?.id !== 3)
            : item?.user_data[0]?.user_id === userData?.user_id
              ? postMoreInfo?.filter((item) => item?.id === 3)
              : postMoreInfo?.filter((item) => item?.id === 2)
        }
        onBtnPress={async (item1) => {
          if (item1?.id === 1) {
            await handleSelectedFollow(
              item?.user_data[0]?.user_id,
              index,
              "remove"
            );
            setMoreInfo(false);
          } else if (item1?.id === 3) {
            setMoreInfo(false);
            dispatch(
              setModalData({
                type: "deletePost",
                title: "deletePostText",
                buttonTxt: "delete",
                cancelText: "No",
                icon: "Delete",
                visible: true,
                extraData:
                  item?.type === "post" ? item?.post_id : item?.reel_id,
              })
            );
          } else {
            if (
              !isEmpty(isCurrentPlan) ||
              activePlanData?.is_prime_user ||
              activePlanData?.is_free_user
            ) {
              setIsReportModal(true);
              setMoreInfo(false);
            } else {
              setIsPaymentModal(true);
              setMoreInfo(false);
            }
          }
        }}
      />
      <ReportModal
        visible={isReportModal}
        setModalVisible={() => setIsReportModal(false)}
        selectedOptionForReason={(e) => {
          setReportReason(e);
        }}
        textInputData={reportTextInput}
        setTextInputValue={(e) => {
          setReportTextinput(e);
          setIsReasonErrMsg("");
        }}
        reasonValue={reportReason}
        isErrMessage={reasonErrorMessage}
        onBtnPress={() => {
          handleToReport();
        }}
        isLoading={isLoader}
      />
      {/* For View Like List */}
      <ShareListModal
        visible={likeListModal}
        setModalVisible={() => {
          setLikeListModal(!likeListModal);
          setOnChangeSearchValue("");
        }}
        buttonText={"Share"}
        type={"post"}
        title={"Likes"}
        likeCount={userLikeList?.totalLike}
        likeViewCount={userLikeList?.viewCount}
        listData={userLikeList?.viewData || []}
        onPressItemClick={(e) => {
          setLikeListModal(!likeListModal);
          setOnChangeSearchValue("");
          handleToPressUser(e);
        }}
        onChangeSearch={(e) => {
          setOnChangeSearchValue(e);
          handleChange(
            item?.type === "post" ? item?.post_id : item?.reel_id,
            item?.type,
            1,
            e)
          
        }}
        searchValue={searchValue}
        handleBtnPress={(e) => {
          handleToBtnPressForFollow(e);
        }}
      />
      {isContactModal ? (
        <ContactInfoModal
          visible={isContactModal}
          setModalVisible={(e) => setIsContactModal(e)}
          listData={item?.user_data[0]}
        />
      ) : null}

      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
    </Animated.View>
  );
};

export default memo(PostComponent);
