import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet, Dimensions } = require("react-native");

HIGHT = Dimensions.get("window").height;

export const styles = StyleSheet.create({
  postMain: {
    marginHorizontal: 20,
    backgroundColor: "#F5F5F5",
    borderRadius: 10,
    elevation: 2,
    marginBottom: 20,
  },
  title: {
    color: BaseColors.headerIcon,
    fontFamily: FontFamily.InterBold,
    fontSize: 16,
  },
  dataAndTime: {
    color: "#9CA0AC",
    fontFamily: FontFamily.InterSemiBold,
    fontSize: 12,
  },
  headerMain: {
    flexDirection: "row",
    padding: 10,
    justifyContent: "space-between",
    width: "100%",
  },
  leftSideMain: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
    width: "50%",
  },
  rightSideMain: {
    flexDirection: "row",
    alignItems: "center",
    width: "50%",
    justifyContent: "flex-end",
  },
  followText: {
    color: BaseColors.headerIcon,
    fontFamily: FontFamily.InterSemiBold,
    fontSize: 14,
  },
  likeText: {
    color: "#536471",
    fontFamily: FontFamily.InterSemiBold,
    fontSize: 14,
    paddingRight: 12,
  },
  likeMain: {
    paddingRight: 5,
  },
  footerMain: {
    padding: 10,
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between",
  },
  likeAndComment: {
    flexDirection: "row",
    width: "35%",
    position: "relative",
  },
  dots: {
    width: "30%",
    borderColor: "red",
    justifyContent: "center",
    alignItems: "center",
    paddingRight: 20,
  },
  shareAndSave: {
    flexDirection: "row",
    width: "25%",
    justifyContent: "flex-end",
  },
  descriptionMain: {
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  description: {
    color: "#9CA0AC",
    fontFamily: FontFamily.InterMedium,
    fontSize: 13,
  },
  moreDescription: {
    color: BaseColors.activeTab,
    fontFamily: FontFamily.InterMedium,
    fontSize: 13,
  },
  paginationView: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
  },
  renderPaginationDotsView: {
    width: 8,
    height: 8,
    borderRadius: 25,
    marginHorizontal: 2,
    borderWidth: 1.5,
    borderColor: BaseColors.activeTab,
  },
  likeView: {
    height: 100,
    width: 100,
    position: "absolute",
    bottom: 0,
    left: -38,
  },
  videoStyle: {
    width: "100%",
    height: "100%",
    backgroundColor: BaseColors.black,
  },
  reelAnimView: {
    height: 80,
    width: 80,
  },
  actionTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.white,
    textAlignVertical: "center",
  },
  boostPostView: {
    backgroundColor: "rgba(214, 0, 46, 0.8)",
    height: 40,
    marginTop: -40,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 18,
  },
  boostPostView1: {
    marginTop: -40,
    marginBottom: 5,
    alignItems: "flex-end",

    marginHorizontal: 6,
  },
  boostBtnText: {
    fontSize: 14,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.activeTab,
  },
  boostBtnView: {
    borderWidth: 1,
    padding: 6,
    borderRadius: 5,
    backgroundColor: BaseColors.white,
    borderColor: BaseColors.white,
  },
  btnContainerStyle: {
    height: 30,
    paddingVertical: 0,
  },
  btnTextContainerStyle: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 15,
  },
});
