import React, { useRef, useState } from "react";
import {
  Dimensions,
  FlatList,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { ImageZoom } from "@likashefqet/react-native-image-zoom";
import { CustomIcon } from "@config/LoadIcons";

const ZoomableImage = ({
  isActiveZoom,
  setIsActiveZoom = () => {},
  dataList = [],
  initialIndex = 0,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const WIDTH = Dimensions.get("window").width;

  // Handle visible items change
  const onViewableItemsChanged = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index); // Set the current index
    }
  }).current;
  return (
    <Modal
      visible={isActiveZoom}
      onRequestClose={() => {
        setIsActiveZoom(false);
      }}
      transparent={true}
      animationType="fade"
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View
            style={[
              styles.header,
              { paddingTop: Platform.OS === "ios" ? 50 : 10 },
            ]}
          >
            <TouchableOpacity
              onPress={() => setIsActiveZoom(false)}
              activeOpacity={0.8}
            >
              <CustomIcon
                name="BsXCircle"
                size={30}
                color={"#fff"}
                onPress={() => setIsActiveZoom(false)}
              />
            </TouchableOpacity>
          </View>
          <FlatList
            data={dataList}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            bounces={false}
            initialScrollIndex={initialIndex}
            getItemLayout={(data, index) => ({
              length: WIDTH, // Height of each item (make sure this matches your item height)
              offset: WIDTH * index, // The distance from the top to the current index
              index, // The index of the current item
            })}
            renderItem={({ item, index }) => {
              return (
                <View
                  onPress={() => {
                    setIsActiveZoom(false);
                  }}
                  activeOpacity={0.8}
                  style={{
                    flex: 1,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <ImageZoom
                    uri={item?.fileUrl}
                    style={{ height: 120, width: WIDTH }}
                    isDoubleTapEnabled
                  />
                </View>
              );
            }}
            keyExtractor={(item, index) => index.toString()}
            onViewableItemsChanged={onViewableItemsChanged} // Track viewable items
          />
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              {currentIndex + 1 + "/" + dataList?.length}
            </Text>
          </View>
        </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

export default ZoomableImage;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    backgroundColor: "rgba(0,0,0,0.6)",
  },
  footer: {
    position: "absolute",
    bottom: "5%",
    alignSelf: "center",
  },
  footerText: {
    color: "#fff",
    fontSize: 15,
    fontFamily: "Inter-Medium",
  },
  header: {
    alignItems: "flex-end",
    paddingHorizontal: 12,
    paddingTop: 10,
  },
});
