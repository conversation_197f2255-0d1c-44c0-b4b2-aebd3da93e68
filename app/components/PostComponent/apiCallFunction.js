import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { isEmpty } from "lodash-es";
import Toast from "react-native-simple-toast";

// Get Comment List
export const getCommentData = async (post_id, page, postType) => {
  try {
    const resp = await getApiData(
      postType === "reel"
        ? `v1/comments/list/${post_id}?pageSize=20&page=${page}&slug=reel`
        : `v1/comments/list/${post_id}?pageSize=20&page=${page}&slug=post`,
      "GET"
    );
    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// hide comment API
export const hideComment = async (formData) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.commentsHide,
      "POST",
      formData,
      false,
      false
    );

    return resp;
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};

// add new comment
export const addComment = async (formData) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.commentsAdd,
      "POST",
      formData,
      false,
      false
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};

// comment like API
export const ClickedCommentLikeButton = async (PostId) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commentLike}${PostId}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getData ~ error:", error);
  }
};

export const getPostLikeList = async (id, searchData='') => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.postLikeList}/${id}${!isEmpty(searchData)?`?search=${searchData}`:''}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ getPostLikeList ~ error:", error);
  }
};
