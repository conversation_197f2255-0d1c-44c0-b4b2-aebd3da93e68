import React from "react";
// import Animated, { FadeInDown } from 'react-native-reanimated';
import { FlatList, Text, View } from "react-native/";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import isEmpty from "lodash-es/isEmpty";
import styles from "./styles";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

function PasswordValidation(props) {
  const { state, error } = props;
  return (
    <Animated.View entering={FadeInDown}>
      <FlatList
        data={state}
        renderItem={({ item }) => {
          return (
            <View style={styles.mainView}>
              <Text
                style={{
                  ...styles.text,
                  color:
                    !item.condition && isEmpty(error)
                      ? BaseColors.primary
                      : item.condition
                        ? BaseColors.primary
                        : "red",
                }}
              >{`\u2022 ${item.key}`}</Text>
              {item.condition && (
                <CustomIcon name={"tickGreen"} size={16} color={"#24D1BA"} />
              )}
            </View>
          );
        }}
        keyExtractor={(item, index) => index.toString()}
      />
    </Animated.View>
  );
}

export default PasswordValidation;
