import React from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
} from "react-native";
import { FontFamily } from "@config/typography";
import { BaseColors } from "@config/theme";
import LottieView from "lottie-react-native";
import { images } from "@config/images";
import styles from "./styles";
import CButton from "@components/CButton";
import { translate } from "../../lang/Translate";

const BlockWarningModal = ({
  visible,
  setModalVisible,
  text,
  navigation,
  textTitle = "",
}) => {
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };
  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View style={styles.sharedTextView}>
                <LottieView
                  autoSize={true}
                  source={images.warningLottie}
                  autoPlay={true}
                  style={{ height: 150, width: 150 }}
                />

                <Text
                  style={{
                    fontSize: 18,
                    fontFamily: FontFamily.RobotoMedium,
                    marginTop: 10,
                    color: BaseColors.fontColor,
                    textAlign: "center",
                  }}
                >
                  {translate(text)}
                </Text>
                {textTitle !== "" ? (
                  <Text
                    style={{
                      fontSize: 18,
                      fontFamily: FontFamily.RobotoMedium,
                      marginVertical: 10,
                      marginBottom: 25,
                      color: BaseColors.activeTab,
                      textAlign: "center",
                    }}
                  >
                    {textTitle}
                  </Text>
                ) : null}
              </View>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "center",
                  gap: 8,
                }}
              >
                <View style={{ flex: 1 }}>
                  <CButton
                    type="outlined"
                    onBtnClick={() => setModalVisible(false)}
                  >
                    {translate("ok")}
                  </CButton>
                </View>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default BlockWarningModal;
