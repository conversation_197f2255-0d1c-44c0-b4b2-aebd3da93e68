/* eslint-disable react-native/no-inline-styles */
// Import React and Component
import React, { useEffect, useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import styles from "./styles";
// import Animated, { FadeInDown } from "react-native-reanimated";
import DatePicker from "react-native-date-picker";
import { translate } from "../../lang/Translate";
import { useSelector } from "react-redux";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import dayjs from "dayjs";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

const DateTimePicker = ({
  isSelectedDate,
  isSelectedTime,
  type,
  title,
  placeholder,
  minimumDate,
  logo,
  value,
  onConfirm = () => {},
  isErrorMsg,
  maximumDate,
  disabled,
  isError,
}) => {
  const [isOpenDatePicker, setOpenDatePicker] = useState(false);
  const { languageData } = useSelector((s) => s.language);

  const openDatePickerModal = () => {
    setOpenDatePicker(true);
  };

  useEffect(() => {
    if (isSelectedDate) {
    }
  }, [isSelectedDate, isSelectedTime]);

  return (
    <View style={styles.deliveryMainView}>
      {type === "date" && (
        <TouchableOpacity
          activeOpacity={0.9}
          style={{
            //marginHorizontal: 20,
            backgroundColor: disabled ? BaseColors.black10 : BaseColors.white,
            borderWidth: 1,
            borderRadius: 5,
            borderColor: isError ? "#FF0000" : "#8E8383",
            height: 50,

            justifyContent: "center",
          }}
          onPress={disabled ? () => null : () => openDatePickerModal()}
        >
          <Animated.View style={styles.summaryView}>
            {value ? (
              <Text style={styles.summaryValue}>
                {dayjs(value).format("DD/MM/YYYY")}
              </Text>
            ) : (
              <Text style={styles.summaryValue1}>{placeholder}</Text>
            )}
          </Animated.View>
        </TouchableOpacity>
      )}
      {type === "time" && (
        <TouchableOpacity
          activeOpacity={0.9}
          style={{
            //marginHorizontal: 20,
            backgroundColor: disabled ? BaseColors.black10 : BaseColors.white,
            borderWidth: 1,
            borderRadius: 50,
            paddingHorizontal: 10,
            borderColor: isError ? "#FF0000" : BaseColors.DavtiveBorderColor,
            height: 50,

            justifyContent: "center",
          }}
          onPress={disabled ? () => null : () => openDatePickerModal()}
        >
          <Animated.View entering={FadeInDown} style={styles.summaryView}>
            {value ? (
              <Text style={styles.summaryValue}>
                {dayjs(value).format("HH:mm")}
              </Text>
            ) : (
              <Text style={styles.summaryValue1}>{placeholder}</Text>
            )}
            <View
              style={{
                height: 38,
                width: 38,
                alignItems: "center",

                justifyContent: "center",
                borderRadius: 23,
                backgroundColor: "rgba(185, 188, 200, 0.2)",
              }}
            >
              <CustomIcon
                name={logo}
                style={styles.logo1}
                size={20}
                color={BaseColors.gray2}
              />
            </View>
          </Animated.View>
        </TouchableOpacity>
      )}

      <DatePicker
        date={
          type === "date" && isSelectedDate
            ? new Date(`${isSelectedDate}`)
            : type === "time" && isSelectedTime
              ? new Date(isSelectedTime)
              : maximumDate
                ? new Date(maximumDate)
                : new Date()
        }
        locale={languageData}
        title={title}
        mode={type}
        cancelText={"Cancel"}
        confirmText={translate("Confirm")}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        androidVariant="nativeAndroid"
        modal
        open={isOpenDatePicker}
        onCancel={() => setOpenDatePicker(false)}
        // minuteInterval={15}
        onConfirm={(e) => {
          setOpenDatePicker(false);
          if (type === "date") {
            const formattedDate = dayjs(e).format("YYYY-MM-DD");
            onConfirm(formattedDate);
          } else {
            onConfirm(e);
          }
        }}
      />
      {isError ? (
        <View style={styles.errorMsgMainView}>
          <CustomIcon
            name={"BsExclamationCircle"}
            size={18}
            color={"#D6002E"}
          />
          <Text style={styles.errorTxt}>{isErrorMsg}</Text>
        </View>
      ) : null}
    </View>
  );
};

export default DateTimePicker;
