import { FontFamily } from "@config/typography";
import { BaseColors } from "../../config/theme";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  mainView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  title: {
    color: BaseColors.secondary,
    fontSize: 20,
    fontFamily: "Inter-SemiBold",
    textAlign: "center",
  },
  amountText: {
    paddingVertical: 24,
    paddingHorizontal: 30,
    marginBottom: 20,
    textAlign: "center",
    fontSize: 18,
    fontFamily: "Inter-SemiBold",
    color: BaseColors.white,
    backgroundColor: BaseColors.secondary,
    borderRadius: 10,
  },
  AddressTitle: {
    color: "#333",
    fontSize: 16,
    fontFamily: "Inter-SemiBold",
    textAlign: "center",
    marginTop: 20,
    marginBottom: 0,
  },
  floorInput: {
    marginVertical: 10,
  },
  savedAddressTag: {
    fontSize: 12,
    fontFamily: "Inter-SemiBold",
    color: BaseColors.white,
    backgroundColor: BaseColors.primary,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 20,
  },
  deliveryMainView: {
    flex: 1,
  },
  addressSaveBtn: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BaseColors.white,
    paddingLeft: 20,
    paddingRight: 10,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#e6effe",
    width: "47%",
  },
  savedAddressButton: {
    fontSize: 14,
    fontFamily: "Inter-Regular",
    color: BaseColors.gray10,
    marginRight: 10,
  },
  summaryView: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    paddingVertical: 14,
    // borderWidth: 1,
    // borderColor: "#8E8383",
    borderRadius: 5,
    marginBottom: 0,
  },
  summaryViewTime: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: "#8E8383",
    borderRadius: 8,
    marginBottom: 8,
  },
  errorTxt: {
    color: "#D6002E",
    textAlign: "left",
  },
  errorMsgMainView: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 8,
    gap: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: "#555454",
  },
  summaryValue: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.fontColor,
  },
  summaryValue1: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#555454",
  },
  paymentView: {
    flex: 1,
    justifyContent: "flex-end",
    paddingHorizontal: 20,
  },
  transferTitle: {
    flex: 1,
    color: BaseColors.secondary,
    fontSize: 20,
    fontFamily: "Inter-SemiBold",
    textAlign: "center",
    marginTop: 20,
    textAlignVertical: "center",
  },
  paymentBtn: {
    backgroundColor: BaseColors.secondary,
    justifyContent: "center",
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 10,
    marginTop: 50,
    marginBottom: 20,
  },
  paymentTxt: {
    fontFamily: "Inter-SemiBold",
    fontSize: 18,
    color: BaseColors.white,
    textAlign: "center",
  },
  OtherCurrencyListView: {
    flex: 1,
    flexDirection: "column",
    marginBottom: 15,
  },
  OtherCurrencyView: {
    backgroundColor: "#E6EFFE",
    paddingHorizontal: 10,
    paddingVertical: 14,
    borderRadius: 10,
    flexDirection: "row",
    alignItems: "center",
  },
  otherCurrencyTextView: {
    marginHorizontal: 10,
  },
  exchangeLogo: {
    width: 75,
    height: 75,
    borderRadius: 10,
  },
  otherCurrencyTitle: {
    fontSize: 16,
    fontFamily: "Inter-SemiBold",
    textTransform: "capitalize",
    color: BaseColors.secondary,
    marginBottom: 5,
  },
  otherCurrencyDesc: {
    color: BaseColors.gray10,
    fontFamily: "Inter-SemiBold",
    textTransform: "capitalize",
  },
  otherCurrencyExchangeRate: {
    backgroundColor: BaseColors.white,
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingVertical: 10,
    marginTop: 5,
    borderRadius: 10,
  },
  otherCurrencyExchangeRateText: {
    fontSize: 14,
    fontFamily: "Inter-SemiBold",
    textTransform: "uppercase",
    color: BaseColors.secondary,
  },
  markerImg: {
    width: 50,
    height: 50,
    borderRadius: 100,
    overflow: "hidden",
  },
  errorMsg: {
    fontSize: 14,
    fontFamily: "Inter-Regular",
    textTransform: "capitalize",
    color: "#FF0B1E",
    paddingLeft: 5,
    // paddingBottom: 5,
  },
});
