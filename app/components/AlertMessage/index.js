import React from "react";
import { Text, TouchableOpacity } from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";

const AlertMessage = ({ title1 = "", title2 = "", onBtnPress = () => {} }) => {
  return (
    <TouchableOpacity
      style={styles.trailMainView}
      activeOpacity={0.9}
      onPress={() => onBtnPress()}
    >
      <Text style={styles.trailTextStyle}>{translate(title1)}</Text>
      <Text
        style={[
          styles.trailTextStyle,
          { textDecorationLine: "underline", textTransform: "uppercase" },
        ]}
      >
        {translate(title2)}
      </Text>
    </TouchableOpacity>
  );
};

export default AlertMessage;
