import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import React, { useEffect } from "react";
import { View, Text } from "react-native";

const Timer = ({ timeLeft, setTimeLeft, onStopRecord, isAudioDuration }) => {
  useEffect(() => {
    let timer = setInterval(() => {
      setTimeLeft((prevTimeLeft) => {
        if (prevTimeLeft === 1) {
          onStopRecord();
          clearInterval(timer);
          return 0;
        } else {
          return prevTimeLeft + 1;
        }
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isAudioDuration]);

  const seconds = timeLeft % 60;
  const formattedSeconds = seconds.toString().padStart(2, "0");

  return (
    <View>
      <Text
        style={{
          fontFamily: FontFamily.InterMedium,
          color: BaseColors.black,
          fontSize: 15,
          paddingLeft: 10,
        }}
      >
        {/* {`00:${formattedSeconds === "00" ? "00" : formattedSeconds}`} */}
        {isAudioDuration}
      </Text>
    </View>
  );
};

export default Timer;
