import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";

export const getStoryData = async (page) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.viewUserStory}?pageSize=10&page=${page}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.log("Your STORY LIST ERROR IS : =========>", error);
  }
};

export const likeDislikeToggle = async (story_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.userStoryLike}/${story_id}`,
      "POST"
    );

    return resp;
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

export const onReelsPressFunc = (
  navigation,
  item,
  dispatch,
  setReelsList,
  reelsList
) => {
  dispatch(setReelsList({ ...reelsList, data: [item] }));
  navigation.navigate("Reel", {
    screen: "reels",
    params: {
      type: "selectedReel",
    },
  });
};

export const onReplyToMessage = async (data) => {
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.storyReply,
      "POST",
      data
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ onReplyToMessage ~ error:", error);
  }
};
