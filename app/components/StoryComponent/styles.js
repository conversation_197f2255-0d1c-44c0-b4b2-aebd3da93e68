import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { Dimensions, Platform, StyleSheet } from "react-native";

const { height, width } = Dimensions.get("window");
const styles = StyleSheet.create({
  mainView: {
    flexDirection: "row",
    margin: 12,
  },
  containerView: {
    borderWidth: 1,
    height: 75,
    width: 75,
    margin: 4,
    borderRadius: 45,
    overflow: "hidden",
    alignItems: "center",
    justifyContent: "center",
  },

  storyProfileImgStyle: {
    height: 75,
    width: 75,
  },
  contentMainView: {
    flex: 1,
    // paddingHorizontal: 20,
  },
  btnMainView: {
    height: height,
    width: width,
    position: "absolute",
    top: 0,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  indiCatorMainView: {
    width: width,
    marginTop: 20,
    marginHorizontal: 20,
    justifyContent: "space-evenly",
    alignItems: "center",
    flexDirection: "row",
  },
  indiCatorViewStyles: {
    flex: 1,
    height: 3,
    backgroundColor: "rgba(248,247,247,0.5)",
    marginLeft: 10,
  },
  profileImageView: {
    height: 55,
    width: 55,
    borderRadius: 40,
    resizeMode: "contain",
  },
  headerView: {
    marginVertical: 6,
    paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  userNameText: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.fontColor,
  },
  bottomView: {
    position: "absolute",
    marginVertical: Platform.OS === "ios" ? 0 : 30,
    flexDirection: "row",
    alignSelf: "center",
    bottom: 0,
    gap: 10,
    alignItems: "center",
    zIndex: 111111111111,
  },
  cancelTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#484848",
    textTransform: "capitalize",
  },
  userStoryImageStyle: {
    borderRadius: 5,
    padding: 6,
  },
  renderItemMainView: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  userNameStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotSemiBold,
    color: BaseColors.fontColor,
    textTransform: "capitalize",
  },
  hoursTimeText: {
    fontSize: 14,
    color: BaseColors.gray2,
    fontFamily: FontFamily.RobotoMedium,
  },
  countView: {
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#DCDFE3",
    width: "100%",
  },
  countTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    color: "#484848",
  },
  likeView: {
    height: 100,
    width: 100,
    position: "absolute",
    bottom: 0,
    left: -35,
  },
  highLightText: {
    fontFamily: FontFamily.RobotoMedium,
    fontSize: 14,
    color: BaseColors.black,
    textAlign: "center",
  },
  watchFullReelStyle: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 12,
    color: BaseColors.gray3,
  },
  watchFullReelView: {
    flexDirection: "row",
    alignItems: "center",
  },
});

export default styles;
