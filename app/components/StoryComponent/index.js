import { View, TouchableOpacity, ScrollView } from "react-native";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import styles from "./styles";
// import FastImage from "react-native-fast-image";
import { BaseColors } from "@config/theme";
import { navigationRef } from "@navigation/NavigationService";
import { CustomIcon } from "@config/LoadIcons";
import { getStoryData } from "./apiCallFunction";
import { useFocusEffect } from "@react-navigation/native";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import AuthActions from "@redux/reducers/auth/actions";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";
import isUndefined from "lodash-es/isUndefined";
import ShimmerPlaceholder from "react-native-shimmer-placeholder";

import MiniLoader from "@components/MiniLoader";
import Loader from "@components/Loader";
import LinearGradient from "react-native-linear-gradient";
import { isEmpty } from "lodash-es";
import PurChasePlanModal from "@components/PurchasePlanModal";

const FastImage = require("react-native-fast-image");
const { setUserStoryList } = AuthActions;

const StoryComponent = ({ isEndReached }) => {
  // Redux Variable
  const dispatch = useDispatch();
  const { userStoryList, isCurrentPlan, activePlanData } = useSelector((state) => state.auth);

  // State's Variable
  const [isVisible, setIsVisible] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [contentWidth, setContentWidth] = useState(0);
  const [layoutWidth, setLayoutWidth] = useState(0);
  const [isLoader, setIsLoader] = useState(false);
  // Memo's Variable
  const isVisibleMemo = useMemo(() => isVisible, [isVisible]);

  // Storying data Functions
  const shortingData = (array, key) => {
    let selfIndex;
    array.forEach((item, index) => {
      if (item.hasOwnProperty(key) && item[key] === true) {
        selfIndex = index;
        return;
      }
    });

    // Move the object with the "self" key to the 0 index
    if (selfIndex !== undefined) {
      const selfObject = array.splice(selfIndex, 1)[0];
      array.unshift(selfObject);
    }

    return array;
  };
  const handleToGetData = async (page = 1) => {
    setIsLoader(false);
    const respData = await getStoryData(page);

    if (respData?.data?.success) {
      const data = await shortingData(respData?.data?.data, "self");

      dispatch(
        setUserStoryList({
          page: 1,
          data: page > 1 ? [...userStoryList?.data, ...data] : data,
          hasNextPage: respData?.data?.hasNextPage,
        })
      );
      setIsLoader(false);
    } else {
      Toast.show(
        respData?.data?.message || "Something went wrong please try again!"
      );
      setIsLoader(false);
    }
    setIsLoader(false);
  };
  useEffect(() => {
    if (isEndReached && userStoryList?.hasNextPage) {
      handleToGetData(Number(userStoryList?.page) + 1);
    }
  }, [isEndReached]);
  const handleToNavigateSelfUser = useCallback(
    (item) => {
      if (item?.self === true && !isEmpty(item?.story_data)) {
        setIsVisible(true);
      } else {
        if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
          navigationRef.current.navigate("AddStoryScreen");
        } else {
          setIsPaymentModal(true);
        }
      }
    },
    [isCurrentPlan]
  );

  const onAddStory = useCallback(() => {
    setIsVisible(false);
    navigationRef.current.navigate("AddStoryScreen");
  }, []);

  const onViewStory = useCallback(() => {
    setIsVisible(false);
    navigationRef.current.navigate("StoryViewComponent", {
      storyData: userStoryList?.data,
      index: 0,
    });
  }, []);

  const handleScroll = (event) => {
    setScrollPosition(event.nativeEvent.contentOffset.x);
  };

  const handleContentSizeChange = (contentWidth, contentHeight) => {
    setContentWidth(contentWidth);
  };

  const handleLayout = (event) => {
    setLayoutWidth(event.nativeEvent.layout.width);
  };

  isEndReached = useMemo(() => {
    return layoutWidth + scrollPosition >= contentWidth - 20; // Adding a small buffer
  }, [layoutWidth, scrollPosition, contentWidth]);

  return (
    <ScrollView
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      onScroll={handleScroll}
      onContentSizeChange={handleContentSizeChange}
      onLayout={handleLayout}
      scrollEventThrottle={16}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      <View style={styles.mainView}>
        {isEmpty(userStoryList?.data) ? (
          <ShimmerPlaceholder
            height={75}
            width={75}
            style={{ height: 75, width: 75, borderRadius: 40 }}
            LinearGradient={LinearGradient}
          />
        ) : (
          userStoryList?.data?.map((item, index) => {
            return (
              <TouchableOpacity
                key={index.toString()}
                onPress={() => {
                  item?.self === true
                    ? handleToNavigateSelfUser(item)
                    : navigationRef.current.navigate("StoryViewComponent", {
                        userId: item?.user_id,
                        storyData: userStoryList,
                        index: index,
                      });
                }}
              >
                <View
                  style={[
                    styles.containerView,
                    {
                      borderWidth: 2,
                      borderColor:
                        !item?.is_all_seen && !isUndefined(item?.is_all_seen)
                          ? BaseColors.red
                          : BaseColors.gray,
                    },
                  ]}
                >
                  <FastImage
                    source={{ uri: item?.user_dp }}
                    resizeMode="contain"
                    style={styles.storyProfileImgStyle}
                  />
                </View>
                {item?.self === true ? (
                  <View
                    style={{
                      position: "absolute",
                      right: 5,
                      bottom: 4,
                      borderWidth: 2,
                      borderColor: BaseColors.activeTab,
                      padding: 4,
                      borderRadius: 23,
                      backgroundColor: BaseColors.white,
                    }}
                  >
                    <CustomIcon
                      name="plus"
                      size={12}
                      color={BaseColors.activeTab}
                    />
                  </View>
                ) : null}
              </TouchableOpacity>
            );
          })
        )}
        <AlreadyHaveStoryModal
          visible={isVisibleMemo}
          setModalVisible={() => setIsVisible(false)}
          onPressTitle1={() => {
            if (!isEmpty(isCurrentPlan) || activePlanData?.is_prime_user || activePlanData?.is_free_user) {
              onAddStory();
            } else {
              setIsPaymentModal(true);
            }
          }}
          onPressTitle2={() => {
            onViewStory();
          }}
          title1="addNewStory"
          title2="viewStory"
          headingText="storyText"
        />
        <PurChasePlanModal
          visible={isPaymentModal}
          setModalVisible={(e) => setIsPaymentModal(e)}
          text={"currentlyPlanText"}
          navigation={navigationRef.current}
        />
      </View>
      {isLoader ? <MiniLoader size="medium" /> : null}
    </ScrollView>
  );
};

export default memo(StoryComponent);
