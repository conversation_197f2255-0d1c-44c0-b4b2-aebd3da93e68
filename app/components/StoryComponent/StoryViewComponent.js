import {
  View,
  Text,
  Image,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
  Platform,
  PanResponder,
  FlatList,
  KeyboardAvoidingView,
  Keyboard,
} from "react-native";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import styles from "./styles";
import { useFocusEffect } from "@react-navigation/native";
import CInput from "@components/TextInput";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import { translate } from "../../lang/Translate";
import AuthActions from "@redux/reducers/auth/actions";
import { useSelector, useDispatch } from "react-redux";
import { UserStoryList1 } from "./component";
import {
  likeDislikeToggle,
  onReelsPressFunc,
  onReplyToMessage,
} from "./apiCallFunction";
import StoryProgressBar from "./StoryProgressBar";
import { addHighlight, removeHighlight } from "@screens/Profile/apiFunction";
import ShareListModal from "@components/ShareListModal";
import { onSharePost } from "@app/utils/commonFunction";
import { isEmpty } from "lodash-es";
import Toast from "react-native-simple-toast";
import MoreInfoModal from "@components/MoreInfoModal";

const useSharedValue = require("react-native-reanimated").useSharedValue;
const withSpring = require("react-native-reanimated").withSpring;
const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;
const Video = require("react-native-video").default;
const FastImage = require("react-native-fast-image");

// Dimension height
const { height, width } = Dimensions.get("window");

const { setStoryCount, setUserStoryList, setReelsList, setModalData } =
  AuthActions;

const VideoPlaying = (props) => {
  const {
    isLoading,
    setIsLoading,
    thumbnailImage,
    url,
    imageHeight,
    imageWidth,
    onStart,
    setIsOnLoad,
    isPlay,
  } = props;

  return (
    <Video
      source={{
        uri: url,
      }}
      poster={thumbnailImage}
      posterResizeMode="contain"
      minLoadRetryCount={6}
      resizeMode="contain"
      style={{
        height: imageHeight,
        width: imageWidth,
        borderRadius: 12,
      }}
      paused={isPlay}
      preload="auto"
      onBuffer={(w) => {
        setIsLoading(true);
        Platform.OS !== "ios" ? setIsOnLoad(false) : null;
        onStart("finish");
      }}
      onPlaybackResume={() => {
        setIsOnLoad(true);
      }}
      onLoadStart={(e) => {
        setIsOnLoad(false);
      }}
      onLoad={(q) => {
        setIsOnLoad(true);
        setIsLoading(false);

        onStart("finish");
      }}
      onLoadEnd={(r) => {
        setIsOnLoad(true);
        setIsLoading(false);
        onStart("finish");
      }}
    />
  );
};

const StoryViewComponent = ({ navigation, route }) => {
  function removeDuplicates(arr, prop) {
    return arr.filter(
      (obj, index, self) =>
        index === self.findIndex((o) => o[prop] === obj[prop])
    );
  }
  const dispatch = useDispatch();
  const {
    userStoryList,
    storyCount,
    reelsList,
    userFollowList,
    userFollowingList,
  } = useSelector((auth) => auth.auth);
  const userId = route?.params?.userId;
  const storyData = userStoryList?.data || route?.params?.storyData;

  const index = route?.params?.index;

  //   State's
  const [current, setCurrent] = useState(0);
  const [imageHight, setImageHight] = useState(height / 1.5);
  const [imageWidth, setImageWidth] = useState(width - 40);
  const [userIndex, setUserIndex] = useState(index);
  const [isLoading, setIsLoading] = useState(false);
  const [isViewUserList, setIsViewUserList] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [isReset, setIsRest] = useState(false);
  const [textInputData, setTextInputValue] = useState(false);
  const [isLikeAnim, setIsLikeAnim] = useState(false);
  const [shareListModal, setIsShareListModal] = useState(false);
  const [onMoreInfo, setMoreInfo] = useState(false);
  const getUserShareList =
    userFollowList?.data && userFollowingList?.data
      ? [...new Set([...userFollowList?.data, ...userFollowingList?.data])]
      : [];
  const progress = useSharedValue(0);
  const timeoutRef = useRef(null);
  const swipeableRef = useRef(null);

  const start = () => {
    progress.value = withSpring(1, { damping: 20 });

    timeoutRef.current = setTimeout(() => {
      next();
    }, 5000);
  };

  useFocusEffect(
    useCallback(() => {
      clearTimeout(timeoutRef.current);
    }, [])
  );
  useEffect(() => {
    const storyId =
      route?.params?.storyData?.[userIndex]?.story_data?.[current]?.story_id ||
      storyData[userIndex]?.story_data[current]?.story_id;

    if (storyId && storyId !== undefined && !storyCount.includes(storyId)) {
      dispatch(setStoryCount([...storyCount, storyId]));
    }
  }, [route, userIndex, current, storyCount]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setIsActive(false); // Set isActive to false when keyboard is shown
      }
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  // Event listener for keyboard hide event
  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setIsActive(true); // Set isActive back to true when keyboard is hidden
      }
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);

  const next = () => {
    // let updatedStoryData = [...userStoryList];

    if (current !== storyData[userIndex]?.story_data?.length - 1) {
      setIsRest(false);

      if (
        userStoryList.data[userIndex] &&
        userStoryList.data[userIndex].story_data[current]
      ) {
        userStoryList.data[userIndex].story_data[current].is_seen = true;
        dispatch(setUserStoryList(userStoryList));
      }

      clearTimeout(timeoutRef.current);
      setImageHight(height / 1.5);

      progress.value = 0;
      setImageWidth(width - 40);
      setCurrent(Number(current) + 1);
      setIsViewUserList(false);
    } else {
      if (storyData[userIndex]?.self === true) {
        if (
          userStoryList.data[userIndex] &&
          userStoryList.data[userIndex].story_data[current]
        ) {
          userStoryList.data[userIndex].story_data[current].is_seen = true;
          dispatch(setUserStoryList(userStoryList));
        }
        navigation.goBack();
      } else {
        if (userIndex !== storyData?.length - 1) {
          const indexOfUnseen = storyData[userIndex]?.story_data.findIndex(
            (story) => story.is_seen !== undefined
          );
          if (indexOfUnseen !== -1) {
            setCurrent(indexOfUnseen + 1);
            setIsViewUserList(false);
          }
          if (
            userStoryList.data[userIndex] &&
            userStoryList.data[userIndex].story_data[current]
          ) {
            userStoryList.data[userIndex].story_data[current].is_seen = true;
            dispatch(setUserStoryList(userStoryList));
          }
          setIsRest(true);
          setIsActive(false);
          setIsViewUserList(false);
          setImageHight(height / 1.5);
          setImageWidth(width - 40);
          progress.value = 0;
          setCurrent(0);
          setUserIndex(userIndex + 1);
        } else {
          setIsViewUserList(false);
          setImageHight(height / 1.5);
          setImageWidth(width - 40);
          navigation.goBack();
        }
      }

      close();
    }
  };

  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        const { dy, moveY } = gestureState;
        const screenHeight = Dimensions.get("window").height;
        // Check if the gesture moves downwards and if it starts from the top of the screen
        return dy > 5 && moveY < screenHeight / 2;
      },
      onPanResponderMove: (evt, gestureState) => {
        const { dy } = gestureState;
        if (dy > 0) {
          navigation.goBack();
        }
      },
      onPanResponderRelease: () => {},
    })
  ).current;

  const close = () => {
    progress.value = 0;
  };

  const previous = async () => {
    if (current > 0) {
      setIsViewUserList(false);
      await clearTimeout(timeoutRef.current);
      progress.value = 0;
      setIsRest(false);
      setImageHight(height / 1.5);
      setImageWidth(width - 40);
      setCurrent(current - 1);
      // timeoutRef.current = await setTimeout(start, 5000);
    } else {
      setIsViewUserList(false);
      if (userIndex > 0) {
        setIsViewUserList(false);
        progress.value = 0;
        setImageHight(height / 1.5);
        setImageWidth(width - 40);
        setCurrent(storyData[userIndex - 1].story_data.length - 1);
        setUserIndex(userIndex - 1);
        setIsRest(true);
        setIsActive(false);
      }
      setIsViewUserList(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      clearTimeout(timeoutRef.current);
      setImageHight(height / 1.5);

      setImageWidth(width - 40);
      setIsViewUserList(false);
      getUserIndex();
    }, [userId, index])
  );

  // Get User Index Function
  const getUserIndex = async () => {
    await setUserIndex(index);
  };
  const handleToPressProfile = (e) => {
    navigation.navigate("ProfileNew", { data: e, type: "anotherProfile" });
  };
  // List view
  const handleToViewList = () => {
    clearTimeout(timeoutRef.current);
    setImageHight(height / 4);
    setImageWidth(width / 3);
    setIsViewUserList(true);
    setIsActive(!isActive);
  };

  // Render UserStory data`
  const renderData = useCallback((item, currentIndex) => {
    return (
      <UserStoryList1
        data={item}
        navigation={navigation}
        handleToPressProfile={(e) => handleToPressProfile(e)}
        currentIndex={currentIndex}
      />
    );
  }, []);

  const handleToResetImage = async () => {
    setImageHight(height / 1.5);
    setImageWidth(width - 40);
    setIsViewUserList(false);
    if (isViewUserList) {
      setIsActive(!isActive);
    }
  };
  const handleToLikeAndDisLike = async (story_id) => {
    const resp = await likeDislikeToggle(story_id);
    if (resp?.data?.success) {
      const storyIndex = userStoryList?.data[userIndex]?.story_data.findIndex(
        (userStory) => userStory.story_id === story_id
      );
      if (storyIndex !== -1) {
        if (
          userStoryList.data[userIndex] &&
          userStoryList.data[userIndex].story_data[storyIndex]
        ) {
          userStoryList.data[userIndex].story_data[storyIndex].is_liked =
            !userStoryList.data[userIndex].story_data[storyIndex].is_liked;
        }
      }
      dispatch(setUserStoryList(userStoryList));
      setIsLikeAnim(true);
    } else {
      console.error("Something went wrong please try again");
    }
  };

  const handleToHighlighted = () => {
    const storyIndex = userStoryList.data[userIndex]?.story_data.findIndex(
      (userStory) =>
        userStory.story_id ===
        storyData[userIndex]?.story_data?.[current]?.story_id
    );

    if (storyIndex !== -1) {
      if (
        userStoryList.data[userIndex]?.story_data[storyIndex]?.is_highlighted ==
        0
      ) {
        userStoryList.data[userIndex].story_data[storyIndex].is_highlighted = 1;
      } else {
        userStoryList.data[userIndex].story_data[storyIndex].is_highlighted = 0;
      }
    }
    dispatch(setUserStoryList(userStoryList));
  };

  const handleToHighlight = async () => {
    handleToHighlighted();
    const storyIndex = storyData[userIndex]?.story_data.findIndex(
      (userStory) =>
        userStory.story_id ===
        storyData[userIndex]?.story_data?.[current]?.story_id
    );
    const data = await {
      story_id: storyData[userIndex]?.story_data?.[current]?.story_id,
      is_highlighted: true,
      description: "Highlight",
    };
    if (storyData[userIndex]?.story_data[storyIndex]?.is_highlighted == 1) {
      const resp = await addHighlight(data);

      if (resp?.data?.success) {
      } else {
        handleToHighlighted();
      }
    } else {
      const resp = await removeHighlight(
        storyData[userIndex]?.story_data?.[current]?.story_id
      );

      if (resp?.data?.success) {
      } else {
        handleToHighlighted();
      }
    }
  };
  const handleToReply = async (message) => {
    if (!isEmpty(message)) {
      const resp = await onReplyToMessage({
        story_id: storyData[userIndex]?.story_data[current]?.story_id,
        message: message,
      });

      setTextInputValue(null);
      if (resp?.data?.success) {
      } else {
        Toast.show(resp?.data?.message || "Something went wrong please try again");
      }
    } else {
      Keyboard.dismiss();
      Toast.show("Please enter message");
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: BaseColors.white }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <View style={styles.contentMainView} {...panResponder.panHandlers}>
          <View style={styles.indiCatorMainView}>
            {storyData[userIndex]?.story_data?.map((item, index) => {
              const percentage = 100 / storyData[userIndex]?.story_data.length;

              return (
                <>
                  {current === index ? (
                    <StoryProgressBar
                      duration={
                        item?.type === "image"
                          ? 5000
                          : storyData[userIndex]?.story_data[current]
                              ?.story_seconds || 15000
                      } // Example duration (5 seconds)
                      isActive={isActive} // Pass isActive state
                      onComplete={() => next()} // Callback when progress completes
                      progressWidth={`${percentage}%`} // Optional prop to set progress bar width
                      completedColor="red" // Optional prop to set completed color
                      reset={isReset}
                    />
                  ) : (
                    <View
                      style={{
                        height: 5,
                        width: `${percentage}%`,
                        backgroundColor:
                          item?.is_seen && current > index
                            ? BaseColors.activeTab
                            : "#ddd",
                        borderRadius: 2,
                        overflow: "hidden",
                        marginHorizontal: 2,
                      }}
                    />
                  )}
                </>
              );
            })}
          </View>
          <View
            style={[
              styles.headerView,
              { justifyContent: "space-between", marginHorizontal: 20 },
            ]}
          >
            <TouchableOpacity
              style={[styles.headerView]}
              onPress={() =>
                navigation.navigate("ProfileNew", {
                  data: {
                    user_id: storyData[userIndex]?.user_id,
                    user_dp: storyData[userIndex]?.user_dp,
                  },
                  type: "anotherProfile",
                })
              }
              activeOpacity={0.8}
            >
              <Image
                source={{ uri: storyData[userIndex]?.user_dp }}
                style={styles.profileImageView}
              />
              <View>
                <Text style={styles.userNameText}>
                  {storyData[userIndex]?.username}
                </Text>
                <Text style={styles.hoursTimeText}>
                  {storyData[userIndex]?.story_data[current]?.timestamp}
                </Text>
                {storyData[userIndex]?.story_data[current]?.reel_id ? (
                  <TouchableOpacity
                    style={styles.watchFullReelView}
                    activeOpacity={0.8}
                    onPress={() =>
                      onReelsPressFunc(
                        navigation,
                        storyData[userIndex]?.story_data[current]?.reeldata,
                        dispatch,
                        setReelsList,
                        reelsList
                      )
                    }
                  >
                    <Text style={styles.watchFullReelStyle}>
                      Watch Full Reel
                    </Text>
                    <CustomIcon
                      name="BsChevronRight"
                      size={14}
                      color={BaseColors.gray3}
                    />
                  </TouchableOpacity>
                ) : null}
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                navigation.goBack();
              }}
              style={{ zIndex: 1111111 }}
            >
              <Text style={styles.cancelTextStyle}>
                {translate("cancelText")}
              </Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={[
              {
                alignItems: "center",
                zIndex: 11111,
                height: height / 1.4,
              },
            ]}
            onPress={() => handleToResetImage()}
            activeOpacity={1}
            onPressIn={() => {
              // Hold in
              console.log("Press in");
              if (!isViewUserList) {
                setIsActive(false);
              }
            }}
            onPressOut={() => {
              // Hold Out
              if (!isViewUserList) {
                setIsActive(true);
              }
            }}
          >
            {storyData[userIndex]?.story_data[current]?.type === "video" ? (
              <VideoPlaying
                url={storyData[userIndex]?.story_data[current]?.content_url}
                thumbnailImage={
                  storyData[userIndex]?.story_data[current]?.thubnails
                }
                userIndex={userIndex}
                isPlay={!isActive}
                isLoading={isLoading}
                storyData={storyData}
                current={current}
                setIsLoading={setIsLoading}
                setIsOnLoad={(e) => {
                  setIsActive(e);
                }}
                imageHeight={imageHight}
                imageWidth={imageWidth}
                onStart={(e) => {
                  if (e === "finish") {
                    progress.value = 0;
                    // start();
                  }
                }}
              />
            ) : (
              <Image
                source={{
                  uri: storyData[userIndex]?.story_data[current]?.content_url,
                }}
                style={[
                  {
                    height: "100%",
                    width: "100%",
                    // overflow: "hidden",

                    backgroundColor: "#F5F5F5",
                  },
                ]}
                onLoadEnd={(e) => {
                  progress.value = 0;
                  // start();
                  setIsActive(true);
                }}
                resizeMode="contain"
              />
            )}
            <View style={styles.btnMainView}>
              <TouchableOpacity
                style={{ width: "20%", height: "100%" }}
                onPress={() => {
                  previous();
                }}
                onLongPress={() => {
                  if (!isViewUserList) {
                    setIsActive(false);
                  }
                }}
                onPressIn={() => {
                  // Hold in
                  console.log("Press in");
                  if (!isViewUserList) {
                    setIsActive(false);
                  }
                }}
                onPressOut={() => {
                  // Hold Out
                  if (!isViewUserList) {
                    setIsActive(true);
                  }
                }}
              >
                <View />
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  width: "20%",
                  height: "100%",
                }}
                onPress={() => {
                  next();
                }}
                onLongPress={() => {
                  if (!isViewUserList) {
                    setIsActive(false);
                  }
                }}
                onPressIn={() => {
                  // Hold in
                  console.log("Press in");
                  if (!isViewUserList) {
                    setIsActive(false);
                  }
                }}
                onPressOut={() => {
                  // Hold Out
                  if (!isViewUserList) {
                    setIsActive(true);
                  }
                }}
              >
                <View />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
          {storyData[userIndex || 0]?.self !== true ? (
            <View style={styles.bottomView}>
              <View
                style={{
                  width:
                    Platform.OS === "android"
                      ? storyData[userIndex]?.story_data[current]
                          ?.is_shareable === 1
                        ? "75%"
                        : "89%"
                      : storyData[userIndex]?.story_data[current]
                            ?.is_shareable === 1
                        ? "76%"
                        : "79%",
                  marginBottom: 10,
                }}
              >
                <CInput
                  placeholderText={"Send a message..."}
                  textInputStyle={{ maxHeight: 35 }}
                  onChange={setTextInputValue}
                  value={textInputData}
                  onSubmit={() => {
                    handleToReply(textInputData);
                  }}
                  isButton={true}
                  isBtnText={"Send"}
                  onPressBtn={() => handleToReply(textInputData)}
                />
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() =>
                  handleToLikeAndDisLike(
                    storyData[userIndex]?.story_data?.[current]?.story_id
                  )
                }
              >
                {storyData[userIndex]?.story_data[current]?.is_liked ===
                false ? (
                  <CustomIcon
                    name="BsSuitHeart"
                    size={30}
                    color={BaseColors.activeTab}
                  />
                ) : (
                  <>
                    {isLikeAnim ? (
                      <LottieView
                        autoSize={true}
                        source={images.like}
                        autoPlay={true}
                        loop={false}
                        style={styles.likeView}
                        onAnimationFinish={() => setIsLikeAnim(false)}
                      />
                    ) : null}
                    <CustomIcon
                      name="BsSuitHeartFill"
                      size={30}
                      color={BaseColors.activeTab}
                    />
                  </>
                )}
              </TouchableOpacity>
              {storyData[userIndex]?.story_data[current]?.is_shareable === 1 ? (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => setIsShareListModal(true)}
                >
                  <CustomIcon
                    name="BsShareFill"
                    size={30}
                    color={BaseColors.gray2}
                  />
                </TouchableOpacity>
              ) : null}
            </View>
          ) : (
            <View
              style={[
                styles.bottomView,
                {
                  marginHorizontal: 20,

                  width: "100%",
                },
              ]}
            >
              <View
                style={{
                  width: "50%",
                  alignItems: "flex-start",
                  alignSelf: "center",
                }}
              >
                <View style={{ alignItems: "center" }}>
                  <TouchableOpacity
                    style={{
                      flexDirection: "row",
                      alignItems: "flex-start",
                      alignSelf: "flex-start",
                      justifyContent: "flex-start",
                    }}
                    onPress={() => handleToViewList()}
                    activeOpacity={0.8}
                  >
                    {!isEmpty(storyData[0]?.story_data[current]?.views) &&
                    !isViewUserList
                      ? storyData[0]?.story_data[current]?.views
                          .slice(0, 3)
                          .map((item, index) => {
                            return (
                              <FastImage
                                source={{
                                  uri: item?.user_dp,
                                }}
                                style={{
                                  height: 50,
                                  width: 50,
                                  borderRadius: 29,
                                  marginRight: -20,
                                }}
                              />
                            );
                          })
                      : null}
                  </TouchableOpacity>
                  {!isViewUserList &&
                  !isEmpty(storyData[0]?.story_data[current]?.views) ? (
                    <Text style={styles.highLightText}>Activity</Text>
                  ) : null}
                </View>
              </View>
              {!isViewUserList ? (
                storyData[userIndex]?.story_data[current]?.is_highlighted ===
                0 ? (
                  <View
                    style={{
                      width: "50%",
                      alignItems: "center",
                      paddingRight: 10,
                      flexDirection: "row",
                      justifyContent: "flex-end",
                    }}
                  >
                    <TouchableOpacity
                      style={{ alignItems: "center" }}
                      activeOpacity={0.8}
                      onPress={() => {
                        handleToHighlight();
                      }}
                    >
                      <CustomIcon
                        name="heartoutline"
                        size={35}
                        color={BaseColors.activeTab}
                      />
                      <Text style={styles.highLightText}>Highlighted</Text>
                    </TouchableOpacity>
                    {storyData[userIndex]?.self === true ? (
                      <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => setMoreInfo(true)}
                        style={{ marginBottom: 20 }}
                      >
                        <CustomIcon name={"dot"} size={30} color={"#484848"} />
                      </TouchableOpacity>
                    ) : null}
                  </View>
                ) : (
                  <View
                    style={{
                      width: "50%",
                      alignItems: "flex-end",
                      paddingRight: 10,
                      flexDirection: "row",
                      justifyContent: "flex-end",
                    }}
                  >
                    <TouchableOpacity
                      style={{ alignItems: "center" }}
                      activeOpacity={0.8}
                      onPress={() => {
                        handleToHighlight();
                      }}
                    >
                      <CustomIcon
                        name="heartfill"
                        size={35}
                        color={BaseColors.activeTab}
                      />
                      <Text style={styles.highLightText}>Highlighted</Text>
                    </TouchableOpacity>
                    {storyData[userIndex]?.self === true ? (
                      <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => setMoreInfo(true)}
                        style={{ marginBottom: 20 }}
                      >
                        <CustomIcon name={"dot"} size={30} color={"#484848"} />
                      </TouchableOpacity>
                    ) : null}
                  </View>
                )
              ) : null}
            </View>
          )}
        </View>
        {isViewUserList ? (
          <View
            style={{
              flex: 1,
              backgroundColor: "#EBEAEA",
              paddingHorizontal: 20,
              borderRadius: 5,
              width: "100%",
            }}
          >
            <View style={styles.countView}>
              <Text style={styles.countTextStyle}>
                {storyData[userIndex]?.story_data[current]?.view_counts || 0}{" "}
                View
              </Text>
            </View>
            <FlatList
              data={storyData[0]?.story_data[current]?.views}
              keyExtractor={(item, index) => index.toString()}
              renderItem={(item, index) => renderData(item, current)}
            />
          </View>
        ) : null}
        <ShareListModal
          visible={shareListModal}
          setModalVisible={() => {
            setIsShareListModal(!shareListModal);
          }}
          buttonText={"Share"}
          listData={removeDuplicates(getUserShareList, "username") || []}
          shareItem={storyData[userIndex]?.story_data[current]?.story_id}
          handleBtnPress={(s, i) => {
            onSharePost(s?.item, i);
          }}
        />

        <MoreInfoModal
          visible={onMoreInfo}
          setModalVisible={() => setMoreInfo(!onMoreInfo)}
          listData={[
            {
              id: 3,
              title: "delete",
              color: "#FF0000",
            },
          ]}
          onBtnPress={async (item1) => {
            if (item1?.id === 3) {
              setMoreInfo(false);
              await dispatch(
                setModalData({
                  type: "deleteStory",
                  title: "deleteStoryText",
                  buttonTxt: "delete",
                  cancelText: "No",
                  icon: "Delete",
                  visible: true,
                  extraData: {
                    story_id:
                      storyData[userIndex]?.story_data[current]?.story_id,
                    currentIndex: current,
                    userIndex: userIndex,
                    navigation: navigation,
                  },
                })
              );
              navigation.goBack();
            } else {
              setMoreInfo(false);
            }
          }}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default memo(StoryViewComponent);
