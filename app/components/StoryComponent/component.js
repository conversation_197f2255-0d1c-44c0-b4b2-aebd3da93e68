import { FlatList, Text, TouchableOpacity, View } from "react-native";
// import FastImage from "react-native-fast-image";
import styles from "./styles";
import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import Toast from "react-native-simple-toast";
import UserViewProfileModal from "@components/userViewProfileModal";
import { useState } from "react";
import { handleFollowToggle } from "@app/utils/commonFunction";
import { useSelector } from "react-redux";

const FastImage = require("react-native-fast-image");

export const UserStoryList1 = ({
  data,
  navigation,
  currentIndex = 0,
  handleToPressProfile = () => {},
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState({});
  const {userStoryList} = useSelector((s)=>s.auth)

  const handleToFollowAnimation = async (e) => {
    userStoryList?.data[0]?.story_data.forEach(item => {
      // console.log("Your item is : ",item)
      item.views.forEach(user => {
        if (user.user_id === e) {
          user.is_followed = !user.is_followed;
        }
      });
    });
  }
  const onPressRemoveFollowers = async (e) => {
    handleToFollowAnimation(data?.item?.user_id)
    const resp = await handleFollowToggle(data?.item?.user_id);
    if (resp !== undefined && resp?.data?.success) {
      setModalVisible(false);
    } else {
      setModalVisible(false)
      handleToFollowAnimation()
      Toast.show(resp?.data?.message || 'Something went wrong please try again')
    }
  };

  const handleToSendMessage = (e) => {
    setModalVisible(false);
    navigation.navigate("MessagesInfo", {
      userInfo: {
        is_blocked: e?.is_blocked,
        conversation_id: e?.conversation_id || "",
        userData: {
          user_dp: e?.user_dp,
          user_id: e?.user_id,
          full_name: e?.full_name,
        },
      },
    });
  };
  return (
    <View
      style={[
        styles.renderItemMainView,
        {
          justifyContent: "space-between",
          borderBottomWidth: 1,
          borderBottomColor: "#DCDFE3",
          paddingBottom: 10,
          paddingTop: 10,
        },
      ]}
    >
      <View style={styles.renderItemMainView}>
        <View style={styles.userStoryImageStyle}>
          <FastImage
            source={{ uri: data?.item?.user_dp }}
            style={{ height: 50, width: 50, borderRadius: 6 }}
            resizeMode="stretch"
          />
          {data?.item?.isLike ? (
            <View style={{ alignItems: "flex-end", marginTop: -7 }}>
              <CustomIcon
                name="BsSuitHeartFill"
                size={14}
                color={BaseColors.activeTab}
              />
            </View>
          ) : null}
        </View>
        <Text style={styles.userNameStyle}>{data?.item?.username}</Text>
      </View>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          setModalVisible(true);
          setModalData(data?.item);
        }}
      >
        <CustomIcon name="dot" size={25} color={BaseColors.fontColor} />
      </TouchableOpacity>
      <UserViewProfileModal
        visible={modalVisible}
        setModalVisible={() => {
          setModalVisible(false);
          setModalData({});
        }}
        data={modalData}
        onPressViewProfile={(e) => handleToPressProfile(e)}
        onPressRemoveFollowers={(e) => onPressRemoveFollowers(e)}
        onPressSendMessage={(e) => handleToSendMessage(e)}
      />
    </View>
  );
};
