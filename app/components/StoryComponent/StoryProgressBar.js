import { BaseColors } from "@config/theme";
import React, { useState, useEffect, useRef } from "react";
import { View, StyleSheet, Animated, Easing } from "react-native";

const StoryProgressBar = ({
  duration,
  isActive,
  onComplete,
  progressWidth,
  completedColor,
  reset, // New prop for resetting the progress
}) => {
  const [progress, setProgress] = useState(new Animated.Value(0));
  const animation = useRef(null);

  useEffect(() => {
    if (reset) {
      resetAnimation(); // Reset the animation
      isActive = true;
    }
  }, [reset]);

  useEffect(() => {
    if (isActive) {
      startAnimation();
    } else {
      stopAnimation();
    }
  }, [duration, isActive]);

  const widthInterpolate = progress.interpolate({
    inputRange: [0, 1],
    outputRange: ["0%", "100%"],
  });

  const startAnimation = () => {
    animation.current = Animated.timing(progress, {
      toValue: 1,
      duration: duration,
      easing: Easing.linear,
      useNativeDriver: false,
    });
    animation.current.start(({ finished }) => {
      if (finished && onComplete) {
        onComplete();
      }
    });
  };

  const stopAnimation = () => {
    if (animation.current) {
      animation.current.stop(); // Stop the animation
    }
  };

  const resetAnimation = () => {
    setProgress(new Animated.Value(0)); // Reset the progress value
    if (isActive) {
      startAnimation(); // Restart the animation if isActive is true
    }
  };

  return (
    <View style={[styles.container, { width: progressWidth || "100%" }]}>
      <Animated.View
        style={[
          styles.progressBar,
          { width: widthInterpolate },
          completedColor && !isActive ? { backgroundColor: "red" } : null, // Change color when completed
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 5,
    width: "100%",
    backgroundColor: "#ddd",
    borderRadius: 2,
    overflow: "hidden",
    marginHorizontal: 2,
  },
  progressBar: {
    height: "100%",
    backgroundColor: BaseColors.activeTab,
    borderRadius: 2,
  },
});

export default StoryProgressBar;
