/* eslint-disable react-native/no-inline-styles */
import React, { useRef, useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { createStackNavigator } from "@react-navigation/stack";
// Import Screens
import { BaseColors } from "../config/theme";
import Home from "../screens/Home";
import styles from "./styles";
import Pushnotification from "../components/Pushnotification";
import { CurvedBottomBarExpo } from "react-native-curved-bottom-bar";
import { CustomIcon } from "@config/LoadIcons";
import CustomFloatingActionItem from "@components/CustomFloatingActionItem/CustomFloatingActionItem";
import ViewReels from "@screens/ViewReels";
import Profile from "@screens/Profile";
import ChatScreen from "@screens/Chat";
import { navigationRef } from "./NavigationService";
import { useSelector } from "react-redux";
import { isEmpty } from "lodash-es";
import dayjs from "dayjs";
import PurChasePlanModal from "@components/PurchasePlanModal";
import ChatTab from "@screens/Chat/chatTab";
import GroupChat from "@screens/Chat/groupChat";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const interpolate = require("react-native-reanimated").interpolate;
const useAnimatedStyle = require("react-native-reanimated").useAnimatedStyle;
const useSharedValue = require("react-native-reanimated").useSharedValue;
const withTiming = require("react-native-reanimated").withTiming;
const Easing = require("react-native-reanimated").Easing;

const Stack = createStackNavigator();

// Bottom Tab Data
const data = [
  { title: "Create Post", key: "post" },
  { title: "Create Reel", key: "reel" },
  { title: "Draft", key: "draft" },
];

const HomeStack = ({ navigation, route }) => {
  return (
    <Stack.Navigator
      initialRouteName="HomeTab"
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen
        name="HomeTab"
        component={Home}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ProfileNew"
        component={Profile}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

const ChatStack = ({ navigation }) => {
  return (
    <Stack.Navigator
      initialRouteName="chatTab"
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen
        name="GroupChat"
        component={GroupChat}
        navigation={navigation}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="chatTab"
        component={ChatTab}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="chat"
        component={ChatScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ProfileNew1"
        component={Profile}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

const ReelsStack = ({ navigation }) => {
  return (
    <Stack.Navigator
      initialRouteName="reels"
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen
        name="reels"
        component={ViewReels}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ProfileNew"
        component={Profile}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

const ProfileStack = ({ navigation }) => {
  return (
    <Stack.Navigator
      initialRouteName="profile"
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen
        name="profile"
        component={Profile}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ProfileNew"
        component={Profile}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

// Return Tab bar icon for Active and Inactive tab
const TabBarIcon = (route, focused) => {
  let iconName;
  if (route === "Home") {
    iconName = focused ? "home_icon" : "home_icon";
  } else if (route === "Chat") {
    iconName = focused ? "BsChat" : "BsChat";
  } else if (route === "Reel") {
    iconName = focused ? "reel-outline" : "reel-outline";
  } else if (route === "Profile") {
    iconName = focused ? "user" : "user";
  }

  return (
    <>
      <CustomIcon
        name={iconName}
        size={22}
        color={
          focused === route ? BaseColors.activeTab : BaseColors.inactiveTab
        }
      />

      <View>
        <Text
          style={{
            color:
              focused === route ? BaseColors.activeTab : BaseColors.inactiveTab,
          }}
        >
          {route}
        </Text>
      </View>
    </>
  );
};

// Final function
const BottomTabBar = ({ navigation }) => {
  const [isFabVisible, setFabVisible] = useState(false);
  const rotation = useSharedValue(0);
  const { totalMsgCount } = useSelector((s) => s.socket);
  const { isCurrentPlan, activePlanData } = useSelector((s) => s.auth);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const date = dayjs(activePlanData?.plan_expire_date).unix();
  const currentDate = dayjs().unix();

  const rotateUp = () => {
    rotation.value = withTiming(180, {
      duration: 300,
      easing: Easing.linear,
    });
  };
  const rotateDown = () => {
    rotation.value = withTiming(0, {
      duration: 300,
      easing: Easing.linear,
    });
  };

  const handleTabNavigation = (routeName, navigate) => {
    navigate(routeName);
    setFabVisible(false);
    rotateDown();
  };
  const lastTapRef = useRef(null);

  const renderTabBar = ({
    routeName,
    selectedTab,
    onSingleTap,
    onDoubleTap,
    navigate,
  }) => {
    const handlePress = () => {
      const now = Date.now();
      if (lastTapRef.current && now - lastTapRef.current < 300) {
        if (routeName === "Home") {
          navigationRef.current.navigate("Home", { screen: "HomeTab" });
        } else if (routeName === "Reel") {
          navigationRef.current.navigate("Reel", { screen: "reels" });
        }
      } else {
        // Single tap detected
        if (routeName === "Home") {
          navigationRef.current.navigate("Home", { screen: "HomeTab" });
        } else if (routeName === "Reel") {
          navigationRef.current.navigate("Reel", { screen: "reels" });
        } else {
          handleTabNavigation(routeName, navigate);
        }
      }
      lastTapRef.current = now;
    };
    return (
      <TouchableOpacity
        onPress={() => handlePress()}
        style={styles.tabbarItem}
        activeOpacity={0.8}
      >
        {routeName === "Chat" && totalMsgCount > 0 ? (
          <View style={styles.chatNotifyDot} />
        ) : null}
        {TabBarIcon(routeName, selectedTab)}
      </TouchableOpacity>
    );
  };

  const iconStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotate: `${interpolate(rotation.value, [0, 60], [0, 60])}deg`,
        },
      ],
    };
  });

  // navigation
  const handleNavigation = (key) => {
    if (key === "post") {
      if (
        activePlanData?.is_prime_user ||
        activePlanData?.is_free_user ||
        activePlanData?.allow_unlimited_post === 1
      ) {
        navigation.navigate("AddPostScreen", { goToPost: true });
        setFabVisible(false);
      } else {
        if (
          (!isEmpty(activePlanData) &&
            activePlanData?.post_count > 0 &&
            date >= currentDate) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          navigation.navigate("AddPostScreen", { goToPost: true });
          setFabVisible(false);
        } else {
          setFabVisible(false);
          setIsPaymentModal(true);
        }
      }
    } else if (key === "reel") {
      if (
        activePlanData?.is_prime_user ||
        activePlanData?.is_free_user ||
        activePlanData?.allow_unlimited_reel === 1
      ) {
        navigation.navigate("AddReelScreen", { goToPost: true });
        setFabVisible(false);
      } else {
        if (
          (!isEmpty(activePlanData) &&
            activePlanData?.post_reels_count > 0 &&
            date >= currentDate) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          navigation.navigate("AddReelScreen", { goToPost: true });
          setFabVisible(false);
        } else {
          setFabVisible(false);
          setIsPaymentModal(true);
        }
      }
    } else {
      navigation.navigate("DraftList");
      setFabVisible(false);
    }
  };
  const handleToggle = () => {
    setFabVisible(!isFabVisible);
    if (isFabVisible) {
      rotateUp();
    } else {
      rotateDown();
    }
  };
  return (
    <>
      {isFabVisible ? (
        <Animated.View entering={FadeInDown} style={styles.bottomMain}>
          <CustomFloatingActionItem
            data={data}
            onPress={(key) => handleNavigation(key)}
          />
        </Animated.View>
      ) : null}

      <CurvedBottomBarExpo.Navigator
        type="DOWN"
        style={styles.mainTabView}
        shadowStyle={styles.shawdow}
        height={55}
        circleWidth={100}
        bgColor="white"
        initialRouteName="Home"
        renderCircle={({ selectedTab, navigate }) => (
          <Animated.View style={styles.btnCircleUp}>
            <View style={styles.button}>
              <TouchableOpacity
                onPress={() => handleToggle()}
                style={styles.addButton}
                activeOpacity={1}
              >
                <Animated.View style={[styles.arrowIconContainer, iconStyle]}>
                  <CustomIcon
                    name={isFabVisible ? "BsX" : "add"}
                    size={isFabVisible ? 30 : 22}
                    color={BaseColors.black}
                  />
                </Animated.View>
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}
        tabBar={renderTabBar}
      >
        <CurvedBottomBarExpo.Screen
          position="LEFT"
          name="Home"
          component={HomeStack}
          options={{
            tabBarLabel: "Home",
            title: "Home",
            headerShown: false,
          }}
        />
        <CurvedBottomBarExpo.Screen
          position="LEFT"
          name="Chat"
          component={ChatStack}
          options={{
            tabBarLabel: "Chat",
            title: "Chat",
            headerShown: false,
          }}
        />

        <CurvedBottomBarExpo.Screen
          position="RIGHT"
          name="Reel"
          component={ReelsStack}
          options={{
            tabBarLabel: "Reel",
            title: "Reel",
            headerShown: false,
          }}
        />
        <CurvedBottomBarExpo.Screen
          position="RIGHT"
          name="Profile"
          component={ProfileStack}
          options={{
            tabBarLabel: "Profile",
            title: "Profile",
            headerShown: false,
          }}
        />
      </CurvedBottomBarExpo.Navigator>
      <Pushnotification />
      {isPaymentModal ? (
        <PurChasePlanModal
          visible={isPaymentModal}
          setModalVisible={(e) => setIsPaymentModal(e)}
          text={"currentlyPlanText"}
          navigation={navigation}
        />
      ) : null}
    </>
  );
};

export default BottomTabBar;
