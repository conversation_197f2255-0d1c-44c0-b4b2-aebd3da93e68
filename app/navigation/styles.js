import { BaseColors } from "@config/theme";
import { FontFamily } from "../config/typography";
import { Platform, StyleSheet } from "react-native";

export default StyleSheet.create({
  labelStyle: {
    fontFamily: FontFamily.RobotoRegular,
  },
  addButton: {
    backgroundColor: "#F5F5F5",
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  addIcon: {
    width: 24,
    height: 24,
    tintColor: "white",
  },
  btnCircleUp: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#E8E8E8",
    bottom: 30,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 1,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  mainTabView: {
    elevation: 50,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: Platform.OS === "ios" ? 0.5 : 1,
  },
  shawdow: {
    shadowColor: "#DDDDDD",
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 5,
  },
  button: {
    flex: 1,
    justifyContent: "center",
  },

  btnCircleUp: {
    flex: 1,
    width: 60,
    height: 60,
    alignItems: "center",
    justifyContent: "center",
    bottom: 30,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 1,
  },
  imgCircle: {
    width: 30,
    height: 30,
    tintColor: "gray",
  },
  tabbarItem: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  img: {
    width: 30,
    height: 30,
  },
  bottomMain: {
    width: "100%",
    alignItems: "center",
    position: "absolute",

    bottom: 100,

    zIndex: 99,

    alignSelf: "center",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    width: "92%",
    borderRadius: 10,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 1,
    elevation: 1,
  },
  chatNotifyDot: {
    width: 10,
    height: 10,
    backgroundColor: BaseColors.activeTab,
    position: "absolute",
    top: 5,
    right: 21,
    borderRadius: 50,
    zIndex: 1,
  },
});
