// Import React
import React from "react";

// Import Navigators from React Navigation
import { createDrawerNavigator } from "@react-navigation/drawer";

import { SafeAreaView, StyleSheet, Text, View } from "react-native";

import CustomSidebarMenu from "./CustomSidebarMenu";

// Import Screens
import { translate } from "../lang/Translate";
import { FontFamily } from "../config/typography";
import { BaseColors } from "../config/theme";
import BottomTabBar from "./BottomTabBar";
import CMS from "@screens/CMS/index";
import ChangePassword from "@screens/ChangePassword/index";
import { navigationRef } from "./NavigationService";
import PaymentHistory from "@screens/PaymentHistory";
import SavedScreen from "@screens/SavedScreen";
import ContactUs from "@screens/ContactUs";
import FAQ from "@screens/FAQ/FAQ";
import { CustomIcon } from "@config/LoadIcons";
import { useSelector } from "react-redux";
import PaymentPlans from "@screens/Payments";
import AddOnPlans from "@screens/AddOnPlans";
import { createStackNavigator } from "@react-navigation/stack";
import CancelCurrentPlan from "@screens/CancelCurrentPlan";
import AddReview from "@screens/AddReview";

const Drawer = createDrawerNavigator();
const Stack = createStackNavigator();

const drawerLabelWithIcon = (iconType, label, color) => {
  return (
    <View style={styles.mainContainer}>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        <Text style={{ ...styles.drawerLabel, color }}>{translate(label)}</Text>

        <CustomIcon name="BsChevronRight" size={20} color={BaseColors.black} />
      </View>
    </View>
  );
};

const SubscriptionStack = () => {
  return (
    <Stack.Navigator initialRouteName="MySubScription">
      <Stack.Screen
        name="MySubScription"
        component={PaymentPlans}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AddOnPlans"
        component={AddOnPlans}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="CancelCurrentPlan"
        component={CancelCurrentPlan}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};
const SideBar = () => {
  const checkRoute = navigationRef.current.getCurrentRoute();
  const { userData, activePlanData } = useSelector((auth) => auth.auth);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: BaseColors.white }}>
      <Drawer.Navigator
        initialRouteName="DrawerHome"
        screenOptions={{
          headerShown: false,
          drawerStyle: {
            backgroundColor: BaseColors.whiteSmoke,
            width: "65%",
          },
          drawerType: "slide",
          keyboardDismissMode: "on-drag",
          drawerActiveBackgroundColor: BaseColors.activeTab,
          drawerInactiveBackgroundColor: "transparent",
          overlayColor: "transparent",
          drawerActiveTintColor: BaseColors.white,
          drawerLabelStyle: {
            fontSize: 16,
            fontFamily: FontFamily.RobotoMedium,
            lineHeight: 19.2,
            color: BaseColors.White,
          },
          drawerItemStyle: {
            marginHorizontal: 0,
            fontSize: 16,
            fontFamily: FontFamily.RobotoMedium,
            lineHeight: 19.2,
            color: BaseColors.White,
          },
        }}
        drawerContent={CustomSidebarMenu}
      >
        <Drawer.Screen
          name="DrawerHome"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon("DrawerHome", "DrawerHome", color),
            drawerItemStyle: { height: 0 },
            swipeEnabled: checkRoute?.name === "Home",
          }}
          component={BottomTabBar}
        />
          <Drawer.Screen
            name="PaymentHistory"
            options={{
              drawerLabel: ({ color }) =>
                drawerLabelWithIcon("PaymentHistory", "Payment History", color),
              swipeEnabled: false,
            }}
            component={PaymentHistory}
          />
        <Drawer.Screen
          name="Saved"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon("Saved", "Saved", color),
            swipeEnabled: false,
          }}
          component={SavedScreen}
        />
          <Drawer.Screen
            name="MySubScription"
            options={{
              drawerLabel: ({ color }) =>
                drawerLabelWithIcon("MySubScription", "My Subscription", color),
            }}
            component={SubscriptionStack}
          />

        {userData?.type !== "social" ? (
          <Drawer.Screen
            name="ChangePassword"
            options={{
              drawerLabel: ({ color }) =>
                drawerLabelWithIcon("ChangePassword", "Change Password", color),
              swipeEnabled: false,
            }}
            component={ChangePassword}
          />
        ) : null}
        <Drawer.Screen
          name="Terms&Conditions"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon(
                "TermsConditions",
                "Terms & Conditions",
                color
              ),
            swipeEnabled: false,
          }}
          component={CMS}
        />
        <Drawer.Screen
          name="PrivacyPolicy"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon("PrivacyPolicy", "Privacy Policy", color),
            swipeEnabled: false,
          }}
          component={CMS}
        />
        <Drawer.Screen
          name="Contact Us"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon("ContactUs", "Contact Us", color),
            swipeEnabled: false,
          }}
          component={ContactUs}
        />
        <Drawer.Screen
          name="AboutUs"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon("aboutUsIcon", "About Us", color),
            swipeEnabled: false,
          }}
          component={CMS}
        />
        <Drawer.Screen
          name="FAQ"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon("FAQ", "FAQ", color),
            swipeEnabled: false,
          }}
          component={FAQ}
        />
        <Drawer.Screen
          name="AddReview"
          options={{
            drawerLabel: ({ color }) =>
              drawerLabelWithIcon("AddReview", "Add Review", color),
            swipeEnabled: false,
          }}
          component={AddReview}
        />
      </Drawer.Navigator>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    marginLeft: 8,
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    width: "110%",
    alignItems: "center",
  },
  drawerIcon: {
    justifyContent: "flex-end",
    position: "absolute",
    right: 0,
    alignSelf: "center",
  },
  drawerLabel: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.White,
    textAlignVertical: "center",
    alignItems: "center",
    lineHeight: 19.2,
  },
});

export default SideBar;
