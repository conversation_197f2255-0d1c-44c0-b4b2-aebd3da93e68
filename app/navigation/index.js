import 'react-native-gesture-handler';
import React from 'react';
import DatePicker from 'react-native-datepicker';
import { Text, TextInput } from 'react-native';
import NetworkModal from '@screens/NetworkModal';

// Remove font scale
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false;
DatePicker.defaultProps = DatePicker.defaultProps || {};
DatePicker.defaultProps.allowFontScaling = false;

const NavStart = () => {
  return (
    <>
      <NetworkModal />
    </>
  );
};

export default NavStart;
