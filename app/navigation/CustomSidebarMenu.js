import React from "react";
import { View, StyleSheet, Text, Platform } from "react-native";

import {
  DrawerContentScrollView,
  DrawerItem,
  DrawerItemList,
} from "@react-navigation/drawer";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useState } from "react";
import ModalComponent from "../components/Modal";
import { translate } from "../lang/Translate";
import { FontFamily } from "../config/typography";
import { BaseColors } from "../config/theme";
import { images } from "../config/images";
// import FastImage from "react-native-fast-image";
import { CustomIcon } from "@config/LoadIcons";
import authAction from "../redux/reducers/auth/actions";
import { useDispatch, useSelector } from "react-redux";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";
import { getApiData } from "@app/utils/apiHelper";

const {
  logOut,
  setModalData,
  setCreatedPostList,
  setSavedPostList,
  setReelsList,
  setSavedReelList,
  setUserStoryList,
  setUUid
} = authAction;

const CustomSidebarMenu = (props) => {
  const dispatch = useDispatch();

  // redux state
  const {
    modalData,
    createdPostList,
    savedPostList,
    reelsList,
    savedReelList,
    userStoryList,
  } = useSelector((state) => state.auth);
  const { socketObj } = useSelector((state) => state.socket);

  // state
  const [loading, setIsLoading] = useState(false);
  const [isAddReview, setIsAddReView] = useState(false);

  const logoutApiCall = async () => {
    setIsLoading(true);
    try {
      const resp = await getApiData(
        BaseSetting.endpoints.logout,
        "POST",
        {},
        false,
        false
      );
      if (resp?.data?.success) {
        socketObj.disconnect();
        dispatch(logOut());
        dispatch(setModalData({}));
        dispatch(setUUid(''));
        AsyncStorage.clear();
        props.navigation.replace("Auth");
        props.navigation.toggleDrawer();
      } else {
        setIsLoading(false);
        Toast.show(resp?.data?.message);
      }
    } catch (error) {
      props.navigation.toggleDrawer();
    }
  };

  // Delete Account Function
  const DeleteAccountApiCall = async (d) => {
    const finalData = {
      reason:
        d?.deleteReason === "Others" ? d?.deleteReasonText : d?.deleteReason,
    };
    setIsLoading(true);
    try {
      const resp = await getApiData(
        BaseSetting.endpoints.DeleteAccount,
        "POST",
        finalData,
        false,
        false
      );
      if (resp?.data?.success) {
        setIsLoading(false);
        dispatch(logOut());
        dispatch(setModalData({}));
        AsyncStorage.clear();
        props.navigation.replace("Auth");
        props.navigation.toggleDrawer();
        Toast.show("Account Deleted Successfully");
      } else {
        setIsLoading(false);
        Toast.show(resp?.data?.message);
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  // Delete Post or Reel Function
  const DeletePostApiCall = async (deleteType) => {
    setIsLoading(true);
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.deletePostOrReel}/${modalData?.extraData}`,
        "POST",
        {},
        false,
        false
      );
      if (resp?.data?.success) {
        if (deleteType === "postAndReel") {
          // Update Post & Reel List
          const updatedPostsData = createdPostList?.data?.filter((post) => {
            // Check if post_id or reel_id matches the id to be removed
            return !(
              post?.post_id === modalData?.extraData ||
              post?.reel_id === modalData?.extraData
            );
          });

          if (updatedPostsData) {
            dispatch(
              setCreatedPostList({
                ...createdPostList,
                data: updatedPostsData,
              })
            );
          }

          const updatedSavedPosts = savedPostList?.data?.filter(
            (post) => post.post_id !== modalData?.extraData
          );

          if (updatedSavedPosts) {
            dispatch(
              setSavedPostList({
                ...savedPostList,
                data: updatedSavedPosts,
              })
            );
          }
        } else if (deleteType === "reelOnly") {
          const updatedReelList = reelsList?.data?.filter(
            (post) => post.reel_id !== modalData?.extraData
          );

          const updatedPostsData = createdPostList?.data?.filter(
            (post) => post.reel_id !== modalData?.extraData
          );

          const updatedSavedReels = savedReelList?.data?.filter(
            (post) => post.reel_id !== modalData?.extraData
          );

          dispatch(
            setSavedReelList({ ...savedReelList, data: updatedSavedReels })
          );

          dispatch(
            setReelsList({
              ...reelsList,
              data: updatedReelList,
            })
          );

          if (updatedPostsData) {
            dispatch(
              setCreatedPostList({
                ...createdPostList,
                data: updatedPostsData,
              })
            );
          }
        }
        dispatch(setModalData({}));
        setIsLoading(false);
        Toast.show("Deleted Successfully");
      } else {
        setIsLoading(false);
        Toast.show(resp?.data?.message);
      }
      setIsLoading(false);
    } catch (error) {
      console.log("🚀 ~ DeletePostApiCall ~ error:", error);
      setIsLoading(false);
    }
  };

  const DeleteStoryApiCall = async () => {
    setIsLoading(true);
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.deleteStory}/${modalData?.extraData?.story_id}`,
        "POST",
        {},
        false,
        false
      );
      if (resp?.data?.success) {
        const userData = userStoryList?.data[modalData?.extraData?.userIndex];

        // Filter out the story with the matching story ID
        userData.story_data = userData.story_data.filter(
          (story) => story.story_id !== modalData?.extraData?.story_id
        );

        // Update the story count
        userData.story_count = userData.story_data.length;

        dispatch(setUserStoryList(userStoryList));
        dispatch(setModalData({}));
        setIsLoading(false);
        Toast.show("Deleted Successfully");
      } else {
        setIsLoading(false);
        Toast.show(resp?.data?.message);
      }
      setIsLoading(false);
    } catch (error) {
      console.log("🚀 ~ DeletePostApiCall ~ error:", error);
      setIsLoading(false);
    }
  };

  const versionValue = require("../.././version-update.json");
  return (
    <>
      <View style={stylesSidebar.sideMenuContainer}>
        {/* <DrawerItem
          label={() => (
            <View
              style={[
                stylesSidebar.mainContainer,
                { marginTop: Platform.OS === "ios" ? 0 : 0 },
              ]}
            >
              <FastImage
                source={images.footMainLogo}
                style={{
                  width: 120,
                  height: 79,
                }}
                resizeMode="contain"
              />
            </View>
          )}
          pressColor={BaseColors.white}
        /> */}
        <DrawerContentScrollView
          {...props}
          bounces={false}
          style={{ marginBottom: 60 }}
          showsVerticalScrollIndicator={false}
        >
          <DrawerItemList
            {...props}
            style={{
              fontSize: 22,
              fontFamily: FontFamily.regular,
              color: BaseColors.gray4,
            }}
          />
          <DrawerItem
            label={() => (
              <View style={stylesSidebar.mainContainer}>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: "120%",
                  }}
                >
                  <Text style={{ ...stylesSidebar.drawerLabel }}>
                    {translate("deleteAccount")}
                  </Text>

                  <CustomIcon
                    name="BsChevronRight"
                    size={20}
                    color={BaseColors.black}
                  />
                </View>
              </View>
            )}
            onPress={() => {
              dispatch(
                setModalData({
                  type: "deleteAccount",
                  title: "deleteAccountText",
                  buttonTxt: "delete",
                  cancelText: "No",
                  icon: "Leave",
                  visible: true,
                })
              );
            }}
          />
        </DrawerContentScrollView>
        <DrawerItem
          style={{
            position: "absolute",
            bottom: 45,
            left: 0,
            right: 0,
          }}
          label={() => (
            <View
              style={[
                stylesSidebar.mainContainer,
                {
                  justifyContent: "space-between",
                  width: "120%",
                },
              ]}
            >
              <Text style={{ ...stylesSidebar.drawerLabel }}>
                {translate("Log out")}
              </Text>

              <CustomIcon
                name={"Leave"}
                size={20}
                color={BaseColors.activeTab}
              />
            </View>
          )}
          onPress={() => {
            dispatch(
              setModalData({
                type: "logOut",
                title: "areYouSure",
                buttonTxt: "Logout",
                cancelText: "No",
                icon: "Leave",
                visible: true,
              })
            );
          }}
        />
          <DrawerItem
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
            }}
            label={() => (
              <View
                style={[
                  stylesSidebar.mainContainer,
                  {
                    justifyContent: "space-between",
                    width: "120%",
                  },
                ]}
              >
                <Text
                  style={{
                    ...stylesSidebar.drawerLabel,
                    color: BaseColors.gray,
                  }}
                >
                  {`Version : ${versionValue?.version}`}
                </Text>
              </View>
            )}
          />
      </View>
      <ModalComponent
        visible={modalData?.visible || false}
        modalTitle={translate(modalData?.title)}
        // modalDescription={translate("areYouSure")}
        logoutModal={true}
        buttonTxt={translate(modalData?.buttonTxt)}
        cancelText={translate(modalData?.cancelText)}
        isLoader={false}
        onClickSaveBtn={(d) => {
          modalData?.type === "deleteStory"
            ? DeleteStoryApiCall()
            : modalData?.type === "deletePost"
              ? DeletePostApiCall("postAndReel")
              : modalData?.type === "deleteReelsOnly"
                ? DeletePostApiCall("reelOnly")
                : modalData?.type === "deleteAccount"
                  ? DeleteAccountApiCall(d)
                  : logoutApiCall();
        }}
        centerIconName={modalData?.icon}
        dropDown={modalData?.type === "deleteAccount" ? true : false}
        loading={loading}
        type="sideBar"
      />
    </>
  );
};

export default CustomSidebarMenu;

const stylesSidebar = StyleSheet.create({
  mainContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 0,
    paddingTop: 0,
  },
  drawerIcon: { width: 30, height: 30, marginRight: 0 },
  sideMenuContainer: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: BaseColors.white,
    color: BaseColors.White,
  },
  drawerLabel: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: "#7A7979",
    textAlignVertical: "center",
    alignItems: "center",
    lineHeight: 19.2,
  },
  drawerLabel1: {
    fontSize: 26,
    lineHeight: 34,
    letterSpacing: 1,
    color: "rgba(10, 43, 54, 1)",
    fontFamily: FontFamily.RobotoMedium,
  },
});
