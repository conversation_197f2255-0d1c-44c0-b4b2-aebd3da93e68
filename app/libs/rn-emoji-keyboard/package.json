{"name": "rn-emoji-keyboard", "version": "1.7.0", "description": "Super performant, lightweight, fully customizable emoji picker. Designated to be user and developer friendly! 💖", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "cpp", "*.podsp<PERSON>", "!lib/typescript/example", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"test": "jest", "typecheck": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepack": "bob build", "release": "release-it", "example": "yarn --cwd example", "bootstrap": "yarn example && yarn install", "generateIcons": "node ./scripts/generateIcons.js"}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/TheWidlarzGroup/rn-emoji-keyboard", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jakex7)", "license": "MIT", "bugs": {"url": "https://github.com/TheWidlarzGroup/rn-emoji-keyboard/issues"}, "homepage": "https://thewidlarzgroup.github.io/rn-emoji-keyboard", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "devDependencies": {"@commitlint/config-conventional": "^17.0.2", "@evilmartians/lefthook": "^1.2.2", "@react-native-community/eslint-config": "^3.0.2", "@release-it/conventional-changelog": "^5.0.0", "@testing-library/react-native": "12.1.2", "@types/jest": "^28.1.2", "@types/react": "~17.0.21", "@types/react-native": "0.70.0", "commitlint": "^17.0.2", "del-cli": "^5.0.0", "eslint": "^8.4.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "pod-install": "^0.1.0", "prettier": "^2.0.5", "react": "18.2.0", "react-native": "0.71.8", "react-native-builder-bob": "^0.23.2", "react-test-renderer": "18.2.0", "release-it": "^15.0.0", "typescript": "^5.0.2", "unicode-emoji-json": "0.4.0", "emojilib": "^3.0.10"}, "resolutions": {"@types/react": "17.0.21"}, "peerDependencies": {"react": "*", "react-native": "*"}, "engines": {"node": ">= 16.0.0"}, "packageManager": "yarn@1.22.21", "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "all", "useTabs": false, "semi": false, "printWidth": 100}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "all", "useTabs": false, "semi": false, "printWidth": 100}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}}