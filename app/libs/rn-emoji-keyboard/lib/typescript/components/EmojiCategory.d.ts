import * as React from 'react';
import { type EmojisByCategory } from '../types';
export declare const EmojiCategory: React.MemoExoticComponent<({ item: { title, data }, setKeyboardScrollOffsetY, activeCategoryIndex, }: {
    item: EmojisByCategory;
    setKeyboardScrollOffsetY: React.Dispatch<React.SetStateAction<number>>;
    activeCategoryIndex: number;
}) => React.JSX.Element>;
//# sourceMappingURL=EmojiCategory.d.ts.map