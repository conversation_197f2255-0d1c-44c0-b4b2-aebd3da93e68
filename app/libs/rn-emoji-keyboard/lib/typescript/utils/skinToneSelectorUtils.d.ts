export declare const generateToneSelectorPosition: (numOfColumns: number, emojiIndex: number, windowWidth: number, emojiWidth: number, emojiHeight: number, extraSearchTop: number) => {
    x: number;
    y: number;
};
export declare const generateToneSelectorFunnelPosition: (numOfColumns: number, emojiIndex: number, emojiWidth: number) => number;
export declare const insertAtCertainIndex: (arr: string[], index: number, newItem: string) => string[];
export declare const zeroWidthJoiner: string;
export declare const variantSelector: string;
export declare const skinToneCodes: string[];
export declare const removeSkinToneModifier: (emoji: string) => string;
export declare const skinTones: {
    name: string;
    color: string;
}[];
//# sourceMappingURL=skinToneSelectorUtils.d.ts.map