export type RecursivePartial<T> = Partial<{
    [P in keyof T]: T[P] extends Record<string, any> ? RecursivePartial<T[P]> : T[P];
}>;
export declare const deepMerge: <T extends Record<K, any>, K extends keyof T>(source: T, additional: Partial<{ [P in keyof T]: T[P] extends Record<string, any> ? Partial<T[P] extends infer T_1 ? { [P_1 in keyof T_1]: T[P][P_1] extends Record<string, any> ? Partial<T[P][P_1] extends infer T_2 ? { [P_2 in keyof T_2]: T[P][P_1][P_2] extends Record<string, any> ? Partial<T[P][P_1][P_2] extends infer T_3 ? { [P_3 in keyof T_3]: T[P][P_1][P_2][P_3] extends Record<string, any> ? Partial<T[P][P_1][P_2][P_3] extends infer T_4 ? { [P_4 in keyof T_4]: T[P][P_1][P_2][P_3][P_4] extends Record<string, any> ? Partial<T[P][P_1][P_2][P_3][P_4] extends infer T_5 ? { [P_5 in keyof T_5]: T[P][P_1][P_2][P_3][P_4][P_5] extends Record<string, any> ? Partial<T[P][P_1][P_2][P_3][P_4][P_5] extends infer T_6 ? { [P_6 in keyof T_6]: T[P][P_1][P_2][P_3][P_4][P_5][P_6] extends Record<string, any> ? Partial<T[P][P_1][P_2][P_3][P_4][P_5][P_6] extends infer T_7 ? { [P_7 in keyof T_7]: T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7] extends Record<string, any> ? Partial<T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7] extends infer T_8 ? { [P_8 in keyof T_8]: T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8] extends Record<string, any> ? Partial<T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8] extends infer T_9 ? { [P_9 in keyof T_9]: T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8][P_9] extends Record<string, any> ? Partial<T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8][P_9] extends infer T_10 ? { [P_10 in keyof T_10]: T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8][P_9][P_10] extends Record<string, any> ? Partial<any> : T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8][P_9][P_10]; } : never> : T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8][P_9]; } : never> : T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7][P_8]; } : never> : T[P][P_1][P_2][P_3][P_4][P_5][P_6][P_7]; } : never> : T[P][P_1][P_2][P_3][P_4][P_5][P_6]; } : never> : T[P][P_1][P_2][P_3][P_4][P_5]; } : never> : T[P][P_1][P_2][P_3][P_4]; } : never> : T[P][P_1][P_2][P_3]; } : never> : T[P][P_1][P_2]; } : never> : T[P][P_1]; } : never> : T[P]; }>) => T;
//# sourceMappingURL=deepMerge.d.ts.map