import type { CategoryTranslation } from '../types'

const ro: CategoryTranslation = {
  recently_used: 'Utilizate recent',
  smileys_emotion: 'Zâmbete & Emoții',
  people_body: 'Oameni & Corp',
  animals_nature: 'Animale & Natură',
  food_drink: 'Mâncare & Băutură',
  travel_places: 'Călătorii & Locuri',
  activities: 'Activități',
  objects: 'Obiecte',
  symbols: 'Simboluri',
  flags: '<PERSON><PERSON><PERSON><PERSON>',
  search: '<PERSON><PERSON><PERSON><PERSON>',
}
export default ro
