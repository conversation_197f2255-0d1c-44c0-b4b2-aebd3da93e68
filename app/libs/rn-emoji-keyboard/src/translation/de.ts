import type { CategoryTranslation } from '../types'

const de: CategoryTranslation = {
  recently_used: '<PERSON><PERSON><PERSON><PERSON> benutzt',
  smileys_emotion: '<PERSON><PERSON> & Emotion',
  people_body: '<PERSON><PERSON> & <PERSON>rper',
  animals_nature: '<PERSON><PERSON> & Natur',
  food_drink: 'Essen & Trinken',
  travel_places: 'Reisen & Orte',
  activities: 'Aktivitäten',
  objects: 'Objekte',
  symbols: 'Symbole',
  flags: 'Flaggen',
  search: 'Suche',
}
export default de
