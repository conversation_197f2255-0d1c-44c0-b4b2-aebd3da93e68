import type { CategoryTranslation } from '../types'

const tr: CategoryTranslation = {
  recently_used: 'Son <PERSON>',
  smileys_emotion: '<PERSON>at<PERSON> & Duygular',
  people_body: '<PERSON>ns<PERSON><PERSON> & Bedenler',
  animals_nature: '<PERSON>van<PERSON> & Doğa',
  food_drink: 'Yiyecek & İçecek',
  travel_places: 'Seyahat & Mekanlar',
  activities: 'Aktiviteler',
  objects: '<PERSON>b<PERSON><PERSON>',
  symbols: 'Semboller',
  flags: '<PERSON><PERSON><PERSON>',
  search: 'Ara',
}
export default tr
