import type { CategoryTranslation } from '../types'

const pl: CategoryTranslation = {
  recently_used: 'Ostatnio używane',
  smileys_emotion: '<PERSON><PERSON><PERSON><PERSON> i emocje',
  people_body: '<PERSON><PERSON><PERSON>',
  animals_nature: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i przyroda',
  food_drink: 'Jed<PERSON><PERSON> i napoje',
  travel_places: 'Podróże i miejsca',
  activities: 'Aktywność',
  objects: 'Przedmioty',
  symbols: 'Symbole',
  flags: 'Flagi',
  search: 'Szukaj',
}
export default pl
