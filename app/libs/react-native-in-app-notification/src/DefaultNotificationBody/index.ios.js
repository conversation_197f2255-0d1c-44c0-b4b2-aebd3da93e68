import React from "react";
import PropTypes from "prop-types";
import {
  TouchableOpacity,
  StatusBar,
  View,
  Text,
  Image,
  Vibration,
} from "react-native";
import { getStatusBarHeight, isIphoneX } from "react-native-iphone-x-helper";
import GestureRecognizer, {
  swipeDirections,
} from "react-native-swipe-gestures";
import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const styles = {
  container: {
    flex: 1,
    backgroundColor: BaseColors.White,
    marginHorizontal: 15,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.5,
    shadowRadius: 5,
    minHeight: 60,
  },
  content: {
    flex: 1,
    flexDirection: "row",
    alignItem: "center",
    width: "100%",
    paddingRight: 80,
  },
  iconApp: {
    width: "100%",
    height: "95%",
  },
  iconContainer: {
    width: 50,
    height: 50,
    justifyContent: "center",
    alignItem: "center",
    marginHorizontal: 10,
    // backgroundColor: BaseColors.activeTab,
    // borderRadius: 16,
    // borderColor: BaseColors.secondary,
    padding: 5,
    marginTop: 10,
  },
  icon: {
    width: "100%",
    height: "95%",
  },
  textContainer: {
    alignSelf: "center",
  },
  title: {
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
  },
  message: {
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoRegular,
    opacity: 0.9,
    marginTop: 5,
    flexWrap: "wrap",
  },
  footer: {
    backgroundColor: "#696969",
    borderRadius: 5,
    alignSelf: "center",
    height: 5,
    width: 35,
    margin: 5,
  },
};

class DefaultNotificationBody extends React.Component {
  constructor() {
    super();

    this.onNotificationPress = this.onNotificationPress.bind(this);
    this.onSwipe = this.onSwipe.bind(this);
  }

  componentDidUpdate(prevProps) {
    if (this.props.isOpen !== prevProps.isOpen) {
      StatusBar.setHidden(this.props.isOpen);
    }

    if (this.props.vibrate && this.props.isOpen && !prevProps.isOpen) {
      Vibration.vibrate();
    }
  }

  onNotificationPress() {
    const { onPress, onClose } = this.props;

    onClose();
    onPress();
  }

  onSwipe(direction) {
    const { SWIPE_UP } = swipeDirections;

    if (direction === SWIPE_UP) {
      this.props.onClose();
    }
  }

  renderIcon() {
    const { iconApp, icon } = this.props;

    if (icon) {
      return <Image source={icon} style={styles.icon} resizeMode="contain" />;
    } else if (iconApp) {
      return (
        <Image source={iconApp} style={styles.iconApp} resizeMode="contain" />
      );
    }

    return null;
  }

  render() {
    const { title, message } = this.props;

    return (
      // <View style={styles.root}>
      <GestureRecognizer
        onSwipe={this.onSwipe}
        style={{
          ...styles.container,

          marginTop: this.props.isOpen ? 35 : 0,
        }}
      >
        <TouchableOpacity
          style={styles.content}
          activeOpacity={0.8}
          underlayColor="transparent"
          onPress={this.onNotificationPress}
        >
          <View style={styles.iconContainer}>{this.renderIcon()}</View>
          <View style={styles.textContainer}>
            <Text numberOfLines={1} style={styles.title}>
              {title}
            </Text>
            <Text numberOfLines={2} style={styles.message}>
              {message}
            </Text>
          </View>
        </TouchableOpacity>
      </GestureRecognizer>
      // </View>
    );
  }
}

DefaultNotificationBody.propTypes = {
  title: PropTypes.string,
  message: PropTypes.string,
  vibrate: PropTypes.bool,
  isOpen: PropTypes.bool,
  onPress: PropTypes.func,
  onClose: PropTypes.func,
  iconApp: Image.propTypes.source,
  icon: Image.propTypes.source,
};

DefaultNotificationBody.defaultProps = {
  title: "Notification",
  message: "This is a test notification",
  vibrate: true,
  isOpen: false,
  iconApp: null,
  icon: null,
  onPress: () => null,
  onClose: () => null,
};

export default DefaultNotificationBody;
