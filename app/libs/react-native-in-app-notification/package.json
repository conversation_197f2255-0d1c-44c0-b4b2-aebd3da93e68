{"name": "react-native-in-app-notification", "version": "3.2.0", "description": "Customisable in-app notification component for React Native", "main": "src/index.js", "repository": "https://github.com/rob<PERSON>croft/react-native-in-app-notification.git", "author": "<PERSON> <rob<PERSON><PERSON>@users.noreply.github.com>", "license": "MIT", "scripts": {"lint": "eslint Notification.js DefaultNotificationBody.ios.js DefaultNotificationBody.android.js"}, "peerDependencies": {"prop-types": "^15.5.10", "react": ">=16.3.0", "react-native": ">=0.54.0"}, "dependencies": {"hoist-non-react-statics": "^3.0.1", "react-native-iphone-x-helper": "^1.2.0", "react-native-swipe-gestures": "^1.0.2"}, "devDependencies": {"eslint": "^4.18.2", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^4.0.0", "eslint-plugin-react": "^6.10.3"}}