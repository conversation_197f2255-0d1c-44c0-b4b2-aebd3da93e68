/* eslint-disable no-async-promise-executor */
import BaseSetting from '@config/setting';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';

GoogleSignin.configure({
  // scopes: ['https://www.googleapis.com/auth/drive.readonly'],
  webClientId: BaseSetting.googleClientId,
  forceConsentPrompt: true,
});

export const signInWithGoogle = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      resolve(userInfo);
    } catch (error) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // user cancelled the login flow
      } else if (error.code === statusCodes.IN_PROGRESS) {
        // operation (e.g. sign in) is in progress already
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        // play services not available or outdated
      } else {
        // some other error happened
      }
      reject(error);
    }
  });
};

export const signOutGoogle = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      await GoogleSignin.revokeAccess();
      await GoogleSignin.signOut();
      return;
      // this.setState({ user: null }); // Remember to remove the user from your app's state as well
    } catch (error) {
      console.error(error);
    }
  });
};

export const googleSignOut = async () => {
  try {
    await GoogleSignin.revokeAccess();
    let response = await GoogleSignin.signOut();
    return response;
  } catch (error) {
    console.error(error);
    return error;
  }
};
