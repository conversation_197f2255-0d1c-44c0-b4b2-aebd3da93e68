import { BaseColors } from "../config/theme";
import { translate } from "../lang/Translate";
import {
  LayoutAnimation,
  Linking,
  PermissionsAndroid,
  Platform,
  UIManager,
} from "react-native";
import Toast from "react-native-simple-toast";
import { getApiData } from "./apiHelper";
import BaseSetting from "@config/setting";
import { isArray, isEmpty } from "lodash-es";
import ImageCropPicker from "react-native-image-crop-picker";
import ImagePicker from "react-native-image-crop-picker";
const Geocoder = require("react-native-geocoding");
import * as RNIap from "react-native-iap";
// const { FFmpegKit } = require("ffmpeg-kit-react-native");
const { compactFormat } = require("cldr-compact-number");
// const RNFS = require("react-native-fs");
const {
  openSettings,
  check,
  PERMISSIONS,
  request,
  RESULTS,
} = require("react-native-permissions");
const {
  PhoneNumberUtil,
  PhoneNumberType,
  PhoneNumberFormat,
} = require("google-libphonenumber");
const Geolocation = require("@react-native-community/geolocation");

export const selectAndCropImage = async (width = 120, height = 120) => {
  try {
    const image = await ImagePicker.openPicker({
      cropping: true,
      cropperCircleOverlay: true,
      showCropGuidelines: false,
      showCropFrame: false,
    });
    return image;
  } catch (error) {
    console.log(error);
    return null;
  }
};

export const cropImage = async (
  imagePath,
  width = 350,
  height = 350,
  type = ""
) => {
  try {
    const image = await ImageCropPicker.openCropper({
      path: imagePath,
      cropperCircleOverlay: !isEmpty(type) ? true : false,
      showCropGuidelines: !isEmpty(type) ? false : true,
      showCropFrame: !isEmpty(type) ? false : true,
    });
    return image;
  } catch (error) {
    console.log(error);
    return null;
  }
};

export const checkMicrophonePermission = async (startRecording) => {
  const permission = Platform.select({
    ios: PERMISSIONS.IOS.MICROPHONE,
    android: PERMISSIONS.ANDROID.RECORD_AUDIO,
  });

  try {
    const result = await check(permission);

    switch (result) {
      case RESULTS.UNAVAILABLE:
        console.log("Microphone not available on this device.");
        break;
      case RESULTS.DENIED:
        const requestResult = await request(permission);
        if (requestResult === RESULTS.GRANTED) {
          startRecording();
        } else {
          console.log("Microphone permission denied.");
        }
        break;
      case RESULTS.GRANTED:
        startRecording();
        break;
      case RESULTS.BLOCKED:
        console.log(
          "Microphone permission blocked. Please enable it in settings."
        );
        break;
    }
  } catch (error) {
    console.log("Failed to check microphone permission:", error);
  }
};

export const formatNumber = (number, locale = "en") => {
  if (isNaN(number)) {
    return "Invalid number";
  }

  return compactFormat(number, locale, {
    significantDigits: 1,
    minimumFractionDigits: 1,
    maximumFractionDigits: 2,
  });
};

export const enableAnimateInEaseOut = () => {
  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
};

export const enableAnimateLinear = () => {
  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
};

export const enableAnimateSpring = () => {
  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.spring);
};

export const defaultStyle = `
<style>
a {
  color: ${BaseColors.primary}; 
  /* These are technically the same, but use both */
  overflow-wrap: break-word;
  word-wrap: break-word;

  -ms-word-break: break-all;
  /* This is the dangerous one in WebKit, as it breaks things wherever */
  word-break: break-all;
  /* Instead use this non-standard one: */
  word-break: break-word;

  /* Adds a hyphen where the word breaks, if supported (No Blink) */
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}
html, body {font-family: Lato, "Helvetica Neue", Roboto, Sans-Serif; overflow-x: 'hidden'; font-size: 14px; word-break: 'break-all';color: ${BaseColors.black};background-color:${BaseColors.white}; white-space: 'no-wrap'}
img {display: inline; height: auto; max-width: 100%;}
</style>`;

export const getGreeting = () => {
  const currentHour = new Date().getHours();
  let greetingMessage = "";
  if (currentHour >= 4 && currentHour < 12) {
    greetingMessage = "goodMorning";
  } else if (currentHour >= 12 && currentHour < 18) {
    greetingMessage = "goodAfternoon";
  } else {
    greetingMessage = "goodEvening";
  }
  return translate(greetingMessage);
};

export function formatAddress(addressArray) {
  let buildingStreet = "";
  let neighborhood = "";
  let route = "";
  let area = "";
  let state = "";
  let city = "";
  let country = "";

  // Loop through the address components to extract the relevant information
  for (const component of addressArray) {
    if (component?.types.includes("street_number")) {
      buildingStreet = component?.long_name;
    } else if (component?.types.includes("route")) {
      route = component?.long_name;
    } else if (component?.types.includes("neighborhood")) {
      neighborhood = component?.long_name;
    } else if (component?.types.includes("sublocality_level_1")) {
      area = component?.long_name;
    } else if (component?.types.includes("locality")) {
      city = component?.long_name;
    } else if (component?.types.includes("administrative_area_level_1")) {
      state = component?.long_name;
    } else if (component?.types.includes("country")) {
      country = component?.long_name;
    }
  }

  // Construct the formatted address
  const formattedAddress = `${buildingStreet ? buildingStreet : ""} ${
    route ? `${route}-` : ""
  }${area ? `${area}-` : ""}${neighborhood ? `${neighborhood}-` : ""}${
    city ? `${city}-` : ""
  }${state ? `${state}-` : ""} ${country}`;

  return { address: formattedAddress, city: city };
}

export const fetchAddress = async (cord) => {
  // Replace these values with your latitude and longitude
  const latitude = cord.lat;
  const longitude = cord.lng;
  const fullAddress = await Geocoder.from({ lat: latitude, lng: longitude })
    .then((json) => {
      const location = formatAddress(json.results[0].address_components);
      return { address: location?.address, city: location?.city };
    })
    .catch((error) => console.warn(error));
  return fullAddress;
};
export const getMobileNumberLength = (code) => {
  const phoneUtil = PhoneNumberUtil.getInstance();
  const exampleNumber = phoneUtil.getExampleNumberForType(
    code,
    PhoneNumberType.MOBILE
  );
  const formattedNumber = phoneUtil.format(
    exampleNumber,
    PhoneNumberFormat.E164
  );
  const phoneNumber = phoneUtil.parse(formattedNumber);
  nationalNumber = phoneUtil.getNationalSignificantNumber(phoneNumber);
  const length = nationalNumber ? nationalNumber.length : null;
  return length;
};
export const locationPermission = (dispatch, setUserCurrentLocation) => {
  Geolocation.getCurrentPosition(
    async (info) => {
      const successAddress = await fetchAddress({
        lat: info?.coords?.latitude,
        lng: info?.coords?.longitude,
      });
      dispatch(
        setUserCurrentLocation({
          address: successAddress?.address.trim(),
          lat: info?.coords?.latitude,
          lng: info?.coords?.longitude,
          openSetting: false,
          name: "Current location",
        })
      );
    },
    (error) => {
      dispatch(
        setUserCurrentLocation({
          message:
            error?.message !== "No location provider available."
              ? error?.message
              : "Please allow device location.",
          openSetting: true,
        })
      );
      if (Platform.OS === "ios") {
        Linking.openURL("app-settings:");
      } else {
        openSettings();
      }
    }
  );
};

export const checkImg = (file) => {
  let isValidImg = true;
  let isValidImgErrMsg = "";
  const isPNG =
    file.type === "image/png" ||
    file.type === "image/jpeg" ||
    file.type === "image/jpg" ||
    file.type === "image/heic";
  if (!isPNG) {
    isValidImg = false;
    isValidImgErrMsg = "Upload only png, jpeg, jpg format image";
  }
  const isValidSize = file.size < 1024 * 1024 * 10;

  if (!isValidSize) {
    isValidImg = false;
    isValidImgErrMsg = "Image size must be less then 10Mb";
  }

  return {
    isValid: isValidImg,
    errMsg: isValidImgErrMsg,
  };
};

export const checkImgSize = (size, img) => {
  let isValidImg = true;
  let isValidImgErrMsg = "";

  const isPNG =
    img.type === "image/png" ||
    img.type === "image/jpeg" ||
    img.type === "image/jpg" ||
    img.type === "image/heic";
  if (!isPNG) {
    isValidImg = false;
    isValidImgErrMsg = "Upload only png, jpeg, jpg format image";
  }
  const isValidSize = img.size < 1024 * 1024 * Number(size);

  if (!isValidSize) {
    isValidImg = false;
    isValidImgErrMsg = `Image size must be less then ${size}Mb`;
  }

  return {
    isValid: isValidImg,
    errMsg: isValidImgErrMsg,
  };
};

export const checkVideoSize = (size, video) => {
  let isValidImg = true;
  let isValidImgErrMsg = "";

  const isValidSize = isArray(video)
    ? video[0]?.size < 1024 * 1024 * Number(size)
    : video?.size < 1024 * 1024 * Number(size);

  if (!isValidSize) {
    isValidImg = false;
    isValidImgErrMsg = `Video size must be less then ${size}Mb`;
  }

  return {
    isValid: isValidImg,
    errMsg: isValidImgErrMsg,
  };
};

export const uploadFile = async (file1, type) => {
  console.log("🚀 ~ uploadFile ~ file1:", file1);
  try {
    const resp = await getApiData(
      BaseSetting.endpoints.fileUpload,
      "POST",
      { file: file1, type: type },
      false,
      true
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ uploadFile ~ error:", error);
  }
};

export const mobileValidation = async (mobileNo, maxLength) => {
  let isValidMobileNo = true;
  let isValidationMessage = "";
  if (mobileNo.length < maxLength) {
    isValidMobileNo = false;
    isValidationMessage = "Please enter valid phone number";
  } else {
    isValidMobileNo = true;
    isValidationMessage = "";
  }

  return { isValid: isValidMobileNo, validMessage: isValidationMessage };
};

export const calculateTimeAgo = (past_date) => {
  const now = new Date();
  const pastTime = new Date(past_date); // Your past time here
  const timeDifference = now - pastTime;
  const seconds = Math.floor(timeDifference / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  let timeAgoString = "";
  if (hours > 0) {
    timeAgoString = `${hours} hour${hours > 1 ? "s" : ""} ago`;
  } else if (minutes > 0) {
    timeAgoString = `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
  } else {
    timeAgoString = `${seconds} second${seconds !== 1 ? "s" : ""} ago`;
  }

  return timeAgoString;
};

// This function for trim video for first 30 second
export const trimVideo = async (
  inputPath,
  name,
  startTime,
  endTime,
  oldFile
) => {
  try {
    // let destPath = "";
    // let fileUri = "";
    // if (inputPath.startsWith("content://")) {
    //   destPath = `${RNFS.TemporaryDirectoryPath}/${name}`;
    //   await RNFS.copyFile(inputPath, destPath);
    //   fileUri = `file://${destPath}`;
    // }
    // const outPutFileName = `${dayjs().unix()}.mp4`;
    // const outputImagePath = `file://${RNFS.CachesDirectoryPath}/${outPutFileName}`;

    // // Execute FFmpeg command to trim the video
    // const trimVideoObj = await FFmpegKit.execute(
    //   `-i ${fileUri} -ss ${startTime} -t ${endTime} -c copy ${outputImagePath}`
    // )
    //   .then(async (s) => {
    //     // Get trimmed file object
    //     const trimmedFile = await RNFS.stat(outputImagePath);
    //     let trimFileObj = {};
    //     if (trimmedFile) {
    //       trimFileObj = {
    //         fileCopyUri: null,
    //         name: outPutFileName,
    //         size: trimmedFile?.size,
    //         type: oldFile?.type,
    //         uri: trimmedFile?.path,
    //         durationInSec: 30,
    //       };
    //     }
    //     return trimFileObj;
    //   })
    //   .catch((err) => {
    //     return oldFile;
    //   });
    // return trimVideoObj;
    return oldFile;
  } catch (error) {
    console.error("Error trimming video:", error);
  }
};

// For Push Notification permission

export const requestForPushNotificationPermission = async () => {
  if (Platform.OS === "android" && Platform.Version >= 23) {
    PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    ).then((result) => {
      if (result) {
        console.log("Permission is OK");
      } else {
        PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        ).then((res) => {
          if (res) {
            console.log("User accept");
          } else {
            console.log("User refuse");
          }
        });
      }
    });
  } else {
    check(PERMISSIONS.IOS.POST_NOTIFICATIONS).then((res) => {
      if (res !== "granted") {
        console.log("permission granted === ");
      } else {
        request(PERMISSIONS.IOS.POST_NOTIFICATIONS).then((res) => {
          console.log("Your Permission is accept");
        });
      }
    });
  }
};

export const handleFollowToggle = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.followHandler}/${id}`,
      "POST"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ followHandlerApi ~ error:", error);
  }
};

export const getUserFollowList = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.followList}/${id}?slug=followed`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.error("🚀 ~ getUserFollowList ~ error:", error);
  }
};

export const getUserFollowingList = async (id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.followList}/${id}`,
      "GET"
    );
    return resp;
  } catch (error) {
    console.log("🚀 ~ getUserFollowingList ~ error:", error);
  }
};

export const onSharePost = async (receiver_id, post_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.sharePost}?receiver_id=${receiver_id}&post_id=${post_id}`,
      "POST"
    );
    if (resp?.data?.success) {
      Toast.show(resp?.data?.message || "Sent");
    } else {
      Toast.show(resp?.data?.message || "Not shared");
    }
  } catch (error) {
    console.log("🚀 ~ getCurrentPlantDetail ~ error:", error);
  }
};

export const getFileTypeFromExtension = (filePath) => {
  const extension = filePath.split(".").pop().toLowerCase();
  switch (extension) {
    case "mp3":
      return "audio/mpeg";
    case "wav":
      return "audio/wav";
    case "m4a":
      return "audio/mp4";
    case "mp4":
      return "audio/mp4";
    case "aac":
      return "audio/aac";
    default:
      return "audio/mp4";
  }
};

const inAppPurchaseForIOS = async product_id => {
  const iosPurchase  = await RNIap.requestPurchase({
    sku: product_id,
  })
    .then(async s => {
      const finish = await RNIap.finishTransaction({
        purchase: s,
        isConsumable: true,
      }).then(l => {
        return s;
      });
      return finish;
    })
    .catch(f => {
      console.log('🚀 ~ donateFun ~ f ios:', f);
    });
  console.log("🚀 ~ inAppPurchaseForIOS ~ iosPurchase:", iosPurchase)

    return iosPurchase
};

const inAppPurchaseForAndroid = async product_id => {
  const availableProduct = await RNIap.getAvailablePurchases();

  if (!isEmpty(availableProduct) && !availableProduct[0]?.autoRenewingAndroid) {
    const androidPurchase = await RNIap.acknowledgePurchaseAndroid({
      token: availableProduct[0].purchaseToken,
    })
      .then(async r => {
        const finishTrans = await RNIap.finishTransaction({
          purchase: availableProduct[0],
          isConsumable: true,
          developerPayloadAndroid: availableProduct[0]?.developerPayloadAndroid
        }).then(async l => {
          const res = await RNIap.requestPurchase({
            skus: [product_id],
          });
          return isArray(res) ? res[0] : res
        });
        return finishTrans;
      })
      .catch(f => {
        console.log('🚀 ~ acknowledgePurchase ~ f:', f);
      });
      return androidPurchase;
  } else {
    const androidPurchase = await RNIap.requestPurchase({
      skus: [product_id],
    }).catch(s => {
      console.log('error while purchase', s);
    });
    return isArray(androidPurchase) ? androidPurchase[0] : androidPurchase
  }
};

export const purchaseProduct = async product_id => {
  console.log('🚀 ~ donateFun ~ product_id:', product_id);
  if (Platform.OS === 'android') {
    return inAppPurchaseForAndroid(product_id); // When user try to purchase from android device at that time this function call
  }
  if (Platform.OS === 'ios') {
    return inAppPurchaseForIOS(product_id); // When user try to purchase from iOS device at that time this function call
  }
};

export const getProducts = async itemsList => {
  try {
    await RNIap.getProducts(itemsList)
      .then(s => {
        console.log('get items list-------', s);
      })
      .catch(d => {
        console.log('error while get items list------', d);
      });
  } catch (err) {
    console.warn(err);
    return [];
  }
};

export function generateRandomId(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
