/* eslint-disable react-hooks/exhaustive-deps */
import "react-native-gesture-handler";

// Import React and Component
import React, { useEffect, useMemo } from "react";
import { Linking, Platform, StatusBar, Text, TextInput } from "react-native";
import { persistor, store } from "./app/redux/store/configureStore";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import codePush from "react-native-code-push";
import { withIAPContext } from "react-native-iap";

// Import Navigators from React Navigation
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";

// Import Screens
import SplashScreen from "./app/screens/SplashScreen/SplashScreen";
import OurPolicy from "./app/screens/OurPolicy";
import { InAppNotificationProvider } from "./app/libs/react-native-in-app-notification";
import { navigationRef } from "./app/navigation/NavigationService";
import { initTranslate } from "./app/lang/Translate";
import { useState } from "react";
import UpdatePopup from "./app/components/UpdatePopUp";
import IntroScreen from "./app/screens/IntroScreen/intro";
import TermsAndCondition from "./app/screens/TermsAndCondition";
import ContactUs from "@screens/ContactUs";
import NetworkLost from "./app/components/NetworkLost";
import NetInfo from "@react-native-community/netinfo";
import SideBar from "./app/navigation/SideBar";
import LoginMainScreen from "@screens/LoginMainScreen";
import EmailWithLogin from "@screens/EmailLoginScreen";
import ForgotOtpScreen from "@screens/OTPScreen";
import CForgotPasswordEmail from "@screens/ForgotPassMainScreen";
import CNewPassWordAfterForgot from "@screens/NewPasswordScreen";
import SignUpWithPersonalDetail from "@screens/SignUpWithPersonalDetail";
import SignUpWithMobileAndEmail from "@screens/SignUpMobileAndEmail";
import SingUpWithCompanyDetail from "@screens/SingUpWithCompanyDetail";
import CMS from "@screens/CMS";
import Bugsnag from "@bugsnag/react-native";
import StoryViewComponent from "@components/StoryComponent/StoryViewComponent";
import AddStoryScreen from "@screens/AddStoryScreen";
import StoryPreview from "@screens/StoryPreviewScreen";
import AddPostScreen from "@screens/AddPostScreen";
import CreatePostForm from "@screens/AddPostScreen/CreatePostForm";
import AddReelScreen from "@screens/AddReelScreen";
import PaymentPlans from "@screens/Payments";
import AddOnPlans from "@screens/AddOnPlans";
import SavedPostList from "@screens/SavedScreen/SavedPostList";
import DraftList from "@screens/DraftScreen/DraftList";
import { GiphySDK } from "@giphy/react-native-sdk";
import BaseSetting from "@config/setting";
import ViewReelForSave from "@screens/ViewReelForSave";
import MessagesInfo from "@screens/Messages/MessagesInfo";
import UserPreviousStory from "@screens/Profile/userPreviousStory";
import UserHighlightPreview from "@screens/Profile/userHighlightPreview";
import IntroPreview from "@screens/Profile/IntroPreview";
import HighlightPreviewScreen from "@screens/Profile/highlightPreviewScreen";
import Profile from "@screens/Profile";
import storeAction from "@redux/reducers/socket/actions";
import CreateBoostPost from "@screens/CreateBoostedPost";
import BoostPostReelPreview from "@screens/BoostPostReelPreivew";
import SearchScreen from "@screens/SearchScreen";
import SearchInput from "@screens/SearchScreen/SearchInput";
import * as RNIap from "react-native-iap";
import BoostedPostInsights from "@screens/BoostedPostInsightis";
import PaymentDetails from "@screens/PaymentHistory/PaymentDetails";
import NotificationScreen from "@screens/Notification";
import CancelCurrentPlan from "@screens/CancelCurrentPlan";
import { isEmpty, isNull } from "lodash-es";
import WebViewScreen from "@screens/WebViewScreen";
import VersionCheck from "react-native-version-check";
import AppUpdateModel from "@components/AppUpdate";
import OpenPurchaseModalComponent from "@components/OpenPurchaseModalComponent";
import SignUpWithFastRegistration from "@screens/SignUpWithFastRegistration";
import SingUpWithFastRegistrationSecond from "@screens/SignUpWithFastRegiestrationSecond";
import CreateGroupChat from "@screens/Chat/createGroup";
import GroupInfo from "@screens/GroupInfo";
import BlockList from "@screens/Chat/blockList";

const Stack = createStackNavigator();

const codePushOptions = {
  // installMode: codePush?.InstallMode.IMMEDIATE,
  checkFrequency: codePush?.CheckFrequency.MANUAL,
  updateDialog: false,
};

GiphySDK.configure({
  apiKey:
    Platform.OS === "ios"
      ? BaseSetting.giphyIosApiKey
      : BaseSetting.giphyAndroidApiKey,
});

Bugsnag.start();

const forFade = ({ current }) => ({
  cardStyle: {
    opacity: current.progress,
  },
});

const Auth = ({ navigation }) => {
  // Stack Navigator for Login and Sign up Screen
  return (
    <Stack.Navigator initialRouteName="LoginMainScreen">
      <Stack.Screen
        name="LoginMainScreen"
        component={LoginMainScreen}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />

      <Stack.Screen
        name="EmailWithLogin"
        component={EmailWithLogin}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="CForgotPasswordEmail"
        component={CForgotPasswordEmail}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="ForgotOtpScreen"
        component={ForgotOtpScreen}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="CNewPassWordAfterForgot"
        component={CNewPassWordAfterForgot}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="SignUpWithMobileAndEmail"
        component={SignUpWithMobileAndEmail}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="SignUpWithPersonalDetail"
        component={SignUpWithPersonalDetail}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="SignUpWithFastRegistration"
        component={SignUpWithFastRegistration}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="SingUpWithFastRegistrationSecond"
        component={SingUpWithFastRegistrationSecond}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="SingUpWithCompanyDetail"
        component={SingUpWithCompanyDetail}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="CMS"
        component={CMS}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="paymentPlan"
        component={PaymentPlans}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="AddOnPlans"
        component={AddOnPlans}
        options={{ cardStyleInterpolator: forFade, headerShown: false }}
      />
      <Stack.Screen
        name="MyPreviousStory"
        component={UserPreviousStory}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="UserHighlightPreview"
        component={UserHighlightPreview}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="IntroPreview"
        component={IntroPreview}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

const App = () => {
  // redux state
  const { socket } = store.getState();
  const [isAppUpdate, setIsAppUpdate] = useState(false);

  // state
  const [state, setState] = useState({
    isCheckUpdate: false,
    processing: false,
    loading: true,
    isMandatory: true,
    message:
      "A new version of the app is available. Update now to access new features and improvements.",
  });
  const [loader, setUpdateLoader] = useState(false);
  const [updateProgress, setUpdateProgress] = useState(0);
  const [isNetWorkConnected, setIsNetWorkConnected] = useState(true);

  // memo
  const isNetWorkConnectedMemo = useMemo(
    () => isNetWorkConnected,
    [isNetWorkConnected]
  );

  // useEffects

  useEffect(() => {
    socket?.socketObj?.disconnect();
    store.dispatch({
      type: storeAction.SET_SOCKET,
      socketObj: null,
    });
  }, []);

  useEffect(() => {
    //Check if any update is available or not
    checkAppVersion();
  }, []);

  // Initialize in-app-purchase
  useEffect(() => {
    // Initialize the module
    RNIap.initConnection()
      .then(async () => {
        console.log("Connected to IAP");
        // return await getSubscriptions({ skus: itemSkus });
      })
      .catch((error) => {
        console.log("Connection to IAP failed:", error);
      });
    return () => {
      // Clean up the module
      RNIap.endConnection();
    };
  }, []);

  useEffect(() => {
    NetInfo.addEventListener((state) => {
      setIsNetWorkConnected(state.isConnected);
    });
  }, []);

  // this function for check app version for update
  const checkAppVersion = async () => {
    try {
      const latestVersion =
        Platform.OS === "ios"
          ? await fetch(
              "https://itunes.apple.com/in/lookup?bundleId=com.footbizz"
            )
              .then((r) => r.json())
              .then((res) => res?.results[0]?.version)
          : await VersionCheck.getLatestVersion({
              provider: "playStore",
              packageName: "com.footbizz",
              ignoreErrors: true,
            });

      const currentVersion = VersionCheck.getCurrentVersion();
      const vCheck = latestVersion > currentVersion;
      if (vCheck) {
        setState({
          ...state,
          isCheckUpdate: false,
          message: "",
        });
        setIsAppUpdate(vCheck);
      } else {
        setIsAppUpdate(false);
        checkUpdate();
      }
    } catch (error) {
      console.error("Error checking app version:", error);
    }
  };

  const checkUpdate = async () => {
    try {
      const update = await codePush?.checkForUpdate();

      if (!isEmpty(update) && !isNull(update)) {
        setState({
          ...state,
          isCheckUpdate: true,
          message: update?.description
            ? update?.description
            : "A new version of the app is available. Update now to access new features and improvements.",
          // isMandatory: update?.isMandatory ? update?.isMandatory : true,
        });
      } else {
        setState({
          ...state,
          isCheckUpdate: false,
        });
      }
    } catch (error) {
      console.log("🚀 ~ checkUpdate ~ error:", error);
    }
  };

  const onBeforeLift = () => {
    initTranslate(store);
    setState({
      ...state,
      loading: false,
    });
  };

  const onAppUpdate = () => {
    Linking.openURL(
      Platform.OS === "ios"
        ? "https://apps.apple.com/in/app/footbizz/id6502042816"
        : "https://play.google.com/store/apps/details?id=com.footbizz"
    );
  };

  return (
    <InAppNotificationProvider>
      <Provider store={store}>
        <PersistGate
          // loading={displaySpinner()}
          persistor={persistor}
          onBeforeLift={onBeforeLift}
        >
          <StatusBar
            backgroundColor="#fff"
            animated
            barStyle="dark-content"
            translucent={false}
          />
          <NavigationContainer ref={navigationRef}>
            <Stack.Navigator initialRouteName="SplashScreen">
              {/* SplashScreen which will come once for 5 Seconds */}
              <Stack.Screen
                name="SplashScreen"
                component={SplashScreen}
                // Hiding header for Splash Screen
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="IntroScreen"
                component={IntroScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              {/* Auth Navigator: Include Login and Signup */}
              <Stack.Screen
                name="Auth"
                component={Auth}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="CMS"
                component={CMS}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="OurPolicy"
                component={OurPolicy}
                options={{
                  cardStyleInterpolator: forFade,
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="CancelCurrentPlan"
                component={CancelCurrentPlan}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="ContactUs"
                component={ContactUs}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="TermsAndCondition"
                component={TermsAndCondition}
                options={{
                  cardStyleInterpolator: forFade,
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="HomeScreen"
                component={SideBar}
                // Hiding header for Navigation Drawer
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="StoryViewComponent"
                component={StoryViewComponent}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="AddStoryScreen"
                component={AddStoryScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="CreateGroupChat"
                component={CreateGroupChat}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="AddPostScreen"
                component={AddPostScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="BoostedPostInsights"
                component={BoostedPostInsights}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="AddReelScreen"
                component={AddReelScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="CreatePostForm"
                component={CreatePostForm}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="CreateBoostPost"
                component={CreateBoostPost}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="BoostPostReelPreview"
                component={BoostPostReelPreview}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="SavedPostList"
                component={SavedPostList}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="DraftList"
                component={DraftList}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />

              <Stack.Screen
                name="ViewReelForSave"
                component={ViewReelForSave}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="ProfileNew"
                component={Profile}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="MessagesInfo"
                component={MessagesInfo}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="SearchScreen"
                component={SearchScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="NotificationScreen"
                component={NotificationScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="PaymentDetails"
                component={PaymentDetails}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />

              <Stack.Screen
                name="SearchInput"
                component={SearchInput}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />

              <Stack.Screen
                name="StoryPreview"
                component={StoryPreview}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="HighLightPreView"
                component={HighlightPreviewScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="WebViewScreen"
                component={WebViewScreen}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="GroupInfo"
                component={GroupInfo}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
              <Stack.Screen
                name="BlockList"
                component={BlockList}
                options={{ cardStyleInterpolator: forFade, headerShown: false }}
              />
            </Stack.Navigator>
          </NavigationContainer>
          <UpdatePopup
            state={state}
            setStat={setState}
            loader={loader}
            setUpdateLoader={setUpdateLoader}
            updateProgress={updateProgress}
            setUpdateProgress={setUpdateProgress}
            isAppUpdate={isAppUpdate}
          />
          <AppUpdateModel
            isVisible={isAppUpdate}
            onUpdate={() => onAppUpdate()}
          />
          <NetworkLost isVisible={!isNetWorkConnectedMemo} />
          <OpenPurchaseModalComponent />
        </PersistGate>
      </Provider>
    </InAppNotificationProvider>
  );
};

if (Text.defaultProps == null) {
  Text.defaultProps = {};
}
Text.defaultProps.allowFontScaling = false;
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false;

let indexExport = App;
if (!__DEV__) {
  indexExport = codePush(codePushOptions)(App);
}
export default withIAPContext(indexExport);
