const { execSync } = require('child_process');

const apiKey = 'd4005f25e15dabbce1276a748f018298';

if (!apiKey) {
  throw new Error('BUGSNAG_API_KEY environment variable is not set.');
}

// Function to build and upload source maps for a given platform
function buildAndUpload(platform) {
  const appVersion = platform === 'ios' ? '1.2' : '1.0.2';
  const sourceMap = platform === 'ios' ? 'ios/main.jsbundle.map' : 'android/main.jsbundle.map';
  const bundle = platform === 'ios' ? 'ios/main.jsbundle' : 'android/main.jsbundle'
  console.log(`Building and uploading source maps for ${apiKey}...`);

  // Build the bundle and generate source maps
  execSync(
    `react-native bundle --platform ${platform} --dev false --entry-file index.js --bundle-output ${bundle} --sourcemap-output ${sourceMap}`,
    { stdio: 'inherit' },
  );

   // Build the curl command
  const curlCommand = `curl --http1.1 https://upload.bugsnag.com/react-native-source-map \
  -F apiKey=${apiKey} \
  -F appVersion=${appVersion} \
  -F dev=false \
  -F platform=${platform} \
  -F sourceMap=@${sourceMap} \
  -F bundle=@${bundle}`;

  // Execute the curl command
  try {
    const result = execSync(curlCommand, { stdio: 'pipe' });
    console.log('🚀 ~ buildAndUpload ~ result:', result.toString());
    console.log(result.toString());
  } catch (error) {
    console.error(
      `Error uploading source maps for ${platform}: ${error.message}`,
    );
    if (error.stdout) {
      console.error(`Output: ${error.stdout.toString()}`);
    }
    if (error.stderr) {
      console.error(`Error Output: ${error.stderr.toString()}`);
    }
    throw error;
  }
}

function buildAndUploadDSYM() {

  // Build the bundle and generate source maps
  execSync(
    `xcodebuild clean -workspace ios/FootBizz.xcworkspace -scheme footBizz -configuration Release`,
    { stdio: 'inherit' },
  );

  execSync(
    `xcodebuild archive -workspace ios/FootBizz.xcworkspace -scheme footBizz -configuration Release -archivePath ios/build/FootBizz.xcarchive -sdk iphoneos`,
    { stdio: 'inherit' },
  );

  // Build the curl command
  const curlCommand = `curl --http1.1 https://upload.bugsnag.com/dsym \
  -F apiKey=${apiKey} \
  -F dsym=@ios/build/FootBizz.xcarchive/dSYMs/FootBizz.app.dSYM/Contents/Resources/DWARF/FootBizz`;

  // Execute the curl command
  try {
    const result = execSync(curlCommand, { stdio: 'pipe' });
    console.log('🚀 ~ buildAndUpload ~ result:', result.toString());
    console.log(result.toString());
  } catch (error) {
    console.error(
      `Error uploading source maps for ${error.message}`,
    );
    if (error.stdout) {
      console.error(`Output: ${error.stdout.toString()}`);
    }
    if (error.stderr) {
      console.error(`Error Output: ${error.stderr.toString()}`);
    }
    throw error;
  }
}

// Build and upload for Android
buildAndUpload('android');

// Build and upload for iOS
buildAndUpload('ios');

//Build and upload iOS DSYM file
buildAndUploadDSYM();


