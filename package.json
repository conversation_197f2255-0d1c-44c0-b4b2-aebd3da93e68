{"name": "footbizz", "version": "0.0.1", "author": "Viral Makwana", "email": "email", "private": true, "license": "Groovyweb", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start --reset-cache", "test": "jest", "release": "yarn update-version && cd ./android && ./gradlew clean && ./gradlew assemblerelease", "debug": "cd ./android && ./gradlew clean && ./gradlew assembledebug", "bundle": "yarn update-version && cd android && ./gradlew clean &&./gradlew bundleRelease", "install-pod": "cd ./ios && pod install", "installdebug": "cd android && ./gradlew clean && ./gradlew app:installdebug", "bugsnag:create-build": "bugsnag-cli create-build", "bugsnag:upload-android": "bugsnag-cli upload react-native-android", "update-version": "node incrementVersion.js", "postinstall": "patch-package", "uploadSourceCode": "node build-and-upload.js"}, "dependencies": {"@bugsnag/react-native": "^7.22.6", "@giphy/react-native-sdk": "^3.2.5", "@hookform/resolvers": "^3.3.4", "@invertase/react-native-apple-authentication": "^2.3.0", "@likashefqet/react-native-image-zoom": "^4.3.0", "@react-native-async-storage/async-storage": "^1.23.0", "@react-native-camera-roll/camera-roll": "^7.5.2", "@react-native-community/geolocation": "^3.2.0", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "^18.9.0", "@react-native-firebase/messaging": "^18.4.0", "@react-native-firebase/perf": "^20.3.0", "@react-native-google-signin/google-signin": "^11.0.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@react-navigation/stack": "^6.3.29", "@twotalltotems/react-native-otp-input": "^1.3.11", "appcenter": "^5.0.1", "appcenter-analytics": "^5.0.1", "appcenter-crashes": "^5.0.1", "axios": "^1.6.8", "better-docs": "^2.7.3", "cldr-compact-number": "^0.4.0", "dayjs": "^1.11.10", "deprecated-react-native-prop-types": "^5.0.0", "google-libphonenumber": "^3.2.34", "i18n-js": "^4.4.3", "jsdoc": "^4.0.2", "lodash-es": "^4.17.21", "lottie-react-native": "^6.7.0", "number-to-words": "^1.2.4", "patch-package": "^8.0.0", "prop-types": "^15.8.1", "react": "18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.51.1", "react-native": "0.73.6", "react-native-audio-recorder-player": "^3.6.7", "react-native-auto-height-image": "^3.2.4", "react-native-chart-kit": "^6.12.0", "react-native-code-push": "^8.2.1", "react-native-config": "^1.5.1", "react-native-convert-ph-asset": "^1.0.3", "react-native-curved-bottom-bar": "^3.2.7", "react-native-date-picker": "^4.4.2", "react-native-document-picker": "^9.1.1", "react-native-element-dropdown": "^2.10.4", "react-native-fast-image": "^8.6.3", "react-native-fbsdk-next": "^13.0.0", "react-native-flexible-grid": "^0.1.9", "react-native-fs": "^2.20.0", "react-native-geocoding": "^0.5.0", "react-native-gesture-handler": "2.16.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "2.4.0", "react-native-google-places-autocomplete": "^2.5.6", "react-native-iap": "12.13.1", "react-native-image-crop-picker": "^0.41.1", "react-native-in-app-notification": "^3.2.0", "react-native-in-app-review": "^4.3.3", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.0.6", "react-native-modal": "^13.0.1", "react-native-otp-textinput": "^1.1.5", "react-native-permissions": "^4.1.4", "react-native-popover-view": "^5.1.8", "react-native-progress": "^5.0.1", "react-native-ratings": "^8.1.0", "react-native-reanimated": "3.8.1", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "3.29.0", "react-native-shimmer-placeholder": "^2.0.9", "react-native-simple-toast": "^3.3.0", "react-native-slider": "^0.11.0", "react-native-sound": "^0.11.2", "react-native-status-bar-height": "^2.6.0", "react-native-svg": "^15.3.0", "react-native-swiper-flatlist": "^3.2.4", "react-native-track-player": "^4.1.1", "react-native-vector-icons": "^10.0.3", "react-native-version-check": "^3.4.7", "react-native-video": "^5.2.1", "react-native-video-controls": "^2.8.1", "react-native-vision-camera": "^3.9.2", "react-native-webview": "^13.8.1", "react-redux": "^9.1.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "rn-emoji-keyboard": "^1.7.0", "rn-fetch-blob": "^0.12.0", "socket.io-client": "^4.7.5", "use-count-up": "^3.0.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-arrow-functions": "^7.24.1", "@babel/plugin-transform-shorthand-properties": "^7.24.1", "@babel/plugin-transform-template-literals": "^7.24.1", "@babel/preset-env": "^7.24.1", "@babel/runtime": "^7.24.1", "@bugsnag/source-maps": "^2.3.2", "@react-native-community/eslint-config": "^3.2.0", "@react-native/eslint-config": "0.75.0-main", "@react-native/metro-config": "0.75.0-main", "@tsconfig/react-native": "^3.0.3", "@types/react": "^18.2.67", "@types/react-test-renderer": "^18.0.7", "babel-jest": "^29.7.0", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "cross-env": "^7.0.3", "eslint": "^8.57.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "0.77.0", "prettier": "^3.2.5", "react-native-bundle-visualizer": "^3.1.3", "react-test-renderer": "18.2.0", "typescript": "5.4.2"}, "engines": {"node": ">=16"}, "jest": {"preset": "react-native"}}