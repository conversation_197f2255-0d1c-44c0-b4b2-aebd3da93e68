module.exports = {
  presets: ["module:metro-react-native-babel-preset"],
  plugins: [
    "react-native-reanimated/plugin",
    ["transform-remove-console", { exclude: ["log", "error", "warn"] }],
    [
      "module-resolver",
      {
        root: ["./app"],
        alias: {
          "@config": ["./app/config"],
          "@config/*": ["./app/config/*"],
          "@components": ["./app/components"],
          "@components/*": ["./app/components/*"],
          "@assets": ["./app/assets"],
          "@assets/*": ["./app/assets/*"],
          "@helpers": ["./app/helpers"],
          "@helpers/*": ["./app/helpers/*"],
          "@redux": ["./app/redux"],
          "@redux/*": ["./app/redux/*"],
          "@screens": ["./app/screens"],
          "@screens/*": ["./app/screens/*"],
          "@navigation": ["./app/navigation"],
          "@navigation/*": ["./app/navigation/*"],
          "@language/": ["./app/lang/"],
          "@language/*": ["./app/lang/*"],
          "@app": ["./app"],
          "@app/*": ["./app/*"],
        },
      },
    ],
  ],
};
